syntax = "proto2";

option java_package = "com.volcengine.cloudfs.proto";
option java_outer_classname = "PlacementDriverProtocolProtos";
option java_generic_services = true;
option java_generate_equals_and_hash = true;
option cc_generic_services = true;
package cloudfs.namenode;

import "hdfs.proto";
import "ClientNamenodeProtocol.proto";

message ChooseTargetRequestProto {
  required string writer = 1;
  required int32 storageType = 2;
  required int32 numReplica = 3;
  required fixed32 blockSize = 4;
  required bool isRecover = 5;

  repeated string favored = 6;
  repeated string excluded = 7;

  // no valid response when report is missing, if specified
  optional uint32 namespaceId = 8;
  optional string policyName = 9;

  optional string srcpath = 10;
}

message ChooseTargetResponseProto {
  enum Status {
    OK = 0;
    OK_STALE = 1;  // last received datanode report is present but stale
    MissingDatanodeReport = 2;
    NoEnoughReplicas = 3;
  }
  required Status status = 1;
  repeated string chosenNodes = 2;
  optional string debugMessage = 3;
}

message ReportDatanodesRequestProto {
  required uint32 namespaceId = 1;
  repeated DatanodeStorageReportProto reports = 2;
}

message ReportDatanodesResponseProto {
}

/**
 * Placement driver manages a global of view datanodes across federation.
 */
service PlacementDriverService {
  // all 'active' namenode should regularly report their status of datanodes to each placement driver
  rpc reportDatanodes(ReportDatanodesRequestProto)
      returns (ReportDatanodesResponseProto);

  // presense of datanode report is required before calling 'chooseTarget'
  rpc chooseTarget(ChooseTargetRequestProto) returns(ChooseTargetResponseProto);
}
