#!/usr/bin/python
# coding=utf-8

import sys
sys.path.insert(0, '/opt/tiger/hdfs_deploy/tools/hdfs/lark_util')
from lark_util import send_message
from lark_util import BOT_XIAO_H_TOKEN
import commands

def check_disk(data_dir="data01"):
    disk = commands.getstatusoutput("df -h |awk '/%s/{print $1}'|awk -F/ '{print $NF}'|grep -Eo '[a-z]*'" % data_dir)[1]
    roat = commands.getstatusoutput("lsblk -b -P -o NAME,ROTA|grep '{}'".format(disk))[1].split("\"")[-2]
    disk_type = None
    if roat == "0":
        return "{}: /dev/{} SSD".format(data_dir, disk)
    elif roat == "1":
        return "{}: /dev/{} HDD".format(data_dir, disk)

def main():
    ip = commands.getstatusoutput('/bin/hostname -i')[1]
    lh = commands.getstatusoutput('/opt/tiger/ss_bin/lh -s')[1]

    if 'inf.hadoop.nn.test' in lh or 'inf.dancenn.test' in lh:
	return 

    if sys.argv[1] == "DanceNN":
        data_dir = "data01"
        if lh.find('inf.hadoop.observer') != -1:
            data_dir = "data00"
        disk_type = check_disk(data_dir=data_dir)
        send_message(BOT_XIAO_H_TOKEN, '6563418111813353736', 'restart {}:\n'.format(sys.argv[1]) + ip + '\n' + disk_type + '\n' + lh)
    else:
        send_message(BOT_XIAO_H_TOKEN, '6563418111813353736', 'restart {}:\n'.format(sys.argv[1]) + ip + '\n' + lh)

if __name__ == '__main__':
    main()
