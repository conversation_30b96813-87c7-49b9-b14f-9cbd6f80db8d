<?xml version="1.0"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at
      http://www.apache.org/licenses/LICENSE-2.0
  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<?xml-stylesheet type="text/xsl" href="configuration.xsl"?>
<configuration xmlns:xi="http://www.w3.org/2001/XInclude">
  <property>
    <name>ranger.plugin.hdfs.service.name</name>
    <value>cfs</value>
    <description>
      Name of the Ranger service containing policies for this YARN instance
    </description>
  </property>

  <property>
    <name>ranger.plugin.hdfs.policy.source.impl</name>
    <value>org.apache.ranger.admin.client.RangerAdminRESTClient</value>
    <description>
      Class to retrieve policies from the source
    </description>
  </property>

  <property>
    <name>ranger.plugin.hdfs.policy.rest.url</name>
    <value>{{ RANGER_CONF["RANGER_REST_URL"] }}</value>
    <description>
      URL to Ranger Admin
    </description>
  </property>

  <property>
    <name>ranger.plugin.hdfs.policy.pollIntervalMs</name>
    <value>30000</value>
    <description>
      How often to poll for changes in policies?
    </description>
  </property>

  <property>
    <name>ranger.plugin.hdfs.policy.cache.dir</name>
    <value>/tmp/policycache</value>
    <description>
      Directory where Ranger policies are cached after successful retrieval from the source
    </description>
  </property>

  <property>
    <name>ranger.plugin.hdfs.policy.rest.client.connection.timeoutMs</name>
    <value>120000</value>
    <description>
      Hdfs Plugin RangerRestClient Connection Timeout in Milli Seconds
    </description>
  </property>

  <property>
    <name>ranger.plugin.hdfs.policy.rest.client.read.timeoutMs</name>
    <value>30000</value>
    <description>
      Hdfs Plugin RangerRestClient read Timeout in Milli Seconds
    </description>
  </property>

  <!--  The following fields are used to customize the audit logging feature -->
  <!--
  <property>
    <name>xasecure.auditlog.xasecureAcl.name</name>
    <value>ranger-acl</value>
    <description>
      The module name listed in the auditlog when the permission check is done by RangerACL
    </description>
  </property>
  <property>
    <name>xasecure.auditlog.hadoopAcl.name</name>
    <value>hadoop-acl</value>
    <description>
      The module name listed in the auditlog when the permission check is done by HadoopACL
    </description>
  </property>
  <property>
    <name>xasecure.auditlog.hdfs.excludeusers</name>
    <value>hbase,hive</value>
    <description>
      List of comma separated users for whom the audit log is not written
    </description>
  </property>
  -->

  <property>
    <name>xasecure.add-hadoop-authorization</name>
    <value>false</value>
    <description>
      Enable/Disable the default hadoop authorization (based on
      rwxrwxrwx permission on the resource) if Ranger Authorization fails.
    </description>
  </property>
</configuration>
