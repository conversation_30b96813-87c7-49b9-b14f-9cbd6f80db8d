<?xml version="1.0"?>
<?xml-stylesheet type="text/xsl" href="configuration.xsl"?>
<configuration xmlns:xi="http://www.w3.org/2001/XInclude">
	<property>
		<name>xasecure.audit.is.enabled</name>
		<value>true</value>
	</property>
	<property>
		<name>xasecure.audit.default.async.max.queue.size</name>
		<value>10000</value>
	</property>
	<property>
		<name>xasecure.audit.default.async.max.flush.interval.ms</name>
		<value>5000</value>
	</property>
	<property>
		<name>xasecure.audit.provider.summary.enabled</name>
		<value>false</value>
	</property>
	<property>
		<name>xasecure.audit.destination.http</name>
		<value>true</value>
	</property>
	<property>
		<name>xasecure.audit.destination.log4j</name>
		<value>true</value>
	</property>
	<property>
		<name>xasecure.audit.destination.log4j.logger</name>
		<value>ranger-audit</value>
	</property>
	<property>
		<name>xasecure.audit.destination.http.urls</name>
		<value>http://gas-log-proxy.openstudio.svc.cluster.local/api/logs</value>
	</property>
	<property>
		<name>xasecure.audit.destination.http.topic</name>
		<value>openstudio-audit-logs</value>
	</property>
	<property>
		<name>ranger.audit.source.type</name>
		<value>http</value>
	</property>
	<property>
		<name>ranger.audit.accountId</name>
		<value>**********</value>
		<description />
	</property>
</configuration>
