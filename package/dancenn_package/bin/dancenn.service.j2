[Unit]
Description={{ R_DISPATCHED_CONF["NS_NAME"] }}.dancenn
# Give up restarting your service if it fails to start
# more than 5 times within a 3 minutes interval.
StartLimitBurst=5
StartLimitIntervalSec=180

[Service]
Type=simple
# Don't set User and Group properties, otherwise systemd will complains:
# Failed at step GROUP spawning.
Restart=always
RestartSec=1
WorkingDirectory={{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}
Environment=LD_LIBRARY_PATH=/opt/tiger/jdk/jdk1.8/jre/lib/amd64:/opt/tiger/jdk/jdk1.8/jre/lib/amd64/server
Environment=SEC_TOKEN_PATH=/opt/tiger/zti-svid-loader/data/identity.token
Environment=CPP_HDFS_ENABLE_IO_MONITOR=true
Environment=CPP_HDFS_IO_MONITOR_DATABUS_CHANNEL=inf.hdfs.cpp.client.iomonitor
Environment=CPP_HDFS_ENABLE_PATH_TRACE=true
Environment=CPP_HDFS_PATH_TRACE_DATABUS_CHANNEL=inf.hdfs.cpp.client.iomonitor.path.trace
Environment=CPP_HDFS_TASK_FAMILY={{ R_DISPATCHED_CONF["CFS_CLUSTER"] }}
Environment=CPP_HDFS_TASK_ID={{ R_DISPATCHED_CONF["NS_NAME"] }}
Environment=CPP_HDFS_CONF=/opt/tiger/hdfs_client/conf/celer_cn/core-site.xml:/opt/tiger/hdfs_client/conf/celer_cn/hdfs-site.xml
Environment=CPP_HDFS_LOG_LEVEL=warn
Environment=CPP_HDFS_LOG_TYPE=stderr
Environment=CPP_HDFS_NN_CONSUL_MODE=off

ExecStart={{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_dancenn_deploy/bin/real_run.sh

[Install]
WantedBy=default.target
