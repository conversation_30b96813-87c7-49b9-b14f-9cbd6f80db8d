#!/bin/bash

set -e -o pipefail
ulimit -c 500000000000
bin_dir="$(cd $(dirname ${BASH_SOURCE:-$0}); pwd)"
base_dir="$(cd ${bin_dir}/../; pwd)"
conf_dir="${base_dir}/conf"

{% if 'MY_POD_NAME' in R_DISPATCHED_CONF %}
export MY_POD_NAME='{{ R_DISPATCHED_CONF["MY_POD_NAME"] }}'
{% endif %}

# bvc
if ! grep -q "bvc_version" "${conf_dir}/dancenn_flags.conf"
then
  bvc_version=$(cat ${base_dir}/current_revision | grep 'version' | awk -F':' '{print $NF}')

  echo -e "--bvc_version=$bvc_version" >> ${conf_dir}/dancenn_flags.conf
fi

log_dir='{{ R_DISPATCHED_CONF["DANCENN_LOG_DIR"] }}/{{ MY_POD_NAME.split("-") | last }}'
mkdir -p "${log_dir}"

stdout_file="${log_dir}/stdout_$(date '+%Y-%m-%d:%H:%M:%S').log"

export JAVA_HOME=/opt/tiger/jdk/jdk1.8
export LD_LIBRARY_PATH="${base_dir}/lib:$LD_LIBRARY_PATH"
exec "${bin_dir}/dancenn" --flagfile="${conf_dir}/dancenn_flags.conf" &>> "${stdout_file}"
