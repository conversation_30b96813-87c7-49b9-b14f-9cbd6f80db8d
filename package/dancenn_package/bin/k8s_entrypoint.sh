#!/bin/bash

set -eo pipefail
trap "echo 'error: <PERSON><PERSON><PERSON> failed: see failed command above'" ERR

# XXX path to network_mapping conf file is hard-coded inside BK jar, prepare it here
mkdir -p /opt/tiger/dancenn_deploy/conf_common
ln -sf /opt/tiger/cfs_dancenn_deploy/conf/network_mapping.conf /opt/tiger/dancenn_deploy/conf_common/network_location_v2.conf

/opt/tiger/cfs_hdfs_deploy/hadoop/svc/zkfc_run/render.sh file:///conf/dispatched-conf.json
/opt/tiger/cfs_dancenn_deploy/bin/render.sh file:///conf/dispatched-conf.json
chmod u+x /opt/tiger/cfs_dancenn_deploy/bin/real_run.sh
exec /opt/tiger/cfs_dancenn_deploy/bin/real_run.sh
