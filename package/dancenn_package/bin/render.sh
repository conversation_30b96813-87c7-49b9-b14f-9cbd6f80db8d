#!/bin/bash

set -x -e -o pipefail

if [[ $# -ne 1 ]]
then
  echo "Usage:"
  echo "$0 http://************:8484/api/v1/items/cloudfs.config.nn-test?version=2"
  echo "$0 file:///opt/tiger/cfs-namespace-conf/config-dispatched-sdvfsa-root-18014398509481985.json"
  exit 1
fi

current_dir="$(cd $(dirname ${BASH_SOURCE:-$0}); pwd)"
conf_file=
if [[ $1 == http://* ]]
then
  conf_file="${current_dir}/config-dispatched.json"
  curl "$1" -o "${conf_file}"
elif [[ $1 == file://* ]]
then
  conf_file=${1#"file://"}
else
  echo "$1 is not started with http or file."
  exit 1
fi
function get_conf {
  echo `python -c "import json; print(json.load(open(\"$conf_file\"))[\"$1\"])"`
}

if [[ -z "$MY_POD_NAME" ]]; then
    export MY_POD_NAME="$(get_conf 'MY_POD_NAME')"
    echo "use MY_POD_NAME from config file"
fi
deploy_dir="$(get_conf 'DEPLOY_DIR')"
echo "$deploy_dir"
cfs_dancenn_dir="${deploy_dir}/cfs_dancenn_deploy"
cfs_dancenn_conf_dir="${cfs_dancenn_dir}/conf"
cfs_dancenn_bin_dir="${cfs_dancenn_dir}/bin"
if [[ ${cfs_dancenn_bin_dir} != ${current_dir} ]]
then
  echo "[ERROR] cfs_dancenn_bin_dir is ${cfs_dancenn_bin_dir}, render.sh is in ${current_dir}"
  exit 1
fi

export R_DISPATCHED_CONF="jsonfile:${conf_file}"
if [[ -z "$MY_POD_NAME" ]]; then
    export MY_POD_NAME="$(get_conf 'MY_POD_NAME')"
    echo "use MY_POD_NAME from config file"
fi
cd "${cfs_dancenn_conf_dir}" && e2j2
cd "${cfs_dancenn_bin_dir}" && e2j2

bootstrap_active_nn_ip=`python -c "import json; conf = json.load(open('${conf_file}')); print(filter(lambda x: x['BOOTSTRAP_AS_ACTIVE'], conf['NNS'])[0]['HOST_IP'])"`
if [[ $? -ne 0 ]]
then
  echo "[ERROR] Failed to get bootstrap active nn."
  exit 1
fi
echo "${bootstrap_active_nn_ip}" > "${current_dir}/bootstrap_active_nn_ip"
