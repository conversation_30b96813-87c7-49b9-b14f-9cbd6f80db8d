#!/bin/bash

set -x -e -o pipefail

current_dir="$(cd $(dirname ${BASH_SOURCE:-$0}); pwd)"
deploy_dir="{{ R_DISPATCHED_CONF['DEPLOY_DIR'] }}"
cfs_hdfs_dir="${deploy_dir}/cfs_hdfs_deploy"
cfs_dancenn_dir="${deploy_dir}/cfs_dancenn_deploy"
rocksdb_dir="{{ R_DISPATCHED_CONF['DANCENN_DATA_DIR'] }}/rocksdb"

{% if R_DISPATCHED_CONF["PREFER_IPV4_OPTION"] is defined and R_DISPATCHED_CONF["PREFER_IPV4_OPTION"] == "False" %}
export PREFER_IPV4_OPTION={{ R_DISPATCHED_CONF["PREFER_IPV4_OPTION"] | default('-') }}
{% endif %}

if [  -d "${rocksdb_dir}" ]
then
  echo "[INFO] RocksDB exists, no need to boostrap."
  exit 0
fi

bootstrap_active_nn_ip=`cat "${current_dir}/bootstrap_active_nn_ip"`

timestamp=`date +%s`
ckpt_dir="{{ R_DISPATCHED_CONF['DANCENN_DATA_DIR'] }}/checkpoint_for_bak_${timestamp}"

if [[ $bootstrap_active_nn_ip =~ ^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$ ]]; then
  curl -6 "http://[${bootstrap_active_nn_ip}]:{{ R_DISPATCHED_CONF['NN_HTTP_PORT'] }}/admin?cmd=checkpoint&path=${ckpt_dir}"
  mkdir -p "${rocksdb_dir}"
  wget -6 --recursive --no-host-directories --no-parent --cut-dirs=100 --directory-prefix="${rocksdb_dir}" \
  "http://[${bootstrap_active_nn_ip}]:{{ R_DISPATCHED_CONF['SHARINNGANN_PORT'] }}/${ckpt_dir#'/'}"
else
  curl "http://${bootstrap_active_nn_ip}:{{ R_DISPATCHED_CONF['NN_HTTP_PORT'] }}/admin?cmd=checkpoint&path=${ckpt_dir}"
  mkdir -p "${rocksdb_dir}"
  wget --recursive --no-host-directories --no-parent --cut-dirs=100 --directory-prefix="${rocksdb_dir}" \
  "http://${bootstrap_active_nn_ip}:{{ R_DISPATCHED_CONF['SHARINNGANN_PORT'] }}/${ckpt_dir#'/'}"
fi
