import http.server
import os
import signal
import socket
import sys

DEFAULT_LIMITED_PATH = "/data00/namespaces"
os.chdir('/')


class StoppableHTTPServer(http.server.HTTPServer):
  address_family = socket.AF_INET6

  def run(self):
    try:
      self.serve_forever()
    except KeyboardInterrupt:
      pass
    finally:
      print("Stopping gracefully...")
      # Clean-up server (close socket, etc.)
      self.server_close()
      self.shutdown()


def sigterm_handler(_signo, _stack_frame):
  sys.exit(0)


if __name__ == "__main__":
  signal.signal(signal.SIGTERM, sigterm_handler)

  limited_path = DEFAULT_LIMITED_PATH
  if len(sys.argv) >= 2:
    limited_path = sys.argv[1]
  if len(sys.argv) >= 3:
    port = int(sys.argv[2])
  else:
    port = 8090

  class SafeHTTPHandler(http.server.SimpleHTTPRequestHandler):

    def translate_path(self, path):
      path = http.server.SimpleHTTPRequestHandler.translate_path(self, path)
      path = os.path.abspath(os.path.expanduser(os.path.expandvars(path)))
      print("Request path is {}".format(path))
      if not path.startswith(limited_path):
        path = "{}/not-existed/{}".format(limited_path, path)
      print("Final path is {}".format(path))
      return path

  server_address = ('', port)
  httpd = StoppableHTTPServer(server_address, SafeHTTPHandler)
  httpd.run()
