#!/usr/bin/python3

import sys

from kazoo.client import KazooClient


class Mover(object):

    def __init__(self, hosts, src_path, dst_path):
        self._client = KazooClient(hosts=hosts)
        self._client.start()
        if not self._client.exists(src_path):
            raise Exception("{} is not existed in {}.".format(src_path, hosts))
        self._src_path = src_path
        if self._client.exists(dst_path):
            raise Exception("{} is existed in {}".format(dst_path, hosts))
        self._dst_path = dst_path

    def __del__(self):
        self._client.stop()

    def execute(self):
        self._client.create(self._dst_path)
        self._cp(self._src_path, self._dst_path)
        self._client.delete(self._src_path, recursive=True)

    def _cp(self, src_path, dst_path):
        self._client.set(dst_path, self._client.get(src_path)[0])
        for sub_path in self._client.get_children(src_path):
            src_sub_path = "{}/{}".format(src_path, sub_path)
            dst_sub_path = "{}/{}".format(dst_path, sub_path)
            self._client.create(dst_sub_path)
            self._cp(src_sub_path, dst_sub_path)


if __name__ == "__main__":
    if len(sys.argv) != 4:
        print("Usage: {} <hosts> <src_path> <dst_path>.".format(sys.argv[0]))
        print("hosts: Comma-separated list of hosts to connect to "
              "(e.g. 127.0.0.1:2181,127.0.0.1:2182,[::1]:2183).")
        sys.exit(-1)
    Mover(sys.argv[1], sys.argv[2], sys.argv[3]).execute()
