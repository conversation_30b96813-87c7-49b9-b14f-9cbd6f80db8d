#!/bin/bash

cleanup() {
    echo "Cleaning up test files and directory..."
    rm -f ${TEST_DIR}/test.* *_result.json
    rm -rf ${TEST_DIR}
    echo "Cleanup completed."
}

# 在脚本退出时执行清理
trap cleanup EXIT

# 检查并安装依赖
check_and_install_deps() {
    local deps=("fio" "jq" "bc")
    for dep in "${deps[@]}"; do
        if ! command -v $dep &> /dev/null; then
            echo "$dep not found, installing..."
            if command -v apt-get &> /dev/null; then
                sudo apt-get update && sudo apt-get install -y $dep
            elif command -v yum &> /dev/null; then
                sudo yum install -y $dep
            else
                echo "Unable to install $dep. Please install it manually."
                exit 1
            fi
        fi
    done
}

# 安装依赖
check_and_install_deps

# 测试目录
TEST_DIR="/data00/disk_test"
mkdir -p ${TEST_DIR}

# 清理缓存
sync
echo 3 > /proc/sys/vm/drop_caches

run_fio_test() {
    local test_name=$1
    local rw_type=$2
    local bs=$3
    local iodepth=$4
    local size=$5

    echo "Running $test_name test..."
    fio --name=$test_name \
        --directory=${TEST_DIR} \
        --rw=${rw_type} \
        --bs=${bs} \
        --direct=1 \
        --iodepth=${iodepth} \
        --runtime=30 \
        --numjobs=4 \
        --time_based \
        --group_reporting \
        --size=${size} \
        --filename_format='test.$jobnum' \
        --ioengine=libaio \
        --output=${test_name}_result.json \
        --output-format=json
        
    # Check if fio command was successful
    if [ $? -ne 0 ]; then
        echo "Error: fio test $test_name failed"
        return 1
    fi
    
    # Verify the output file exists and is not empty
    if [ ! -f "${test_name}_result.json" ] || [ ! -s "${test_name}_result.json" ]; then
        echo "Error: fio did not generate a valid output file for $test_name"
        return 1
    fi
}

# 运行测试
echo "Starting disk performance tests..."

# 4K随机读写IOPS测试
run_fio_test "random_read_iops" "randread" "4k" "32" "1G"
run_fio_test "random_write_iops" "randwrite" "4k" "32" "1G"

# 1M顺序读写吞吐量测试
run_fio_test "sequential_read_throughput" "read" "1m" "32" "1G"
run_fio_test "sequential_write_throughput" "write" "1m" "32" "1G"

# 解析结果
parse_results() {
    local file=$1
    local test_type=$(echo $file | sed 's/_result.json//')
    
    # Check if the file exists
    if [ ! -f "${file}" ]; then
        echo "Error: Result file ${file} not found"
        return 1
    fi
    
    # Check if the file is empty
    if [ ! -s "${file}" ]; then
        echo "Error: Result file ${file} is empty"
        return 1
    fi
    
    # Check if the file contains valid JSON
    if ! jq empty "${file}" 2>/dev/null; then
        echo "Error: Result file ${file} does not contain valid JSON"
        return 1
    fi

    if [[ $test_type == *"read"* ]]; then
        # Use -e flag to exit with 0 if expression is valid, and safely handle errors
        local iops=$(jq -e '.jobs[0].read.iops // 0' "${file}" 2>/dev/null || echo 0)
        local bw_kb=$(jq -e '.jobs[0].read.bw // 0' "${file}" 2>/dev/null || echo 0)
        # 延迟统计（单位：微秒）
        local lat_min=$(jq -e '.jobs[0].read.lat_ns.min // 0 | . / 1000' "${file}" 2>/dev/null || echo 0)
        local lat_max=$(jq -e '.jobs[0].read.lat_ns.max // 0 | . / 1000' "${file}" 2>/dev/null || echo 0)
        local lat_mean=$(jq -e '.jobs[0].read.lat_ns.mean // 0 | . / 1000' "${file}" 2>/dev/null || echo 0)
        local lat_p99=$(jq -e '.jobs[0].read.clat_ns.percentile."99.000000" // 0 | . / 1000' "${file}" 2>/dev/null || echo 0)
    else
        local iops=$(jq -e '.jobs[0].write.iops // 0' "${file}" 2>/dev/null || echo 0)
        local bw_kb=$(jq -e '.jobs[0].write.bw // 0' "${file}" 2>/dev/null || echo 0)
        # 延迟统计（单位：微秒）
        local lat_min=$(jq -e '.jobs[0].write.lat_ns.min // 0 | . / 1000' "${file}" 2>/dev/null || echo 0)
        local lat_max=$(jq -e '.jobs[0].write.lat_ns.max // 0 | . / 1000' "${file}" 2>/dev/null || echo 0)
        local lat_mean=$(jq -e '.jobs[0].write.lat_ns.mean // 0 | . / 1000' "${file}" 2>/dev/null || echo 0)
        local lat_p99=$(jq -e '.jobs[0].write.clat_ns.percentile."99.000000" // 0 | . / 1000' "${file}" 2>/dev/null || echo 0)
    fi

    # Check if values are valid numbers before using bc
    if [[ "$bw_kb" =~ ^[0-9]+(\.[0-9]+)?$ ]]; then
        local bw_mb=$(echo "scale=2; $bw_kb / 1024" | bc)
    else
        local bw_mb=0
    fi

    # 格式化延迟数值（保留2位小数）
    if [[ "$lat_min" =~ ^[0-9]+(\.[0-9]+)?$ ]]; then
        lat_min=$(echo "scale=2; $lat_min" | bc)
    else
        lat_min=0
    fi
    
    if [[ "$lat_max" =~ ^[0-9]+(\.[0-9]+)?$ ]]; then
        lat_max=$(echo "scale=2; $lat_max" | bc)
    else
        lat_max=0
    fi
    
    if [[ "$lat_mean" =~ ^[0-9]+(\.[0-9]+)?$ ]]; then
        lat_mean=$(echo "scale=2; $lat_mean" | bc)
    else
        lat_mean=0
    fi
    
    if [[ "$lat_p99" =~ ^[0-9]+(\.[0-9]+)?$ ]]; then
        lat_p99=$(echo "scale=2; $lat_p99" | bc)
    else
        lat_p99=0
    fi

    echo "IOPS: ${iops:-0}, Throughput: ${bw_mb:-0} MB/s"
    echo "Latency (μs) - Min: ${lat_min:-0}, Avg: ${lat_mean:-0}, Max: ${lat_max:-0}, P99: ${lat_p99:-0}"
}

echo -e "\nTest Results:"
echo "=== Random Read ==="
parse_results random_read_iops_result.json || echo "Failed to parse random read results"
echo -e "\n=== Random Write ==="
parse_results random_write_iops_result.json || echo "Failed to parse random write results"
echo -e "\n=== Sequential Read ==="
parse_results sequential_read_throughput_result.json || echo "Failed to parse sequential read results"
echo -e "\n=== Sequential Write ==="
parse_results sequential_write_throughput_result.json || echo "Failed to parse sequential write results"
