#!/bin/bash
export LANG=en_US.UTF-8
DANCEREPLAY_HOME=`dirname $0`
HADOOP_HOME=/opt/tiger/hdfs_deploy/hadoop-2.6.0-cdh5.4.4

DEFAULT_LIBEXEC_DIR=$HADOOP_HOME/libexec
HADOOP_LIBEXEC_DIR=${HADOOP_LIBEXEC_DIR:-$DEFAULT_LIBEXEC_DIR}
. $HADOOP_LIBEXEC_DIR/hdfs-config.sh

ulimit -c 10485760
export HADOOP_OPTS="$HADOOP_OPTS -Xms20g -Xmx20g -XX:NewSize=6g -XX:SurvivorRatio=4 -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=65211"

rm -rf /var/log/tiger/dancereplay.log

HADOOP_ROOT_LOGGER=INFO,console exec $HADOOP_HOME/bin/hadoop jar $DANCEREPLAY_HOME/*.jar -Dhadoop.root.logger=INFO,console "$@" 1>>/var/log/tiger/dancereplay.log 2>&1
