# -*- coding: utf-8 -*-

"""
A library for emitting and collecting metrics.

The python library is actually a proxy to metrics library at cpputil::metrics.

The python library additionally support emitting metrics in multi-process
programs. It creates a multiprocessing.Queue at initialization. All emitted
metric data are put into the queue, while another stand-alone event_loop
process consumes the queue and send data to the real MetricCollector.

In single-process programs it works in the same way with multi-process
programs. So there will always be one more extra process.

Be aware that always call init() and start() methods before creating any
other sub-processes.


Code example:
    import pyutil.program.metrics as metrics
    import pyutil.program.conf

    conf_file = pyutil.program.conf.Conf('test_metrics.conf')

    # init metrics library.
    metrics.init(conf_file)
    metrics.define_counter('throughput', 'req');
    metrics.define_timer('latency', 'us');
    metrics.define_store('cpu_usage', 'us');

    # emit data anywhere you want.
    metrics.emit_counter('throughput', 1)
    metrics.emit_timer('latency', elapsed_time)
    metrics.emit_store('cpu_usage', cpu_usage)

    # use Timer
    with metrics.Timer('latency'):
        do_something()

    # use timing decorator
    @metrics.timing('latency'):
    def do_something():
        work()

Conf example:
    # 后端类型，支持stdout,file,ganglia,opentsdb.
    # 多个后端间逗号分隔. 默认为stdout
    metrics_enabled_backends: stdout

    # 本地日志文件名，使用绝对路径
    metrics_backend_file_path_name: /var/log/tiger/example.metrics.log

    # ganglia服务端点，多个端点间逗号分隔
    metrics_backend_ganglia_endpoints: \
    service-m0.gmond.d.byted.org:8600, service-m1.gmond.d.byted.org:8600

    # OpenTSDB服务端点，多个端点间逗号分隔
    metrics_backend_opentsdb_endpoints: 192.168.20.41:8400

    # 汇报周期，单位为秒，默认值为10秒。
    # 约定同一个服务所有metrics使用统一的flush周期，
    # 暂不支持为每个metric设置不同的flush周期
    metrics_flush_interval: 15

    # 查询监听端口.
    metrics_listening_port: 10086

    # 定义本服务所有metric的公共前缀。
    # 所有api中name参数可以省略该前缀。默认为空
    metrics_namespace_prefix: test


"""

import functools
import re
import socket
import sys
import threading
import time
import traceback
from collections import defaultdict
from os import environ

import msgpack

'''
message format:
    message_type(int16): define_metric, define_alarm, emit_metric, reset_metric
    message_body: ...
        define_metric:
'''

all_metrics = {}
namespace_prefix = None
default_alarm_recipients = None
default_psm = ""
send_batch_size = 1
udp_socket = socket.socket(socket.AF_UNIX, socket.SOCK_DGRAM)
udp_socket.setblocking(0)
server_address = '/opt/tmp/sock/metric.sock'
use_remote = False
remote_addr = ''
remote_port = 0
remote_udp_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
reqs_buffer = threading.local()
reqs_buffer.val = []
podtag_blacklist = ['live.web.api']


def init(conf_obj):
    global default_psm
    global namespace_prefix
    global default_alarm_recipients
    global send_batch_size
    global use_remote

    if conf_obj.get('metrics_namespace_prefix'):
        namespace_prefix = conf_obj.get('metrics_namespace_prefix')
    if conf_obj.get('use_remote') == True:
        use_remote = True
        global remote_addr, remote_port
        remote_addr = conf_obj.get('remote_addr')
        remote_port = conf_obj.get('remote_port')
    default_alarm_recipients = conf_obj.get('metrics_default_alarm_recipients')
    send_batch_size = int(conf_obj.get('send_batch_size', 1))
    default_psm = environ.get("LOAD_SERVICE_PSM")
    if not default_psm:
        default_psm = conf_obj.get("psm", "")

    import atexit  # import here to protect from race with monkey patch

    atexit.register(lambda: _flush_pending())


def _c(metric_name, prefix):
    global namespace_prefix
    if not prefix:
        return '%s.%s' % (namespace_prefix, metric_name)
    return '%s.%s' % (prefix, metric_name)


def define_tagkv(tagk, tagv_list, regex=False):
    pass


def clean_tagkv():
    pass


def valid_tagkv(tagk, tagv):
    return True


def define_counter(name, units=None, prefix=None):
    global all_metrics
    cname = _c(name, prefix)
    if cname in all_metrics and all_metrics[cname] != 'counter':
        # logging.warn('metric redefined. %s %s', cname, all_metrics[cname])
        sys.stderr.write('metric redefined. '
                         '%s %s\n' % (cname, all_metrics[cname]))
        return
    all_metrics[cname] = 'counter'


def define_rate_counter(name, units=None, prefix=None):
    """
    Define the rate counter
    WARNING: Only used in node on which has metricserver2 running !!!
    :param name:
    :param units:
    :param prefix:
    :return:
    """
    global all_metrics
    cname = _c(name, prefix)
    if cname in all_metrics and all_metrics[cname] != 'rate_counter':
        sys.stderr.write('metric redifined for rate counter. '
                         '%s %s\n' % (cname, all_metrics[cname]))
        return
    all_metrics[cname] = 'rate_counter'


def define_meter(name, units=None, prefix=None):
    """
    Define meter
    Warning: Only support on node with metricserver2 above v1.0.0.65
    """
    global all_metrics
    cname = _c(name, prefix)
    if cname in all_metrics and all_metrics[cname] != 'meter':
        sys.stderr.write('metric redifined for meter. '
                         '%s %s\n' % (cname, all_metrics[cname]))
        return
    all_metrics[cname] = 'meter'


def define_timer(name, units=None, prefix=None):
    global all_metrics
    cname = _c(name, prefix)
    if cname in all_metrics and all_metrics[cname] != 'timer':
        # logging.warn('metric redefined. %s %s', cname, all_metrics[cname])
        sys.stderr.write('metric redefined. '
                         '%s %s\n' % (cname, all_metrics[cname]))
        return
    all_metrics[cname] = 'timer'


def define_store(name, units=None, prefix=None):
    global all_metrics
    cname = _c(name, prefix)
    if cname in all_metrics and all_metrics[cname] != 'store':
        # logging.warn('metric redefined. %s %s', cname, all_metrics[cname])
        sys.stderr.write('metric redefined. '
                         '%s %s\n' % (cname, all_metrics[cname]))
        return
    all_metrics[cname] = 'store'


def define_store_with_ts(name, units=None, prefix=None):
    """
    define store with ts
    WARNING:Use only when the node runs metricserver2
    :param name:
    :param units:
    :param prefix:
    :return:
    """
    global all_metrics
    cname = _c(name, prefix)
    if cname in all_metrics and all_metrics[cname] != 'ts_store':
        sys.stderr.write('metric redefined. '
                         '%s %s\n' % (cname, all_metrics[cname]))
        return
    all_metrics[cname] = 'ts_store'


if 'PyPy' in sys.version:
    def register_alarm(metric_name, condition,
                       metric_subfix="avg",
                       metric_prefix=None,
                       enabled=True,
                       recipients=None,
                       message=None,
                       phones=None,
                       silent_mode='fixed',
                       silent_interval=60*5,
                       rule_type=0,
                       tongbi_interval=1,
                       start_time="1m-ago",
                       opentsdb_aggregator=None,
                       opentsdb_downsampler='1m-avg',
                       opentsdb_rate=None,
                       dingding="",
                       psm="data.default.alert"):
        pass
else:
    def register_alarm(metric_name, condition,
                       metric_subfix="avg",
                       metric_prefix=None,
                       enabled=True,
                       recipients=None,
                       message=None,
                       phones=None,
                       silent_mode='fixed',
                       silent_interval=60*5,
                       rule_type=0,
                       tongbi_interval=1,
                       start_time="1m-ago",
                       opentsdb_aggregator=None,
                       opentsdb_downsampler='1m-avg',
                       opentsdb_rate=None,
                       dingding="",
                       psm="data.default.alert"):
        from pyutil.bdmonitor.bdmonitor import set_ruler
        global all_metrics
        global default_alarm_recipients
        global namespace_prefix

        cname = _c(metric_name, metric_prefix)
        if cname not in all_metrics:
            # logging.warn('metric not exist. %s', cname)
            sys.stderr.write('metric not exist. %s\n' % (cname))
            return
        metric_type = all_metrics[cname]
        if metric_type == "timer":
            cname = ".".join([cname, metric_subfix])

        req = {}
        req['service_name'] = cname + condition
        req['metric_name'] = cname
        req['metric_type'] = metric_type
        req['condition'] = condition
        req['enabled'] = enabled
        if recipients:
            req['recipients'] = recipients
        else:
            req['recipients'] = default_alarm_recipients
        if phones:
            req['phones'] = phones
        else:
            req['phones'] = ''
        if message:
            req['message'] = message
        req['silent_mode'] = silent_mode
        req['silent_interval'] = silent_interval
        req['rule_type'] = rule_type
        req['tongbi_interva'] = tongbi_interval
        req['start_time'] = start_time
        if opentsdb_aggregator:
            req['opentsdb_aggregator'] = opentsdb_aggregator
            req['opentsdb_rate'] = opentsdb_rate
        elif metric_type == 'counter':
            req['opentsdb_aggregator'] = 'sum'
            req['opentsdb_rate'] = "rate:"
        else:
            req['opentsdb_aggregator'] = 'avg'
            req['opentsdb_rate'] = ""
        req['opentsdb_downsampler'] = opentsdb_downsampler
        req['dingding'] = dingding
        req['psm'] = psm
        try:
            ret, msg = set_ruler(req)
            # res = set_ruler(req,port=12345)
            # import pdb;pdb.set_trace()
            if ret == 0:
                # logging.warn('fail to set_ruler:%s',msg)
                sys.stderr.write('fail to set_ruler:%s\n' % (msg))
        except Exception:
            # logging.warn('set_ruler raise exception %s', e)
            err = traceback.format_exc()
            sys.stderr.write('set_ruler raise exception %s\n' % err)

if 'PyPy' in sys.version:
    def unregister_alarm(metric_name, condition,
                         metric_subfix="avg",
                         metric_prefix=None,
                         enabled=True,
                         recipients=None,
                         message=None,
                         phones=None,
                         silent_mode='fixed',
                         silent_interval=60*5,
                         rule_type=0,
                         tongbi_interval=1,
                         opentsdb_aggregator=None,
                         opentsdb_downsampler='1m-avg',
                         opentsdb_rate=None,
                         dingding="",
                         psm="data.default.alert"):
        pass
else:
    def unregister_alarm(metric_name, condition,
                         metric_subfix="avg",
                         metric_prefix=None,
                         enabled=True,
                         recipients=None,
                         message=None,
                         phones=None,
                         silent_mode='fixed',
                         silent_interval=60*5,
                         rule_type=0,
                         tongbi_interval=1,
                         opentsdb_aggregator=None,
                         opentsdb_downsampler='1m-avg',
                         opentsdb_rate=None,
                         dingding="",
                         psm="data.default.alert"):
        global all_metrics
        global default_alarm_recipients
        global namespace_prefix
        from pyutil.bdmonitor.bdmonitor import del_ruler

        cname = _c(metric_name, metric_prefix)
        if cname not in all_metrics:
            # logging.warn('metric not exist. %s', cname)
            sys.stderr.write('metric not exist. %s\n' % (cname))
            return
        metric_type = all_metrics[cname]
        if metric_type == "timer":
            cname = ".".join([cname, metric_subfix])

        req = {}
        req['service_name'] = cname + condition
        req['metric_name'] = cname
        req['metric_type'] = metric_type
        req['condition'] = condition
        req['enabled'] = enabled
        if recipients:
            req['recipients'] = recipients
        else:
            req['recipients'] = default_alarm_recipients
        if phones:
            req['phones'] = phones
        else:
            req['phones'] = ''
        if message:
            req['message'] = message
        req['silent_mode'] = silent_mode
        req['silent_interval'] = silent_interval
        req['rule_type'] = rule_type
        req['tongbi_interva'] = tongbi_interval
        if opentsdb_aggregator:
            req['opentsdb_aggregator'] = opentsdb_aggregator
        elif metric_type == 'counter':
            req['opentsdb_aggregator'] = 'sum'
            req['opentsdb_rate'] = "rate:"
        else:
            req['opentsdb_aggregator'] = 'avg'
            req['opentsdb_rate'] = ""
        req['opentsdb_downsampler'] = opentsdb_downsampler
        req['dingding'] = dingding
        req['psm'] = psm
        # print req
        try:
            ret, msg = del_ruler(req)
            # res = del_ruler(req,port=12345)
            # import pdb;pdb.set_trace()
            if ret == 0:
                # logging.warn('fail to del_ruler:%s',msg)
                sys.stderr.write('fail to del_ruler:%s\n' % (msg))
        except Exception:
            # logging.warn('del_ruler raise exception %s', e)
            err = traceback.format_exc()
            sys.stderr.write('del_ruler raise exception %s\n' % err)


def _send_message(req):
    global send_batch_size, use_remote
    reqs = getattr(reqs_buffer, "val", [])
    reqs.append(req)
    if len(reqs) < send_batch_size:
        reqs_buffer.val = reqs
    else:
        reqs_buffer.val = []
        send_buffer = msgpack.dumps(reqs)
        try:
            if use_remote:
                global remote_udp_socket, remote_addr, remote_port
                remote_udp_socket.sendto(send_buffer, (remote_addr, remote_port))
            else:
                global udp_socket, server_address
                udp_socket.sendto(send_buffer, server_address)
        except Exception:
            if send_batch_size < 128:
                send_batch_size = send_batch_size + 1


def _flush_pending():
    """ Flush metrics in pending status when process exits"""
    global use_remote

    reqs = getattr(reqs_buffer, 'val', [])
    if reqs:
        try:
            if use_remote:
                global remote_udp_socket, remote_addr, remote_port
                remote_udp_socket.sendto(msgpack.dump(reqs), (remote_addr, remote_port))
            else:
                global udp_socket, server_address
                udp_socket.sendto(msgpack.dumps(reqs), server_address)
        except Exception:
            err = traceback.format_exc()
            sys.stderr.write('flush metrics error: %s\n' % err)


def _emit(metric_type, name, value, prefix, tagkv, timestamp=-1):
    if tagkv is None:
        tagkv = {}

    if environ.get('IS_TCE_DOCKER_ENV') == "1" and \
            environ.get('IS_SYSTEM_TEST_ENV', '0') != '1':
        pod_name = environ.get('MY_POD_NAME')
        tce_stage = environ.get('TCE_STAGE')

        if default_psm not in podtag_blacklist:
            tagkv['pod_name'] = pod_name

        tagkv['env_type'] = 'tce'

        if tce_stage is not None:
            tagkv['deploy_stage'] = tce_stage

    if default_psm:
        tagkv['_psm'] = default_psm

    cname = _c(name, prefix)

    def _warn(msg):
        sys.stderr.write('[%s %s] %s\n' % (metric_type, cname, msg))
    # 非关键服务，但调用方很多, catch住异常以防出问题
    try:
        tag_list = []
        if cname not in all_metrics:
            _warn('metric not exist. %s\n' % cname)
            return
        if all_metrics[cname] != metric_type:
            _warn('metric type not matched. %s' % all_metrics[cname])
            return
        for tagk, tagv in tagkv.items():
            tag_list.append("=".join([tagk, tagv]))
        if timestamp <= -1:
            timestamp = ""
        else:
            timestamp = str(timestamp)
        req = ['emit', metric_type, cname, str(value), "|".join(tag_list), timestamp]
        _send_message(req)
    except Exception as e:
        try:
            _warn('fail to emit: %s' % e)
        except Exception:
            pass


def has_defined_tagkv(tagk, tagv_list):
    return True


def has_defined_metrics(name, prefix=None):
    global all_metrics
    cname = _c(name, prefix)
    return cname in all_metrics


def emit_counter(name, value, prefix=None, tagkv=None):
    _emit('counter', name, value, prefix, tagkv)


def emit_rate_counter(name, value, prefix=None, tagkv=None):
    """
    Emit the rate counter
    WARNING: Only used in node on which has metricserver2 running !!!
    :param name:
    :param value:
    :param prefix:
    :param tagkv:
    :return:
    """
    _emit('rate_counter', name, value, prefix, tagkv)


def emit_meter(name, value, prefix=None, tagkv=None):
    """
    Define meter
    Warning: Only support on node with metricserver2 above v1.0.0.65
    """
    _emit('meter', name, value, prefix, tagkv)


def emit_timer(name, value, prefix=None, tagkv=None):
    _emit('timer', name, value, prefix, tagkv)


def emit_store(name, value, prefix=None, tagkv=None):
    _emit('store', name, value, prefix, tagkv)


def emit_store_with_ts(name, value, timestamp, prefix=None, tagkv=None):
    """
    emit the store with timestamp
    WARNING: this interface only used when node ruruns
    with metricserver2 running on
    :param name:
    :param value:
    :param timestamp:
    :param prefix:
    :param tagkv:
    :return:
    """
    _emit('ts_store', name, value, prefix, tagkv, timestamp)


def _define_reset_function(metric_type, doc=''):
    supported_metric_types = ('counter', 'rate_counter',
                              'timer', 'store', 'ts_store')
    if metric_type not in supported_metric_types:
        sys.stderr.write('unknown metric type {}\n'.format(metric_type))
        return

    def reset(name, prefix=None, tagkv=None):
        global all_metrics

        if tagkv is None:
            tagkv = {}

        cname = _c(name, prefix)
        if cname not in all_metrics:
            sys.stderr.write('metric not exist. %s\n' % (cname))
            return
        if all_metrics[cname] != metric_type:
            sys.stderr.write('reset metric type not matched. '
                             '%s\n' % (all_metrics[cname]))
            return

        tag_list = []
        for tagk, tagv in tagkv.items():
            tag_list.append("{}={}".format(tagk, tagv))
        req = ['reset', metric_type, cname, "|".join(tag_list), ""]
        _send_message(req)

    reset.__name__ = 'reset_{}'.format(metric_type)
    reset.__doc__ = doc
    return reset


reset_counter = _define_reset_function('counter')
reset_rate_counter = _define_reset_function('rate_counter', doc='''
Reset the rate counter
WARNING: Only used in node on which has metricserver2 running !!!
''')
reset_timer = _define_reset_function('timer')
reset_store = _define_reset_function('store')
reset_ts_store = _define_reset_function('ts_store', doc='''
reset the store with ts
WARNING: Not used now
''')
reset_store_with_ts = reset_ts_store


def start():
    pass


class Timer(object):
    def __init__(self, metric_name, prefix=None, tagkv=None):
        self.metric_name = metric_name
        self.prefix = prefix
        self.tagkv = tagkv or {}

    def __enter__(self):
        self.start_time = time.time()

    def __exit__(self, type, value, traceback):
        emit_timer(self.metric_name, 1000 * (time.time() - self.start_time),
                   prefix=self.prefix, tagkv=self.tagkv)


def timing(metric_name, prefix=None, tagkv=None):
    '''
    timing decorator for function and method
    '''
    def timing_wrapper(func):
        @functools.wraps(func)
        def f2(*args, **kwargs):
            with Timer(metric_name, prefix, tagkv):
                return func(*args, **kwargs)
        return f2
    return timing_wrapper


if __name__ == "__main__":
    pass
