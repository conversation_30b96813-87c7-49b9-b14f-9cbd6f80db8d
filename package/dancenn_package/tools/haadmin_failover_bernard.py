#!/usr/bin/python3

# https://code.byted.org/inf/cloudfs/blob/03ef8b3e72e4789c3b09d0b5c59d3be4012a4f57/saas/metadata-cluster/babysitter/k8s/kustomize/version-manager/namenode/k8s/kustomize/base/haadmin-failover.py

import argparse
import http.client
import ipaddress
import json
import logging
import os
import re
import socket
import subprocess
import time


class ColorfulFormatter(logging.Formatter):

  def __init__(self):
    super().__init__()
    grey = "\x1b[38;20m"
    yellow = "\x1b[33;20m"
    red = "\x1b[31;20m"
    bold_red = "\x1b[31;1m"
    reset = "\x1b[0m"
    fmt = "%(asctime)s - %(levelname)s - %(message)s (%(filename)s:%(lineno)d)"
    self._formats = {
      logging.DEBUG: grey + fmt + reset,
      logging.INFO: grey + fmt + reset,
      logging.WARNING: yellow + fmt + reset,
      logging.ERROR: red + fmt + reset,
      logging.CRITICAL: bold_red + fmt + reset,
    }

  def format(self, record):
    log_fmt = self._formats.get(record.levelno)
    formatter = logging.Formatter(log_fmt)
    return formatter.format(record)


class NameNodeStatus(object):

  def __init__(self, name, domain, http_port, ha_rpc_port, is_observer):
    # e.g. cfs-namenode-root-18014398509482230-0
    self._name = name
    # e.g. cfs-namenode-root-18014398509482230-0.
    #      cfs-namenode-root-18014398509482230.root-18014398509482230.
    #      svc.cluster.local
    # self._domain = domain
    self._domain = domain.strip('[]')
    self._ip_addr = None
    self._http_port = http_port
    self._ha_rpc_port = ha_rpc_port
    self._is_observer = is_observer

  def refresh(self):
    try:
      self._ip_addr = re.sub(
        r":0*([0-9a-fA-F]+)", r":\1",
        ipaddress.IPv6Address(
          socket.getaddrinfo(self._domain, None,
                             socket.AF_INET6)[0][4][0]).exploded.replace(
                               "0000", "0"))
      conn = http.client.HTTPConnection(self._domain, port=self._http_port)
      conn.request(method="GET", url="/status")
      resp = conn.getresponse()
      if resp.status != 200:
        raise ValueError(
          "Failed to get status, status code: {}, reason: {}".format(
            resp.status, resp.reason))
      self._status = json.loads(resp.read().decode("utf-8"))
      return True
    except Exception as e:
      logging.warning(e)
      logging.exception("Failed to get status of {}".format(self._domain))
      return False

  def name(self):
    return self._name

  def domain(self):
    return self._domain

  def ip_addr(self):
    return self._ip_addr

  def ha_rpc_port(self):
    return self._ha_rpc_port

  def is_observer(self):
    return self._is_observer

  def ha_state(self):
    return self._status["ha_state"]

  def alive_dn_num(self):
    return int(self._status["datanodes"]["stats"]["alive"])

  def content_stale_dn_num(self):
    return int(self._status["datanodes"]["stats"]["content_stale"])

  def dead_dn_num(self):
    return int(self._status["datanodes"]["stats"]["dead"])

  def startup_time(self):
    return min(
      filter(lambda x: x > 0, [
        self._status["last_safemode_enter_at"],
        self._status["last_safemode_leave_at"],
        self._status["last_standby_enter_at"],
        self._status["last_standby_leave_at"],
      ]))


class FailoverController(object):

  def __init__(self, dispatched_conf, dryrun, manual):
    # dispatched_conf = json.load(open(dispatched_conf_file))
    self._fs_id = int(dispatched_conf["FS_ID"])
    self._ns_name = dispatched_conf["NS_NAME"]
    self._ns_id = int(dispatched_conf["NS_ID"])
    self._dryrun = dryrun
    self._manual = manual

    nns = list(
      filter(
        # Deprecated names.
        lambda n: n.name() not in ["n0", "bak0", "observer"],
        map(
          lambda n: NameNodeStatus(
            n["NAME"],
            n["HOST_IP"],
            dispatched_conf["NN_HTTP_PORT"],
            dispatched_conf["NN_HA_RPC_PORT"],
            n["IS_OBSERVER"],
          ),
          dispatched_conf["NNS"],
        ),
      ))
    # Me.
    self._me = list(
      filter(lambda nn: nn.name() == os.environ["MY_POD_NAME"], nns))
    assert len(self._me) == 1
    self._me = self._me[0]
    # Others.
    self._others = list(
      filter(
        lambda n: n.name() != self._me.name() and not n.is_observer(),
        nns,
      ))
    assert len(self._others) > 0

  def failover(self):
    running_cmds = [x for x in os.popen("ps h -eo command")] # ignore_security_alert RCE
    is_dancenn_running = any([
      (("dancenn" in cmd) and ("flagfile" in cmd)) for cmd in running_cmds
    ])
    is_zkfc_running = any([
      (("zkfc" in cmd) and ("java" in cmd)) for cmd in running_cmds
    ])
    if not is_zkfc_running or not is_dancenn_running:
      logging.warning("Oops! dancenn or zkfc are down")
      raise Exception("Oops! dancenn or zkfc are down")
    try:
      for i in range(30):
        if self._failover_once():
          return 
        time.sleep(5)
    # There are some cases we do not how to handle.
    # Just wait and leave it to RD.
    except Exception as e:
      logging.exception(e)
      raise e
    raise Exception("do failover failed")

  def _failover_once(self):
    (kActive, kStandby, kObserver) = ("ACTIVE", "STANDBY", "OBSERVER")
    if not self._me.refresh():
      return False
    others = filter(lambda n: n.refresh(), self._others)

    if self._me.ha_state() == kObserver:
      logging.info("I am observer, cannot failover")
      raise Exception("I am observer, cannot failover")
    elif self._me.ha_state() == kStandby:
      logging.info("I am standby, cannot failover")
      raise Exception("I am standby, cannot failover")
    elif self._me.ha_state() == kActive:
      for nn in others:
        # We don't know how to handle double active.
        # Leave it to RD.
        assert nn.ha_state() == kStandby
        if not self._manual and nn.alive_dn_num() < self._me.alive_dn_num():
          logging.warning(
            "No enough datanodes report heartbeat, actual: {}, expected: {}".
            format(nn.alive_dn_num(), self._me.alive_dn_num()))
          continue
        if not self._manual and nn.startup_time() + 3 * 60 >= time.time():
          logging.warning(
            "It is now less than 3 minutes before standby started, "
            "standby is started at {}".format(nn.startup_time()))
          continue
        if not self._manual and nn.content_stale_dn_num() - nn.dead_dn_num(
        ) > nn.alive_dn_num() * 0.05:
          logging.warning("There are {} content stale datanodes".format(
            nn.content_stale_dn_num()))
          continue
        cmd = (
          "/opt/tiger/cfs_hdfs_deploy/hadoop/bin/hdfs haadmin -failover "
          "-conf /opt/tiger/cfs_hdfs_deploy/hadoop/conf/hdfs-site.xml "
          "-fsid %d "
          "-ns %s "
          "-nsid %d "
          "-fromnode %s -fromaddr %s/%s:%d "
          "-tonode %s -toaddr %s/%s:%d "
          ">>/var/log/tiger/yarn_logs/${MY_POD_NAME##*-}/haadmin_failover.out "
          "2>>/var/log/tiger/yarn_logs/${MY_POD_NAME##*-}/haadmin_failover.err"
        ) % (
          self._fs_id,
          self._ns_name,
          self._ns_id,
          self._me.name(),
          self._me.domain(),
          self._me.ip_addr(),
          self._me.ha_rpc_port(),
          nn.name(),
          nn.domain(),
          nn.ip_addr(),
          nn.ha_rpc_port(),
        )
        logging.info("cmd: {}".format(cmd))
        if self._dryrun:
          return True
        return subprocess.run(cmd, shell=True).returncode == 0 # ignore_security_alert RCE
      logging.warning("Oops! No standby found")
      return False


if __name__ == "__main__":
  parser = argparse.ArgumentParser("HAAdmin failover")
  parser.add_argument("-d",
                      "--dryrun",
                      type=str,
                      required=False,
                      default="",
                      help="If true, just print command but do not execute it")
  parser.add_argument("-m",
                      "--manual",
                      type=str,
                      required=False,
                      default="",
                      help="If true, will stop some protection rules")
  args = parser.parse_args()
  args.dryrun = args.dryrun.lower() == "true"
  args.manual = args.manual.lower() == "true"
  assert not (args.dryrun and args.manual)

  logging.getLogger().setLevel(logging.INFO)
  # Output to stderr.
  stderr_handler = logging.StreamHandler()
  stderr_handler.setLevel(logging.INFO)
  stderr_handler.setFormatter(ColorfulFormatter())
  logging.getLogger().addHandler(stderr_handler)

  conf_file = "/opt/tiger/dispatched-conf.json"
  if not os.path.exists(conf_file):
    conf_file = "/conf/dispatched-conf.json"

  dispatched_conf = json.load(open(conf_file))
  if 'MY_POD_NAME' not in os.environ:
    os.environ['MY_POD_NAME'] = dispatched_conf['MY_POD_NAME']

  # Output to file.
  file_handler = logging.FileHandler(
    "/var/log/tiger/yarn_logs/{}/haadmin_failover.log".format(
      os.environ["MY_POD_NAME"].split("-")[-1]))
  file_handler.setLevel(logging.INFO)
  file_handler.setFormatter(
    logging.Formatter("%(asctime)s %(levelname)s %(message)s"))
  logging.getLogger().addHandler(file_handler)

  FailoverController(dispatched_conf,
                     dryrun=args.dryrun,
                     manual=args.manual).failover()
