#!/usr/bin/python
# coding=utf-8
import commands
import requests
import json
import logging
def get_host_ip():
    """
    查询本机ip地址
    :return: ip
    """
    ip = commands.getoutput('hostname -i')
    return ip

def get_backend_name():
    """
    查询本机hdfs backend name
    :return: backend
    """
    lh = commands.getoutput('/opt/tiger/ss_bin/lh -s')
    for line in lh.splitlines():
        if ("inf.dancenn.nn." in line):
            str_tmp = line.rsplit(".", 1)
            backend = str_tmp[-1]
            return backend
        elif("inf.dancenn.observer." in line):
            str_tmp = line.rsplit(".", 1)
            backend = str_tmp[-1]
            return backend
        else:
            continue
    return "null"







def get_current_version():
    """
    查询DanceNN version
    :return: version
    """
    with open("/opt/tiger/dancenn_deploy/current_revision") as f:
        for line in f.readlines():
            if ("version:" in line):
                version_line = line
                str_tmp = version_line.rsplit(":", 1)
                version = str_tmp[-1]
                return version
        return 'null'


def get_idc():
    """
    查询本机idc
    :return: idc
    """
    lh = commands.getoutput('/opt/tiger/ss_bin/lh -s')
    for line in lh.splitlines():
        if ("idc." in line):
            str_tmp = line.rsplit(".", 1)
            idc = str_tmp[-1]
            return line
    return "null"

def get_backend_node_name():
    """
    查询本机hdfs backend name
    :return: backend_node
    """
    lh = commands.getoutput('/opt/tiger/ss_bin/lh -s')
    backend_name = get_backend_name()
    for line in lh.splitlines():
        if ("inf.hadoop.nn." + backend_name + "." in line):
            str_tmp = line.rsplit(".", 1)
            backend_node = str_tmp[-1]
            return backend_node
        elif("inf.dancenn.observer." in line):
            return "observer"
        else:
            continue
    return "null"



def get_dancenn_status_url():
    """
    """
    global url
    try:
        ip = commands.getoutput('hostname -i')
        if ip is not None:
            url = "http://" + ip + ":5070/status"
            return url
        else:
            url = "http://" + "127.0.0.1" + ":5070/status"
    except OSError:
        print("ERROR: get_dancenn_status_url")
    else:
        return url

    return url


def get_lh():
    """
    查询本机tag
    :return: tag
    """
    lh = commands.getoutput('/opt/tiger/ss_bin/lh -s')
    return lh

def send_lark_to_group(group):
    print("start")
    as_url = 'https://fsopen.bytedance.net/open-apis/auth/v3/tenant_access_token/internal/'
    headers = {'Content-Type': 'application/json'}
    data = {
        "app_id": "cli_9e77c0486e2b100b",
        "app_secret": "E12GsJphBx1aWkCIuVPA6cGHLrHsEgPO"
    }
    rsp = requests.post(url=as_url, headers=headers, data=json.dumps(data))
    d = rsp.json()
    token = d['tenant_access_token']
    print(token)
    url = 'https://fsopen.bytedance.net/open-apis/message/v4/send/'
    headers = {'Content-Type': 'application/json', 'Authorization': 'Bearer ' + token}
    data = {
        "chat_id": group,
        "msg_type": "interactive",
        # 是否进行动态调整方框大小
        "card": {
            "config": {
                "wide_screen_mode": False,
                "enable_forward": True
            },
            # 标题
            "header":
                {
                    "title": {
                        "tag": "plain_text",
                        "content": "DanceNN Restart 通知"
                    },
                    "template": "Orange"
                },
            "elements":
                [
                    {
                        "tag": "div",
                        "text": {
                            "tag": "lark_md",
                            "content": " **[HDFS Backend]  **" + get_backend_name() + "\n"
                        }
                    },
                    # 分割线
                    {
                        "tag": "hr"
                    },
                    {
                        "tag": "div",
                        "text": {
                            "tag": "lark_md",
                            "content": " **[HDFS Backend Node]  **" + get_backend_node_name() + "\n"
                        }
                    },
                    # 分割线
                    {
                        "tag": "hr"
                    },
                    {
                        "tag": "div",
                        "text": {
                            "tag": "lark_md",
                            "content": " **[DanceNN IP]  **" + get_host_ip() + "\n"
                        }
                    },
                    # 分割线
                    {
                        "tag": "hr"
                    },
                    {
                        "tag": "div",
                        "text": {
                            "tag": "lark_md",
                            "content": " **[IDC]  **" + get_idc() + "\n"
                        }
                    },
                    # 分割线
                    {
                        "tag": "hr"
                    },
                    {
                        "tag": "div",
                        "text": {
                            "tag": "lark_md",
                            "content": " **[CURRENT VERSION]  **" + get_current_version() + "\n"
                        }
                    },
                    # 分割线
                    {
                        "tag": "hr"
                    },
                    # button按钮
                    {
                        "tag": "action",
                        "actions":
                            [
                                {
                                    "tag": "button",
                                    "text": {
                                        "tag": "lark_md",
                                        "content": "DanceNN Status URL"
                                    },
                                    "url": get_dancenn_status_url(),
                                    "type": "primary"
                                }
                            ]
                    }
                ]
        }

    }
    try:
        rsp = requests.post(url=url, headers=headers, data=json.dumps(data))
        d = rsp.json()
        print(d)
        return d['data']['message_id']
    except:
        logging.info("send lark to {} failed".format(group))
        return 'failed'
if __name__ == '__main__':
    send_lark_to_group("6563418111813353736")

