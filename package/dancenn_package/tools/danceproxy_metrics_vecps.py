#!/usr/bin/env python3

from http.server import BaseHTTPRequestHandler, HTTPServer
import argparse
import base64
from functools import partial
import logging
import re
import requests
import time


def parse_cmd() -> argparse.ArgumentParser:
    parse = argparse.ArgumentParser(description='get metrics from danceproxy')
    parse.add_argument('-H', "--hosts", type=str,
                       required=True, help="danceproxy hosts")
    parse.add_argument('-p', "--prefix", type=str,
                       required=True, help="metrics prefix")
    parse.add_argument('-r', "--cfs_region", type=str,
                       required=True, help="cfs region")
    parse.add_argument('-e', "--cfs_env", type=str,
                       required=True, help="cfs env")
    parse.add_argument('-c', "--cfs_cluster", type=str,
                       required=True, help="cfs cluster")
    parse.add_argument('-P', "--port", type=int,
                       required=True, help="http port for prometheus")
    return parse


class S(BaseHTTPRequestHandler):
    def __init__(self, env, *args, **kwargs) -> None:
        self.env = env
        self.escape_bs = re.compile(r"")
        super().__init__(*args, **kwargs)

    def _set_response(self):
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()

    def do_GET(self):
        logging.info("GET request,\nPath: %s\nHeaders:\n%s\n",
                     str(self.path), str(self.headers))
        self._set_response()
        self.wfile.write(str.encode(self.run_once2()))

    def get_metrics(self, host: str, port: int) -> dict:
        result = None
        for i in range(3):
            try:
                response = requests.get(
                    "http://"+host+":"+str(port)+"/metrics", timeout=30)
            except Exception as e:
                logging.exception(e)
                continue
            if response.status_code == 200:
                result = response.json()
            else:
                logging.error(response)
            break
        return result

    def parse_name(self, k, host):
        # tags = {'host':host[0], 'port':str(host[1])}
        # NOTICE: According to oncall of metric server, setting host and dc by user
        # (sdk) is not recommended. It will cause some unpected problems.
        # But Grafana needs this.
        tags = {'host': host[0], 'port': str(host[1]), 'cfs_region': self.env["region"],
                'cfs_env': self.env["env"], 'cfs_cluster': self.env["cluster"]}
        segs = k.split('#')
        segs[0] = segs[0].replace('.', '_')
        if len(segs) == 1:
            return segs[0], tags
        for tag in segs[1:]:
            tagkv = tag.split('=')
            if len(tagkv) != 2:
                logging.error('Invalid tagkv: {}'.format(tag))
                continue
            tk = tagkv[0].replace('.', '_')
            tv = tagkv[1]
            # 处理中文
            if segs[0] == 'WhiteListHitCount' and tk == 'team':
                tv = base64.b16encode(tv.encode('utf8'))
            tags[tk] = tv
        return segs[0], tags

    # '\n' '\' or '"' are NOT allowed in label name nor value
    def parse_label(self, tags: dict) -> str:
        res = ""
        for k, v in tags.items():
            if res != "":
                res += ','
            res += k
            res += '="{}"'.format(v)
        return res

    def run_once(self) -> str:
        res = ""
        for host in self.env["host"]:
            try:
                result = self.get_metrics(host[0], host[1])
                if not isinstance(result, dict):
                    logging.error(
                        'Unknown type of result, expected type is map.')
                    continue
                for center, ms in result.items():
                    center = center.replace('.', '_')
                    if 'counters' not in ms:
                        logging.warning(
                            'No counters in {} center!'.format(center))
                    else:
                        for k, v in ms['counters'].items():
                            name, tags = self.parse_name(k, host)
                            tagstr = self.parse_label(tags)
                            res += '# TYPE {}_{}_store_{} gauge\n'.format(
                                self.env["prefix"], center, name)
                            res += '{}_{}_store_{}{{{}}} {}\n'.format(
                                self.env["prefix"], center, name, tagstr, v)
                            # logging.info(
                            # 'counters: {} -> {}'.format(k.encode('utf8'), v))
                    if 'gauges' not in ms:
                        logging.warning(
                            'No gauges in {} center!'.format(center))
                    else:
                        for k, v in ms['gauges'].items():
                            name, tags = self.parse_name(k, host)
                            tagstr = self.parse_label(tags)
                            res += '# TYPE {}_{}_store_{} gauge\n'.format(
                                self.env["prefix"], center, name)
                            res += '{}_{}_store_{}{{{}}} {}\n'.format(
                                self.env["prefix"], center, name, tagstr, v)
                            # logging.info('gauges: {} -> {}'.format(k, v))
                    if 'histograms' not in ms:
                        logging.warning(
                            'No histograms in {} center!'.format(center))
                    else:
                        for k, v in ms['histograms'].items():
                            name, tags = self.parse_name(k, host)
                            for quantile, vv in ms['histograms'][k].items():
                                tagstr = self.parse_label(tags)
                                res += '# TYPE {}_{}_histograms_{}_{} gauge\n'.format(
                                    self.env["prefix"], center, name, quantile)
                                res += '{}_{}_histograms_{}_{}{{{}}} {}\n'.format(
                                    self.env["prefix"], center, name, quantile, tagstr, vv)
                                # logging.info('{}.histograms.{}.{}'.format(
                                # center, name, quantile))
            except Exception as e:
                logging.exception(e)
                continue
        return res

    def run_once2(self) -> str:
        res = ""
        for host in self.env["host"]:
            try:
                for i in range(3):
                    try:
                        response = requests.get(
                            "http://"+host[0]+":"+str(host[1])+"/prometheus", timeout=30)
                    except Exception as e:
                        logging.exception(e)
                        continue
                    if response.status_code == 200:
                        res += response.text
                    else:
                        logging.error(response)
                    break
            except Exception as e:
                logging.exception(e)
                continue
        return res

def run(server_class=HTTPServer, handler_class=S, port=8080, env={}) -> None:
    logging.basicConfig(level=logging.INFO)
    server_address = ('0.0.0.0', port)
    handler = partial(handler_class, env)
    httpd = server_class(server_address, handler)
    logging.info('Starting httpd...\n')
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        pass
    httpd.server_close()
    logging.info('Stopping httpd...\n')


if __name__ == '__main__':
    parser = parse_cmd()
    args = parser.parse_args()
    host_list = args.hosts.split(',')
    hosts = []
    for h in host_list:
        t = h.split(':')
        if len(t) != 2:
            logging.error('invalid argument: ', args.hosts)
            exit(-1)
        try:
            port = int(t[1])
        except:
            logging.error('invalid argument: ', args.hosts)
            exit(-1)
        hosts.append((t[0], port))

    env = {
        "prefix": args.prefix.replace('.', '_'),
        "host": hosts,
        "region": args.cfs_region,
        "env": args.cfs_env,
        "cluster": args.cfs_cluster
    }

    run(port=args.port, env=env)
