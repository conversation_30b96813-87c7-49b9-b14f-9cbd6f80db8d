#!/bin/bash

ulimit -c 500000000000

TOOLS_DIR="$(cd $(dirname ${BASH_SOURCE:-$0}); pwd)"
BASE_DIR="$(cd ${TOOLS_DIR}/../; pwd)"
PARENT_DIR="$(cd ${BASE_DIR}/../; pwd)"
CFS_HDFS_DEPLOY="${PARENT_DIR}/cfs_hdfs_deploy"
CFS_DANCENN_DEPLOY="${BASE_DIR}"

DEFAULT_JAVA_HOME="/opt/tiger/jdk/jdk1.8"
[ -d "${DEFAULT_JAVA_HOME}" ] && JAVA_HOME="${DEFAULT_JAVA_HOME}"
export JAVA_HOME="${JAVA_HOME:?"undefined JAVA_HOME"}"
export LD_LIBRARY_PATH="${CFS_DANCENN_DEPLOY}/lib:${JAVA_HOME}/jre/lib/amd64/server:${JAVA_HOME}/jre/lib/amd64:$LD_LIBRARY_PATH"

colon_join() {
    perl -ne 'chomp;push @_,$_}{print join ":",@_'
}

HADOOP_JARS=
for dir in $(cat)
do
  HADOOP_JARS="${HADOOP_JARS}:$(ls ${CFS_HDFS_DEPLOY}/${dir}/*.jar | colon_join)"
done <<-'DONE'
hadoop/share/hadoop/common
hadoop/share/hadoop/common/lib
hadoop/share/hadoop/hdfs
hadoop/share/hadoop/hdfs/lib
DONE

CONF_DIR="${CFS_HDFS_DEPLOY}/hadoop/conf"
DANCENN_JARS="$(find ${CFS_DANCENN_DEPLOY}/jar -type f -not -name 'bookkeeper-server-4.5.0.jar' | colon_join)"

export CLASSPATH="${HADOOP_JARS}:${DANCENN_JARS}:${CONF_DIR}"
