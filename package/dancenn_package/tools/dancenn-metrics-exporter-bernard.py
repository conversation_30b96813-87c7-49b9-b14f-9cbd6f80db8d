#!/usr/bin/python2.7
# -*- coding: utf-8 -*-

# This file was copied from
# https://code.byted.org/inf/dancenn/blob/57bac49424c9b8980a5364d80087f96c0dc906ad/package/dancenn_package/tools/danceproxy_metrics_byte.py
# and slightly modified to make it runnable.
#
# We should update to Python 3 and the latest version of the metrics SDK in the future.
# https://cloud.bytedance.net/docs/metrics/docs/63bbbb1ec6b537022a6000c7/648ac0e510f8f8025abca889?x-resource-account=public
#
# Based on the oncall guide,
# https://oncall.bytedance.net/admin/review/all?id=********&picked_detail=********
# we should query the metrics sent by the current script using the following steps:

import argparse
import base64
import json
import os
import sys
import time

import requests
from pyutil.program import metrics2 as metrics


def parse_cmd():
  parse = argparse.ArgumentParser(description='get metrics from danceproxy')
  parse.add_argument('-H',
                     "--hosts",
                     type=str,
                     default="localhost:5070",
                     help="danceproxy hosts")
  parse.add_argument('-p',
                     "--prefix",
                     type=str,
                     default="inf.hdfs.dancenn.cfs",
                     help="metrics prefix")
  parse.add_argument("-d",
                     "--dispatched_conf",
                     type=str,
                     default="/conf/dispatched-conf.json",
                     help="location of dispatched-conf.json")
  return parse


global_tags = {}
global_host = ""
global_port = 0
global_host_value = ""


def init_global_var(args):
  global global_tags, global_host, global_port
  dispatched_conf = json.load(open(args.dispatched_conf))

  host_list = ['localhost:{}'.format(dispatched_conf['NN_HTTP_PORT'])]
  if len(host_list) > 1:
    print "Not support multi host now"
    exit(1)
  for h in host_list:
    t = h.split(':')
    if len(t) != 2:
      print 'invalid argument: ', args.hosts
      return -1
    try:
      port = int(t[1])
    except:
      print 'invalid argument: ', args.hosts
      return -1

    global_host = t[0]
    global_host_value = global_host
    global_port = t[1]

    metrics.define_tagkv('host', [t[0]])
    metrics.define_tagkv('port', [t[1]])

  if global_host_value in ['localhost', '0.0.0.0', '127.0.0.1']:
    with os.popen('hostname -i') as pipe:
      lines = pipe.readlines()
      if len(lines) > 0:
        global_host_value = str(lines[0]).strip('\n')

  global_tags = {
    "idc": os.environ["RUNTIME_IDC_NAME"],
    "cfs_region": dispatched_conf["CFS_REGION"],
    "cfs_env": dispatched_conf["CFS_ENV"],
    "cfs_cluster": dispatched_conf["CFS_CLUSTER"],
    "nameservice": dispatched_conf["NS_NAME"],
    "pod_name": os.environ["MY_POD_NAME"],
    "ihost": global_host_value,
    "filesystem": dispatched_conf["FS_NAME"],
    "ns_id": str(dispatched_conf["NS_ID"]),
    "port": global_port,
  }
  if "MY_POD_IP" in os.environ and os.environ["MY_POD_IP"] != "":
    global_tags["node_ip"] = os.environ["MY_POD_IP"]
  elif "MY_POD_IPV6" in os.environ and os.environ["MY_POD_IPV6"] != "":
    global_tags["node_ip"] = os.environ["MY_POD_IPV6"]
  elif "MY_HOST_IP" in os.environ and os.environ["MY_HOST_IP"] != "":
    global_tags["node_ip"] = os.environ["MY_HOST_IP"]
  elif "MY_HOST_IPV6" in os.environ and os.environ["MY_HOST_IPV6"] != "":
    global_tags["node_ip"] = os.environ["MY_HOST_IPV6"]
  else:
    global_tags["node_ip"] = ""

  print global_tags
  print global_host
  print global_host_value
  print global_port


def get_metrics(host, port):
  result = None
  for i in range(3):
    try:
      response = requests.get("http://" + host + ":" + str(port) + "/metrics",
                              timeout=30)
    except Exception, e:
      print e
      continue
    if response.status_code == 200:
      result = response.json()
    else:
      print response
    break
  return result


def parse_name(k):
  global global_tags

  tags = global_tags.copy()

  segs = k.split('#')
  if len(segs) == 1:
    return segs[0], tags
  for tag in segs[1:]:
    tagkv = tag.split('=')
    if len(tagkv) != 2:
      print 'Invalid tagkv: {}'.format(tag)
      continue
    tk = tagkv[0]
    tv = tagkv[1]
    # Handle Chinese characters.
    if segs[0] == 'WhiteListHitCount' and tk == 'team':
      tv = base64.b16encode(tv.encode('utf8'))
    tags[tk] = tv
    metrics.define_tagkv(tk, [
      tv,
    ])
  return segs[0], tags


def run_once():
  global global_host, global_port
  try:
    result = get_metrics(global_host, global_port)
    if not isinstance(result, dict):
      print 'Unknown type of result, expected type is map.'
      return
    for center, ms in result.iteritems():
      if 'counters' not in ms:
        print 'No counters in {} center!'.format(center)
      else:
        for k, v in ms['counters'].iteritems():
          name, tags = parse_name(k)
          metrics.define_store('{}.counters.{}'.format(center, name))
          metrics.emit_store('{}.counters.{}'.format(center, name),
                             v,
                             tagkv=tags)
      if 'gauges' not in ms:
        print 'No gauges in {} center!'.format(center)
      else:
        for k, v in ms['gauges'].iteritems():
          name, tags = parse_name(k)
          metrics.define_store('{}.gauges.{}'.format(center, name))
          metrics.emit_store('{}.gauges.{}'.format(center, name), v, tagkv=tags)

      if 'histograms' not in ms:
        print 'No histograms in {} center!'.format(center)
      else:
        for k, v in ms['histograms'].iteritems():
          name, tags = parse_name(k)
          for quantile, vv in ms['histograms'][k].iteritems():
            metrics.define_store('{}.histograms.{}.{}'.format(
              center, name, quantile))
            metrics.emit_store('{}.histograms.{}.{}'.format(
              center, name, quantile),
                               vv,
                               tagkv=tags)
  except Exception as e:
    print e
    return


def main():
  parser = parse_cmd()
  args = parser.parse_args()
  metrics.init({'metrics_namespace_prefix': args.prefix})
  init_global_var(args)

  exec_time = os.environ.get('EXEC_TIME')
  if exec_time and float(exec_time) / 1000 > time.time():
    time.sleep(float(exec_time) / 1000 - time.time())
  run_once()


if __name__ == "__main__":
  sys.exit(main())
