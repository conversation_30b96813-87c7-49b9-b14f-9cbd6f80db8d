#!/usr/bin/python
# -*- coding: utf-8 -*-

import argparse
import base64
import logging
import requests
import signal
import sys
import time
from pyutil.program import metrics2 as metrics


def parse_cmd():
  parse = argparse.ArgumentParser(description="get metrics from dancenn")
  parse.add_argument("-R",
                     "--remote",
                     type=lambda x: x.lower() == "true",
                     required=True,
                     help="use remote metricserver or not")
  parse.add_argument("-a",
                     "--addr",
                     type=str,
                     required=False,
                     help="metricserver addr")
  parse.add_argument("-P",
                     "--port",
                     type=int,
                     required=False,
                     help="metricserver port")
  parse.add_argument("-H",
                     "--hosts",
                     type=str,
                     required=True,
                     help="danceproxy hosts")
  parse.add_argument("-N",
                     "--pod_name",
                     type=str,
                     required=True,
                     help="k8s pod name")
  parse.add_argument("-n",
                     "--node_ip",
                     type=str,
                     required=True,
                     help="k8s node ip")
  parse.add_argument("-p",
                     "--prefix",
                     type=str,
                     required=True,
                     help="metrics prefix")
  parse.add_argument("-r",
                     "--cfs_region",
                     type=str,
                     required=True,
                     help="cfs region")
  parse.add_argument("-e", "--cfs_env", type=str, required=True, help="cfs env")
  parse.add_argument("-c",
                     "--cfs_cluster",
                     type=str,
                     required=True,
                     help="cfs cluster")
  parse.add_argument("-l", "--log_dir", type=str, required=True, help="log dir")
  return parse


def get_metrics(host, port):
  for i in range(3):
    try:
      logging.info("Begin to get metrics from {}:{}, time: {}".format(
        host, port, i + 1))
      response = requests.get("http://{}:{}/metrics".format(host, port),
                              timeout=30)
      if response.status_code == 200:
        logging.info("Get metrics from {}:{} succeed".format(host, port))
        return response.json()
      else:
        logging.error("Get metrics from {}:{} failed, response:{}".format(
          host, port, response))
    except Exception as e:
      logging.error("Get metrics from {}:{} failed, exception: {}".format(
        host, port, e))
  return None


def run(hosts, pod_name, node_ip, cfs_region, cfs_env, cfs_cluster):
  while True:
    cost_sec = run_once(hosts, pod_name, node_ip, cfs_region, cfs_env,
                        cfs_cluster)
    if cost_sec >= 30:
      logging.error("run_once spent {} secs".format(cost_sec))
    else:
      time.sleep(30 - cost_sec)


def parse_name(k, host, pod_name, node_ip, cfs_region, cfs_env, cfs_cluster):
  # tags = {"host":host[0], "port":str(host[1])}
  # NOTICE: According to oncall of metric server, setting host and dc by user
  # (sdk) is not recommended. It will cause some unpected problems.
  # But Grafana needs this.
  tags = {
    "host": host[0],
    "pod_name": pod_name,
    "node_ip": node_ip,
    "port": str(host[1]),
    "cfs_region": cfs_region,
    "cfs_env": cfs_env,
    "cfs_cluster": cfs_cluster
  }
  segs = k.split("#")
  if len(segs) == 1:
    return segs[0], tags
  for tag in segs[1:]:
    tagkv = tag.split("=")
    if len(tagkv) != 2:
      print "Invalid tagkv: {}".format(tag)
      continue
    tk = tagkv[0]
    tv = tagkv[1]
    # 处理中文
    if segs[0] == "WhiteListHitCount" and tk == "team":
      tv = base64.b16encode(tv.encode("utf8"))
    tags[tk] = tv
    metrics.define_tagkv(tk, [
      tv,
    ])
  return segs[0], tags


def run_once(hosts, pod_name, node_ip, cfs_region, cfs_env, cfs_cluster):
  start_time = time.time()
  for host in hosts:
    try:
      logging.info("Begin to send metrics from {}".format(host))
      result = get_metrics(host[0], host[1])
      if not isinstance(result, dict):
        logging.error("Unknown type of result, expected type is map.")
        continue
      for center, ms in result.iteritems():
        if "counters" not in ms:
          logging.info("No counters in {} center!".format(center))
        else:
          for k, v in ms["counters"].iteritems():
            name, tags = parse_name(k, host, pod_name, node_ip, cfs_region,
                                    cfs_env, cfs_cluster)
            metrics.define_store("{}.counters.{}".format(center, name))
            metrics.emit_store("{}.counters.{}".format(center, name),
                               v,
                               tagkv=tags)
        if "gauges" not in ms:
          logging.info("No gauges in {} center!".format(center))
        else:
          for k, v in ms["gauges"].iteritems():
            name, tags = parse_name(k, host, pod_name, node_ip, cfs_region,
                                    cfs_env, cfs_cluster)
            metrics.define_store("{}.gauges.{}".format(center, name))
            metrics.emit_store("{}.gauges.{}".format(center, name),
                               v,
                               tagkv=tags)
        if "histograms" not in ms:
          logging.info("No histograms in {} center!".format(center))
        else:
          for k, v in ms["histograms"].iteritems():
            name, tags = parse_name(k, host, pod_name, node_ip, cfs_region,
                                    cfs_env, cfs_cluster)
            for quantile, vv in ms["histograms"][k].iteritems():
              metrics.define_store("{}.histograms.{}.{}".format(
                center, name, quantile))
              metrics.emit_store("{}.histograms.{}.{}".format(
                center, name, quantile),
                                 vv,
                                 tagkv=tags)
      logging.info("Send metrics from {} succeed".format(host))
    except Exception as e:
      logging.error("Send metrics from {} failed, reason: {}".format(host, e))
  return time.time() - start_time


def sigterm_handler(_signo, _stack_frame):
  sys.exit(0)


def main():
  signal.signal(signal.SIGTERM, sigterm_handler)

  parser = parse_cmd()
  args = parser.parse_args()
  metrics_args = {}
  metrics_args["metrics_namespace_prefix"] = args.prefix
  if args.remote == True:
    metrics_args["use_remote"] = True
    metrics_args["remote_addr"] = args.addr
    metrics_args["remote_port"] = args.port
  else:
    metrics_args["use_remote"] = False
  metrics.init(metrics_args)
  host_list = args.hosts.split(",")
  hosts = []
  for h in host_list:
    t = h.split(":")
    if len(t) != 2:
      print "invalid argument: ", args.hosts
      return -1
    try:
      port = int(t[1])
    except:
      print "invalid argument: ", args.hosts
      return -1
    hosts.append((t[0], port))
    metrics.define_tagkv("host", [t[0]])
    metrics.define_tagkv("port", [t[1]])

  logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s %(levelname)s %(message)s",
    filename="{}/metrics.log".format(args.log_dir),
    filemode="w",
  )
  run(
    hosts,
    args.pod_name,
    args.node_ip,
    args.cfs_region,
    args.cfs_env,
    args.cfs_cluster,
  )


if __name__ == "__main__":
  sys.exit(main())
