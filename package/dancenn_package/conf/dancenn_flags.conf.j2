--cfs_region={{ R_DISPATCHED_CONF["CFS_REGION"] }}
--cfs_env={{ R_DISPATCHED_CONF["CFS_ENV"] }}
--cfs_cluster={{ R_DISPATCHED_CONF["CFS_CLUSTER"] }}
--deploy_dir={{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}
--nameservice={{ R_DISPATCHED_CONF["NS_NAME"] }}
--client_rpc_port={{ R_DISPATCHED_CONF["NN_CLIENT_RPC_PORT"] }}
--datanode_rpc_port={{ R_DISPATCHED_CONF["NN_DATANODE_RPC_PORT"] }}
--ha_rpc_port={{ R_DISPATCHED_CONF["NN_HA_RPC_PORT"] }}
--http_port={{ R_DISPATCHED_CONF["NN_HTTP_PORT"] }}
--client_rpc_recv_buffer_size=0
--client_rpc_send_buffer_size=0
--client_rpc_tcp_recv_buffer_size=32768
--client_rpc_tcp_send_buffer_size=32768
--client_normal_rpc_handler_count={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["CLIENT_NORMAL_RPC_HANDLER_COUNT"] | default(32) }}
--client_slow_rpc_handler_count={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["CLIENT_SLOW_RPC_HANDLER_COUNT"] | default(32) }}
--client_veryslow_rpc_handler_count={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["CLIENT_VERYSLOW_RPC_HANDLER_COUNT"] | default(8) }}
--client_veryveryslow_rpc_handler_count={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["CLIENT_VERYVERYSLOW_RPC_HANDLER_COUNT"] | default(8) }}
--client_rpc_network_thread_count=8
--rpc_in_queue_timeout_ms_block_report=600000
--ha_rpc_handler_count=1
--datanode_rpc_network_thread_count=8
--datanode_rpc_recv_buffer_size=0
--datanode_rpc_send_buffer_size=0
--datanode_rpc_tcp_recv_buffer_size=32768
--datanode_rpc_tcp_send_buffer_size=32768
--datanode_normal_rpc_handler_count={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["DATANODE_NORMAL_RPC_HANDLER_COUNT"] | default(16) }}
--datanode_slow_rpc_handler_count={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["DATANODE_SLOW_RPC_HANDLER_COUNT"] | default(8) }}
--datanode_veryslow_rpc_handler_count={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["DATANODE_VERYSLOW_RPC_HANDLER_COUNT"] | default(2) }}
--datanode_manager_thread_num=5
--retry_cache_enabled={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["RETRY_CACHE_ENABLE"] | default("true") }}
--retry_cache_expiration_time_ms={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["RETRY_CACHE_EXPIRATION_TIME_MS"] | default(480000) }}
--request_ttl_since_emit_ms={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["REQUEST_TTL_SINCE_EMIT_MS"] | default(1800000) }}
--load_local_block_to_blockmap={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["LOAD_LOCAL_BLOCK_TO_BLOCKMAP"] | default("false") }}
--blockmap_num_slice={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["BLOCKMAP_NUM_SLICE"] | default(4096) }}
--blockmap_num_bucket_each_slice={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["BLOCKMAP_NUM_BUCKET_EACH_SLICE"] | default(64) }}
--scan_one_block_map_slice_interval_s={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["SCAN_ONE_BLOCK_MAP_SLICE_INTERVAL_S"] | default(60) }}
--blockmap_invalidate_limit={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["BLOCKMAP_INVALIDATE_LIMIT"] | default(10000) }}
--blockmap_invalidate_limit_per_dn={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["BLOCKMAP_INVALIDATE_LIMIT"] | default(10000) }}
--load_persisted_block_to_blockmap={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["LOAD_PERSISTED_BLOCK_TO_BLOCKMAP"] | default("false") }}
--enable_storage_class={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["ENABLE_STORAGE_CLASS"] | default("true") }}
--namespace_meta_storage_path={{ R_DISPATCHED_CONF["DANCENN_DATA_DIR"] }}/rocksdb
--namespace_meta_storage_ckpt_path={{ R_DISPATCHED_CONF["DANCENN_DATA_DIR"] }}/rocksdb_ckpt
--filesystem_id={{ R_DISPATCHED_CONF["FS_ID"] }}
--namespace_id={{ R_DISPATCHED_CONF["NS_ID"] }}
--namespace_type={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["NS_TYPE"] }}
--hdfs_namespace_node_addr={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["HDFS_NN_ADDR"] | default("") }}
--hdfs_namespace_node_port={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["HDFS_NN_PORT"] | default(0) }}
--hdfs_user={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["HDFS_USER"] | default("") }}
--hdfs_prefix={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["HDFS_PREFIX"] | default("") }}
--hdfs_consul={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["HDFS_CONSUL"] | default("") }}
--hdfs_sec_token={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["HDFS_SEC_TOKEN"] | default("") }}
--tos_endpoint={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["TOS_VPC_ENDPOINT"] | default("https://example.com") }}
--tos_prefix={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["TOS_PREFIX"] | default("") }}
--tos_region={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["TOS_REGION"] | default("cn-beijing") }}
--tos_bucket={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["TOS_BUCKET"] | default("cloudfs") }}
--tos_suffix_salt={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["TOS_SUFFIX_SALT"] | default(R_DISPATCHED_CONF["NS_ID"]) }}
--tos_access_key_id={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["TOS_AK"] | default("") }}
--tos_secret_access_key={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["TOS_SK"] | default("") }}
--tos_sse_enabled={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["TOS_SSE_ENABLED"] | default("false") }}
--dfs_summary_min_depth={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["DFS_SUMMARY_MIN_DEPTH"] | default(10) }}
--bg_auto_compact_all_interval_in_min=5256000
--dfs_meta_storage_rocksdb_use_direct_reads={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["DFS_META_STORAGE_ROCKSDB_USE_DIRECT_READS"] | default("false") }}
--dfs_meta_storage_rocksdb_use_direct_io_for_flush_and_compaction={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["DFS_META_STORAGE_ROCKSDB_USE_DIRECT_IO_FOR_FLUSH_AND_COMPACTION"] | default("false") }}
--dfs_meta_storage_rocksdb_write_sync={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["DFS_META_STORAGE_ROCKSDB_WRITE_SYNC"] | default("false") }}
--dfs_meta_storage_rocksdb_num_levels={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["DFS_META_STORAGE_ROCKSDB_NUM_LEVELS"] | default(4) }}
--dfs_meta_storage_rocksdb_level0_file_num_compaction_triger={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["DFS_META_STORAGE_ROCKSDB_LEVEL0_FILE_NUM_COMPACTION_TRIGER"] | default(1) }}
--dfs_meta_storage_rocksdb_min_write_buffer_number_to_merge={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["DFS_META_STORAGE_ROCKSDB_MIN_WRITE_BUFFER_NUMBER_TO_MERGE"] | default(1) }}
--dfs_meta_storage_rocksdb_compression_type=1
--dfs_meta_storage_rocksdb_use_unified_block_cache={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["DFS_META_STORAGE_ROCKSDB_USE_UNIFIED_BLOCK_CACHE"] | default("true") }}
--dfs_meta_storage_rocksdb_block_cache_capacity_mb={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["DFS_META_STORAGE_ROCKSDB_BLOCK_CACHE_CAPACITY_MB"] | default(200) }}
--dfs_meta_storage_rocksdb_max_write_buffer_number={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["DFS_META_STORAGE_ROCKSDB_MAX_WRITE_BUFFER_NUMBER"] | default("64") }}
--dfs_meta_storage_rocksdb_write_buffer_size_mb={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["DFS_META_STORAGE_ROCKSDB_WRITE_BUFFER_SIZE_MB"] | default("64") }}
--dfs_meta_storage_slows={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["DFS_META_STORAGE_SLOWS"] | default(16) }}
--dfs_meta_storage_slows_mailbox={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["DFS_META_STORAGE_SLOWS_MAILBOX"] | default(409600) }}
--dfs_meta_storage_verify_before_commit={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["DFS_META_STORAGE_VERIFY_BEFORE_COMMIT"] | default("false") }}
--dfs_block_size=134217728
--dfs_replication_max=100
--dfs_replication={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["DFS_REPLICATION"] | default(2) }}
--client_replication_support={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["CLIENT_REPLICATION_SUPPORT"] | default("false") }}
--dfs_bytes_per_checksum=4096
--dfs_client_write_packet_size=65536
--dfs_encrypt_data_transfer=false
--datanode_keep_alive_timeout_sec=3600
--datanode_stale_interval_ms={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["DATANODE_STALE_INTERVAL_MS"] | default(600000) }}
--datanode_dying_interval_ms={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["DATANODE_DYING_INTERVAL_MS"] | default(10000) }}
--datanode_check_interval_sec=10
--fs_trash_interval=10080
--dfs_checksum_type=CRC32C
--log_dir={{ R_DISPATCHED_CONF["DANCENN_LOG_DIR"] }}/{{ MY_POD_NAME.split("-") | last }}/
--fsimage_dir={{ R_DISPATCHED_CONF["YARN_DATA_DIR"] }}/nndata/current
--java_heap_size_mb=2048
--java_error_file={{ R_DISPATCHED_CONF["DANCENN_LOG_DIR"] }}/{{ MY_POD_NAME.split("-") | last }}/jvm_error_%p.log
--java_heap_dump_path={{ R_DISPATCHED_CONF["DANCENN_LOG_DIR"] }}/{{ MY_POD_NAME.split("-") | last }}/
--java_gc_log_file={{ R_DISPATCHED_CONF["DANCENN_LOG_DIR"] }}/{{ MY_POD_NAME.split("-") | last }}/jvm_gc_%p.log
--metrics_prefix=inf.cfs.dancenn
--metric_emitter_plugin_type=
--metric_emitter_script_type={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["METRICS_SCRIPT_TYPE"] | default("push") }}
--metric_emitter_script_path={{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_dancenn_deploy/{{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["METRICS_SCRIPT_PATH"] | default("tools/danceproxy_metrics.py") }}
--metric_emitter_script_env={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["METRICSERVER_ENV"] | default("inner") }}
--metric_emitter_script_remote_addr={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["METRICSERVER2"] | default("127.0.0.1") }}
--metric_emitter_script_pull_port={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["METRICS_SCRIPT_PULL_PORT"] | default(5075) }}
--security_block_access_token_enable={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["SECURITY_BLOCK_ACCESS_TOKEN_ENABLE"] | default("false") }}
--expand_replica=false
--meta_storage_checkpoint_extra_retain_num=2
--bg_auto_compact_all_forbid=1
--tail_period_ms=3000
--java_zk_classpath={{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//zookeeper-jute-3.5.9.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//zookeeper-3.5.9.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//token-provider-2.0.0.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//spotbugs-annotations-3.1.9.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//slf4j-api-1.7.36.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//objenesis-2.6.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//nimbus-jose-jwt-4.41.2.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//netty-transport-native-unix-common-4.1.68.Final.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//netty-transport-native-epoll-4.1.68.Final.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//netty-transport-4.1.68.Final.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//netty-resolver-4.1.68.Final.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//netty-handler-4.1.68.Final.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//netty-common-4.1.68.Final.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//netty-codec-4.1.68.Final.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//netty-buffer-4.1.68.Final.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//mockito-core-2.27.0.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//log4j-slf4j-impl-2.17.2.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//log4j-core-2.17.2.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//log4j-api-2.17.2.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//kerby-xdr-2.0.0.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//kerby-util-2.0.0.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//kerby-pkix-2.0.0.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//kerby-config-2.0.0.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//kerby-asn1-2.0.0.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//kerb-util-2.0.0.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//kerb-simplekdc-2.0.0.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//kerb-server-2.0.0.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//kerb-identity-2.0.0.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//kerb-crypto-2.0.0.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//kerb-core-2.0.0.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//kerb-common-2.0.0.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//kerb-client-2.0.0.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//kerb-admin-2.0.0.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//junit-4.12.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//jsr305-3.0.2.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//json-smart-2.3.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//json-simple-1.1.1.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//jline-2.14.6.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//jetty-util-ajax-9.4.46.v20220331.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//jetty-util-9.4.46.v20220331.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//jetty-servlet-9.4.46.v20220331.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//jetty-server-9.4.46.v20220331.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//jetty-security-9.4.46.v20220331.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//jetty-io-9.4.46.v20220331.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//jetty-http-9.4.46.v20220331.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//jcip-annotations-1.0-1.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//javax.servlet-api-3.1.0.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//jackson-databind-********.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//jackson-core-2.10.5.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//jackson-annotations-2.10.5.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//hamcrest-core-1.3.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//hamcrest-all-1.3.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//commons-io-2.6.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//commons-collections-3.2.2.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//commons-cli-1.2.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//byte-buddy-agent-1.9.10.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//byte-buddy-1.9.10.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//bcprov-jdk15on-1.60.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//bcpkix-jdk15on-1.60.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//audience-annotations-0.5.0.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//asm-5.0.4.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/zookeeper//accessors-smart-1.2.jar:
--java_classpath={{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_dancenn_deploy/conf:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/conf:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_dancenn_deploy/jar/commons-collections4-4.1.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_dancenn_deploy/jar/netty-all-4.1.12.Final.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_dancenn_deploy/jar/bookkeeper-server-shaded-4.6.2.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_dancenn_deploy/jar/bookkeeper-stats-api-4.6.2.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_dancenn_deploy/jar/api-1.0.22.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_dancenn_deploy/jar/byted-codec-formatter-1.0.22.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_dancenn_deploy/jar/byted-config-service-1.0.22.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_dancenn_deploy/jar/byted-sdk-1.0.22.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_dancenn_deploy/jar/core-1.0.22.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_dancenn_deploy/jar/exporter-common-1.0.22.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_dancenn_deploy/jar/structured-1.0.22.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_dancenn_deploy/jar/unix-socket-exporter-1.0.22.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_dancenn_deploy/jar/byted-apm-jnr-unixsock-shade-1.0.9.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_dancenn_deploy/jar/byted-apm-vendor-1.0.9.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_dancenn_deploy/jar/byted-apm-yml-shade-1.0.9.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_dancenn_deploy/jar/config-loader-1.0.9.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_dancenn_deploy/jar/utils-1.0.9.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_dancenn_deploy/jar/vendor-1.0.9.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_dancenn_deploy/jar/protobuf-java-3.0.0.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop-2.6.0-cdh5.4.4/share/hadoop/hdfs/hadoop-hdfs-2.6.0-cdh5.4.4.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/activation-1.1.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/apacheds-i18n-2.0.0-M15.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/apacheds-kerberos-codec-2.0.0-M15.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/api-asn1-api-1.0.0-M20.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/api-util-1.0.0-M20.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/asm-3.2.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/avro-1.7.6-cdh5.4.4.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/commons-beanutils-1.9.4.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/commons-cli-1.2.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/commons-codec-1.4.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/commons-collections-3.2.2.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/commons-compress-1.21.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/commons-configuration-1.6.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/commons-digester-1.8.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/commons-el-1.0.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/commons-httpclient-3.1.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/commons-io-2.4.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/commons-lang-2.6.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/commons-logging-1.1.3.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/commons-math3-3.1.1.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/commons-net-3.1.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/curator-client-2.7.1.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/curator-framework-2.7.1.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/curator-recipes-2.7.1.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/gson-2.2.4.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/guava-11.0.2.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/hadoop-annotations-2.6.0-cdh5.4.4.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/hadoop-auth-2.6.0-cdh5.4.4.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/hamcrest-core-1.3.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/htrace-core-3.0.4.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/httpclient-4.2.5.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/httpcore-4.2.5.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/jackson-core-asl-1.8.8.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/jackson-jaxrs-1.8.8.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/jackson-mapper-asl-1.8.8.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/jackson-xc-1.8.8.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/jasper-compiler-5.5.23.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/jasper-runtime-5.5.23.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/java-xmlbuilder-0.4.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/jaxb-api-2.2.2.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/jaxb-impl-2.2.3-1.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/jersey-core-1.9.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/jersey-json-1.9.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/jersey-server-1.9.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/jets3t-0.9.0.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/jettison-1.1.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/jetty-6.1.26.cloudera.4.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/jetty-util-9.4.46.v20220331.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/jsch-0.1.54.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/jsp-api-2.1.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/jsr305-3.0.0.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/junit-4.11.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/logredactor-1.0.3.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/mockito-all-1.8.5.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/paranamer-2.3.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/protobuf-java-2.5.0.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/servlet-api-2.5.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/slf4j-api-1.7.36.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/log4j-slf4j-impl-2.17.2.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/log4j-api-2.17.2.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/log4j-core-2.17.2.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/snappy-java-1.1.2.4.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/stax-api-1.0-2.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/xmlenc-0.52.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/xz-1.0.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/lib/zookeeper-3.4.5-cdh5.4.4.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/databus4j-1.0.1.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/hadoop-brotli-0.0.1-SNAPSHOT.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/hadoop-common-2.6.0-cdh5.4.4.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/hadoop-common-2.6.0-cdh5.4.4-tests.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/hadoop-lzo-0.4.20-SNAPSHOT.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/hadoop-nfs-2.6.0-cdh5.4.4.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/hadoop-xz-1.5-byted.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/hadoop-zstd-1.0.0.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/common/json-serde-1.3-jar-with-dependencies.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/hdfs/bec.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/hdfs/hadoop-aws-2.6.0-cdh5.4.4.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/hdfs/hadoop-hdfs-2.6.0-cdh5.4.4-tests.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/hdfs/hadoop-hdfs-bkjournal-2.6.0-cdh5.4.4.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/hdfs/hadoop-hdfs-nfs-2.6.0-cdh5.4.4.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/hdfs/lib/asm-3.2.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/hdfs/lib/commons-cli-1.2.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/hdfs/lib/commons-codec-1.4.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/hdfs/lib/commons-daemon-1.0.13.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/hdfs/lib/commons-el-1.0.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/hdfs/lib/commons-io-2.4.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/hdfs/lib/commons-lang-2.6.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/hdfs/lib/commons-logging-1.1.3.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/hdfs/lib/guava-11.0.2.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/hdfs/lib/htrace-core-3.0.4.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/hdfs/lib/jackson-core-asl-1.8.8.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/hdfs/lib/jackson-mapper-asl-1.8.8.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/hdfs/lib/jasper-runtime-5.5.23.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/hdfs/lib/jersey-core-1.9.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/hdfs/lib/jersey-server-1.9.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/hdfs/lib/jetty-6.1.26.cloudera.4.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/hdfs/lib/jetty-util-9.4.46.v20220331.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/hdfs/lib/jsp-api-2.1.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/hdfs/lib/jsr305-3.0.0.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/hdfs/lib/leveldbjni-all-1.8.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/hdfs/lib/servlet-api-2.5.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/hdfs/lib/xmlenc-0.52.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/hdfs/lib/log4j-1.2-api-2.17.2.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/hdfs/lib/log4j-api-2.17.2.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop/share/hadoop/hdfs/lib/log4j-core-2.17.2.jar:{{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_hdfs_deploy/hadoop-2.6.0-cdh5.4.4/share/hadoop/yarn/hadoop-yarn-common-2.6.0-cdh5.4.4.jar
--choose_target_context_refresh_interval_ms=60000
--trusted_ip_file={{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_dancenn_deploy/conf/trusted_ip_list.txt
--max_ongoing_block_report_req_count={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["MAX_ONGOING_BLOCK_REPORT_REQ_COUNT"] | default("4") }}
--max_ongoing_block_report_req_count_hard_limit={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["MAX_ONGOING_BLOCK_REPORT_REQ_COUNT_HARD_LIMIT"] | default("8") }}
--max_ongoing_block_report_req_count_standby={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["MAX_ONGOING_BLOCK_REPORT_REQ_COUNT_STANDBY"] | default("16") }}
--blk_report_thread_count={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["BLK_REPORT_THREAD_COUNT"] | default(40) }}
--block_report_interval_sec={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["BLOCK_REPORT_INTERVAL_SEC"] | default("604800") }}
--block_report_fast_fbr_interval_sec={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["BLOCK_REPORT_FAST_FBR_INTERVAL_SEC"] | default(86400) }}
--block_report_fast_batch_size={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["BLOCK_REPORT_FAST_BATCH_SIZE"] | default(10000) }}
--block_report_batch_interval_ms={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["BLOCK_REPORT_BATCH_INTERVAL_MS"] | default("100") }}
--block_report_window_sec={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["BLOCK_REPORT_WINDOW_SEC"] | default("300") }}
--append_reuse_last_block=false
--complete_rpc_abandon_last_empty_block={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["COMPLETE_ABANDON_LAST_EMPTY_BLOCK"] | default("false") }}
--complete_rpc_use_very_slow={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["COMPLETE_RPC_USE_VERY_SLOW"] | default("true") }}
--force_check_tos_endpoint=false
--iam_account_id={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["IAM_ACCOUNT_ID"] | default("") }}
--use_fixed_ak={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["USE_FIXED_AK"] | default("true") }}
--iam_top_url={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["IAM_TOP_URL"] | default("") }}
--cfs_service_region={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["CFS_SERVICE_REGION"] | default("cn-north-1") }}
--cfs_service_ak={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["CFS_SERVICE_AK"] | default("") }}
--cfs_service_sk={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["CFS_SERVICE_SK"] | default("") }}
--assume_role_name={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["ASSUME_ROLE_NAME"] | default("ServiceRoleForCFS") }}
--extra_iam_resouces={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["EXTRA_IAM_RESOUCES"] | default("") }}
--ufs_auth_enabled={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_AUTH_ENABLED"] | default("false") }}
--ufs_auth_policy={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_AUTH_POLICY"] | default("fixed") }}
--ufs_auth_fixed_ak={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_AUTH_FIXED_AK"] | default("") }}
--ufs_auth_fixed_sk={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_AUTH_FIXED_SK"] | default("") }}
--ufs_auth_fixed_token={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_AUTH_FIXED_TOKEN"] | default("") }}
--ufs_auth_role_info={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_AUTH_ROLE_INFO"] | default("") }}
--ufs_tos_shallow_copy_enabled={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_TOS_SHALLOW_COPY_ENABLED"] | default("true") }}
--ufs_tos_shallow_copy_max_count={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_TOS_SHALLOW_COPY_MAX_COUNT"] | default("10000000") }}
--ufs_tos_shallow_copy_enabled_epoch_sec={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_TOS_SHALLOW_COPY_ENABLED_EPOCH_SEC"] | default("1717113600") }}
--token_duration_secs=43200
{% if not 'SKIP_OBSERVER_FLAG' in R_DISPATCHED_CONF["DANCENN_FLAGS"] %}
--dancenn_observe_mode_on={{
  R_DISPATCHED_CONF["NNS"]                   |
  selectattr("NAME", "equalto", MY_POD_NAME) |
  map(attribute="IS_OBSERVER")               |
  list                                       |
  first                                      |
  string                                     |
  lower
}}
{% endif %}
--dancenn_quota_redis_backends=not_existed.redis.cfs
--scan_deprecated_block_max_size=3000
--block_machine_requirement=2
--block_machine_requirement_for_read=0
--datanode_machine_file={{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_dancenn_deploy/conf_common/datanode_machine_info
--has_writable_datanode={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["HAS_WRITABLE_DATANODE"] | default("false") }}
--vmodule=*={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["V"] | default(8) }}
--stderrthreshold=3
--enable_fast_shutdown={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["ENABLE_FAST_SHUTDOWN"] | default("false") }}
--block_placement_distribution_type={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["BLOCK_PLACEMENT_DISTRIBUTION_TYPE"] | default("uniform") }}
--validate_active_write_batch_mode={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["VALIDATE_ACTIVE_WRITE_BATCH_MODE"] | default("2") }}
--safemode_check_block_cnt={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["SAFEMODE_CHECK_BLOCK_CNT"] | default("false") }}
--safemode_threshold_pct={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["SAFEMODE_THRESHOLD_PCT"] | default("0.999") }}
--safemode_check_dn_cnt={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["SAFEMODE_CHECK_DN_CNT"] | default("false") }}
--safemode_min_datanodes={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["SAFEMODE_MIN_DATANODES"] | default("3") }}
--safemode_check_safe_dn_cnt={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["SAFEMODE_CHECK_SAFE_DN_CNT"] | default("false") }}
--safemode_safe_dn_threshold_pct={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["SAFEMODE_SAFE_DN_THRESHOLD_PCT"] | default("0.99") }}
{% if not 'SKIP_OBSERVER_FLAG' in R_DISPATCHED_CONF["DANCENN_FLAGS"] and (R_DISPATCHED_CONF["NNS"] | selectattr("IS_OBSERVER", "equalto", true) | list) %}
--observer_endpoint={{
  R_DISPATCHED_CONF["NNS"]                   |
  selectattr("IS_OBSERVER", "equalto", true) |
  map(attribute="HOST_IP")                   |
  list                                       |
  first                                      |
  string
}}
{% endif %}
--nn_local_ip={{
  R_DISPATCHED_CONF["NNS"]                   |
  selectattr("NAME", "equalto", MY_POD_NAME) |
  map(attribute="HOST_IP")                   |
  list                                       |
  first                                      |
  string
}}
--usage_report_enable={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["USAGE_REPORT_ENABLE"] | default("true") }}
--usage_service_endpoint={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["USAGE_SERVICE_ENDPOINT"] | default("") }}
--usage_report_interval_seconds={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["USAGE_SERVICE_INTERVAL_SECONDS"] | default("600") }}
--permission_enabled={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["PERMISSION_ENABLED"] | default("false") }}
--permission_model={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["PERMISSION_MODEL"] | default("") }}
--ping_ranger_when_starting={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["PING_RANGER_WHEN_STARTING"] | default("false") }}
--ranger_sock_path={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["RANGER_BRIDGER_SOCKET"] | default("127.0.0.1:9911") }}
--recycle_bin_enable={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["RECYCLE_BIN_ENABLE"] | default("false") }}
--recycle_bin_scanner_enable={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["RECYCLE_BIN_SCANNER_ENABLE"] | default("true") }}
--recycle_bin_scanner_interval_sec={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["RECYCLE_BIN_SCANNER_INTERVAL_SEC"] | default("300") }}
--recycle_bin_retention_day={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["RECYCLE_BIN_RETENTION_DAY"] | default("1") }}
--replication_monitor_interval_ms=1000
--blockmap_replication_work_multiplier={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["BLOCKMAP_REPLICATION_WORK_MULTIPLIER"] | default("10000") }}
--scrub_wait_for_done_sleep_ms={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["SCRUB_WAIT_FOR_DONE_SLEEP_MS"] | default("1") }}
--lifecycle_enable={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["LIFECYCLE_ENABLE"] | default("true") }}
--lifecycle_scanner_start_next_group_period_ms={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["LIFECYCLE_SCANNER_START_NEXT_GROUP_PERIOD_MS"] | default("10800000") }}
--lifecycle_scanner_filter_depred_policy_batch_size={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["LIFECYCLE_SCANNER_FILTER_DEPRED_POLICY_BATCH_SIZE"] | default("10000") }}
--lifecycle_scrub_persist_stats_depth={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["LIFECYCLE_SCRUB_PERSIST_STATS_DEPTH"] | default("2") }}
--lifecycle_exprule_effective_upperbound_sec={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["LIFECYCLE_EXPRULE_EFFECTIVE_UPPERBOUND_SEC"] | default("-1") }}
--lifecycle_exprule_scaling_ratio={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["LIFECYCLE_EXPRULE_SCALING_RATIO"] | default("1.0") }}
--blkid_cmd_max_num_blocks={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["BLKID_CMD_MAX_NUM_BLOCKS"] | default("100") }}
--ufs_read_only={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_READ_ONLY"] | default("false") }}
--ufs_support_append={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_SUPPORT_APPEND"] | default("true") }}
--ufs_worker_thread_num={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_WORKER_THREAD_NUM"] | default("64") }}
--ufs_worker_pending_tasks_limit={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_WORKER_PENDING_TASKS_LIMIT"] | default("4096") }}
--ufs_syncengine_worker_num={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_SYNCENGINE_WORKER_NUM"] | default("64") }}
--ufs_syncengine_list_worker_num={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_SYNCENGINE_LIST_WORKER_NUM"] | default("64") }}
--ufs_syncengine_delete_worker_num={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_SYNCENGINE_DELETE_WORKER_NUM"] | default("32") }}
--ufs_syncengine_rename_worker_num={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_SYNCENGINE_RENAME_WORKER_NUM"] | default("32") }}
--ufs_syncengine_max_tasks_num={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_SYNCENGINE_MAX_TASKS_NUM"] | default("4096") }}
--ufs_event_manager_enabled={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_EVENT_MANAGER_ENABLED"] | default("false") }}
--ufs_event_consumer_count={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_EVENT_CONSUMER_COUNT"] | default(2) }}
--ufs_event_kafka_consumer_enabled={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_EVENT_KAFKA_CONSUMER_ENABLED"] | default("true") }}
--ufs_event_consumer_kafka_endpoint={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_EVENT_CONSUMER_KAFKA_ENDPOINT"] | default("") }}
--ufs_event_consumer_kafka_topic={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_EVENT_CONSUMER_KAFKA_TOPIC"] | default("") }}
--ufs_event_consumer_kafka_group_id={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_EVENT_CONSUMER_KAFKA_GROUP_ID"] | default("") }}
--ufs_event_consumer_kafka_consume_timeout_ms={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_EVENT_CONSUMER_KAFKA_CONSUME_TIMEOUT_MS"] | default("1000") }}
--ufs_event_consumer_max_batch_size={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_EVENT_CONSUMER_MAX_BATCH_SIZE"] | default("2048") }}
--ufs_event_consumer_max_batch_interval_ms={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_EVENT_CONSUMER_MAX_BATCH_INTERVAL_MS"] | default("2000") }}
--ufs_event_rmq_consumer_enabled={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_EVENT_RMQ_CONSUMER_ENABLED"] | default("false") }}
--ufs_event_rmq_consumer_endpoint={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_EVENT_RMQ_CONSUMER_ENDPOINT"] | default("false") }}
--ufs_event_rmq_consumer_topic={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_EVENT_RMQ_CONSUMER_TOPIC"] | default("false") }}
--ufs_event_rmq_consumer_group_id={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_EVENT_RMQ_CONSUMER_GROUP_ID"] | default("false") }}
--ufs_event_rmq_consumer_access_key={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_EVENT_RMQ_CONSUMER_ACCESS_KEY"] | default("false") }}
--ufs_event_rmq_consumer_secret_key={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_EVENT_RMQ_CONSUMER_SECRET_KEY"] | default("false") }}
--ufs_event_handler_count={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_EVENT_HANDLER_COUNT"] | default(8) }}
--ufs_event_handler_add_task_retry_interval_ms={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_EVENT_HANDLER_ADD_TASK_RETRY_INTERVAL_MS"] | default("10") }}
--ufs_event_handler_sync_retry_time={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_EVENT_HANDLER_SYNC_RETRY_TIME"] | default(5) }}
--ufs_event_handler_sync_retry_interval_ms={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_EVENT_HANDLER_SYNC_RETRY_INTERVAL_MS"] | default(1000) }}
--tos_event_key_prefix_blacklist_enabled={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["TOS_EVENT_KEY_PREFIX_BLACKLIST_ENABLED"] | default("false") }}
--tos_event_key_prefix_blacklist={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["TOS_EVENT_KEY_PREFIX_BLACKLIST"] | default("") }}
--write_back_manager_scan_task_interval_ms={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["WRITE_BACK_MANAGER_SCAN_TASK_INTERVAL_MS"] | default("10000") }}
--enable_write_back_task_persistence={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["ENABLE_WRITE_BACK_TASK_PERSISTENCE"] | default("false") }}
--force_rebuild_write_back_task_db={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["FORCE_REBUILD_WRITE_BACK_TASK_DB"] | default("false") }}
--write_back_task_v2_trigger_interval_us={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["WRITE_BACK_TASK_V2_TRIGGER_INTERVAL_US"] | default("3000000") }}
--write_back_task_v2_max_scan_num={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["WRITE_BACK_TASK_V2_MAX_SCAN_NUM"] | default("1000000000") }}
--enable_lease_persistence={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["ENABLE_LEASE_PERSISTENCE"] | default("true") }}
--force_rebuild_lease_db={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["FORCE_REBUILD_LEASE_DB"] | default("false") }}
--enable_recover_lease_no_close_file={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["ENABLE_RECOVER_LEASE_NO_CLOSE_FILE"] | default("true") }}
--force_rebuild_policy_db={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["FORCE_REBUILD_POLICY_DB"] | default("false") }}
--datanode_info_bg_dump_enable={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["DATANODE_INFO_BG_DUMP_ENABLE"] | default("true") }}
--max_component_length={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["MAX_COMPONENT_LENGTH"] | default("255") }}
--max_path_length={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["MAX_PATH_LENGTH"] | default("8000") }}
--max_path_depth={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["MAX_PATH_DEPTH"] | default("1000") }}
--default_storage_class={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["DEFAULT_STORAGE_CLASS"] | default("WARM") }}
--dfs_meta_scanner_v2_scan_worker_num={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["DFS_META_SCANNER_V2_SCAN_WORKER_NUM"] | default(4) }}
--dfs_meta_scanner_v2_worker_num={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["DFS_META_SCANNER_V2_WORKER_NUM"] | default(64) }}
--dfs_meta_scan_use_bfs={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["DFS_META_SCAN_USE_BFS"] | default("false") }}
--dfs_meta_scan_interval_sec={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["DFS_META_SCAN_INTERVAL_SEC"] | default("86400") }}
--bg_deletion_process_pending_delete_interval_sec={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["BG_DELETION_PROCESS_PENDING_DELETE_INTERVAL_SEC"] | default(60) }}
--bg_deletion_batch_remove_inode_threshold={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["BG_DELETION_BATCH_REMOVE_INODE_THRESHOLD"] | default(10000) }}
--bg_deletion_batch_remove_block_threshold={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["BG_DELETION_BATCH_REMOVE_BLOCK_THRESHOLD"] | default(10000) }}
--bg_deletion_batch_remove_sleep_ms={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["BG_DELETION_BATCH_REMOVE_SLEEP_MS"] | default(500) }}
--enable_aws_trace_log={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["ENABLE_AWS_TRACE_LOG"] | default("false") }}
--load_compatible_dn_version={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["LOAD_COMPATIBLE_DN_VERSION"] | default("10500") }}
--managed_job_block_task_exec_timeout_ms={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["MANAGED_JOB_BLOCK_TASK_EXEC_TIMEOUT_MS"] | default("43200000") }}
--managed_task_load_max_retry_times={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["MANAGED_TASK_LOAD_MAX_RETRY_TIMES"] | default("200") }}
--edit_log_assigner_max_num_pending_tasks={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["EDIT_LOG_ASSIGNER_MAX_NUM_PENDING_TASKS"] | default("1000000") }}
--edit_log_assigner_add_task_retry_sleep_ms={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["EDIT_LOG_ASSIGNER_ADD_TASK_RETRY_SLEEP_MS"] | default("10") }}
--edit_log_assigner_apply_mode={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["EDIT_LOG_ASSIGNER_APPLY_MODE"] | default("0") }}
--edit_log_logical_apply_check_physical_log_enable={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["EDIT_LOG_LOGICAL_APPLY_CHECK_PHYSICAL_LOG_ENABLE"] | default("true") }}
--edit_log_physical_apply_check_db_enable={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["EDIT_LOG_PHYSICAL_APPLY_CHECK_DB_ENABLE"] | default("true") }}
--edit_log_applyer_wait_no_pending_sleep_us={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["EDIT_LOG_APPLYER_WAIT_NO_PENDING_SLEEP_US"] | default("100") }}
--inode_stat_check_during_startup={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["INODE_STAT_CHECK_DURING_STARTUP"] | default("false") }}
--inode_stat_delta_cache_gc_interval_ms={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["INODE_STAT_DELTA_CACHE_GC_INTERVAL_MS"] | default("1000") }}
--non_ha_mode_trigger_file={{ R_DISPATCHED_CONF["DANCENN_DATA_DIR"] }}/run-nn-non-ha
--placement_ignore_local_az={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["PLACEMENT_IGNORE_LOCAL_AZ"] | default("true") }}
--placement_ignore_existed_switch={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["PLACEMENT_IGNORE_EXISTED_SWITCH"] | default("true") }}
--placement_ignore_existed_host={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["PLACEMENT_IGNORE_EXISTED_HOST"] | default("false") }}
--upload_cmds_max_size={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UPLOAD_CMDS_MAX_SIZE"] | default("1000") }}
--ne_cmds_max_size={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["NE_CMDS_MAX_SIZE"] | default("8192") }}
--merge_cmds_max_size={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["MERGE_CMDS_MAX_SIZE"] | default("100") }}
--lease_inline_gc_all_slices={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["LEASE_INLINE_GC_ALL_SLICES"] | default("true") }}
--trace_logger_type={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["TRACE_LOGGER_TYPE"] | default("rotating_log") }}
--enable_no_upload_policy_dir_convert={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["ENABLE_NO_UPLOAD_POLICY_DIR_CONVERT"] | default("false") }}
--precheck_before_convert_noupload_to_local={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["PRECHECK_BEFORE_CONVERT_NOUPLOAD_TO_LOCAL"] | default("true") }}
--default_sync_policy={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["DEFAULT_SYNC_POLICY"] | default("false") }}
--convert_noupload_dirs_recursive={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["CONVERT_NOUPLOAD_DIRS_RECURSIVE"] | default("false") }}
--allow_load_in_local_acc_mode={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["ALLOW_LOAD_IN_LOCAL_ACC_MODE"] | default("false") }}
--allow_free_in_local_acc_mode={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["ALLOW_FREE_IN_LOCAL_ACC_MODE"] | default("false") }}
--enable_ufs_sync_in_rename={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["ENABLE_UFS_SYNC_IN_RENAME"] | default("true") }}
--enable_ufs_sync_in_operation={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["ENABLE_UFS_SYNC_IN_OPERATION"] | default("true") }}
--lifecycle_enable_atime_ttl={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["LIFECYCLE_ENABLE_ATIME_TTL"] | default("true") }}
--ttl_atime_collector_batch_size={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["TTL_ATIME_COLLECTOR_BATCH_SIZE"] | default("1000") }}
--ttl_atime_collector_timeout_ms={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["TTL_ATIME_COLLECTOR_TIMEOUT_MS"] | default("100") }}
--ttl_atime_collector_interval_ms={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["TTL_ATIME_COLLECTOR_INTERVAL_MS"] | default("100") }}
--ttl_atime_cleanup_interval_sec={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["TTL_ATIME_CLEANUP_INTERVAL_SEC"] | default("21600") }}

{# for deployment_type 'single-AZ' -#}
{% if 'DEPLOYMENT_TYPE' not in R_DISPATCHED_CONF or R_DISPATCHED_CONF["DEPLOYMENT_TYPE"] == 'single-AZ' -%}
--block_placement_policy={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["BLOCK_PLACEMENT_POLICY"] | default("cfs-default") }}
--dc_topology_file={{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_dancenn_deploy/{{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["DC_TOPOLOGY_FILE"] | default("conf/datacenters_topology.conf") }}
--network_location_file={{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_dancenn_deploy/{{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["NETWORK_LOCATION_FILE"] | default("conf/network_mapping.conf") }}
--all_datacenters={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["ALL_DATACENTERS"] | default("") }}
--storage_policy_default_id={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["STORAGE_POLICY_ID"] | default(12) }}
--replica_policy_default_id={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["REPLICA_POLICY_ID"] | default(0) }}
--default_distributed_dc={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["DISTRIBUTED_DC"] | default("") }}
--az_monitor_enable={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["AZ_MONITOR_ENABLE"] | default("false") }}
--az_monitor_refresh_interval_ms={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["AZ_MONITOR_REFRESH_INTERVAL_MS"] | default("5000") }}
{% endif -%}

{# for deployment_type 'multi-AZ' -#}
{% if 'DEPLOYMENT_TYPE' in R_DISPATCHED_CONF and R_DISPATCHED_CONF["DEPLOYMENT_TYPE"] == 'multi-AZ' -%}
--block_placement_policy={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["BLOCK_PLACEMENT_POLICY"] | default("cfs-multi-dc") }}
--dc_topology_file={{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_dancenn_deploy/{{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["DC_TOPOLOGY_FILE"] | default("conf/datacenters_topology.conf") }}
--network_location_file={{ R_DISPATCHED_CONF["DEPLOY_DIR"] }}/cfs_dancenn_deploy/{{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["NETWORK_LOCATION_FILE"] | default("conf/network_mapping.conf") }}
--all_datacenters={% for item in R_DISPATCHED_CONF["AZ_INFO"] %}{{ item.AZ_NAME }}{% if not loop.last %},{% endif %}{% endfor %}
--storage_policy_default_id={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["STORAGE_POLICY_ID"] | default(13) }}
--replica_policy_default_id={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["REPLICA_POLICY_ID"] | default(1) }}
--default_distributed_dc={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["DISTRIBUTED_DC"] | default("") }}
--az_monitor_enable={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["AZ_MONITOR_ENABLE"] | default("true") }}
--az_monitor_refresh_interval_ms={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["AZ_MONITOR_REFRESH_INTERVAL_MS"] | default("5000") }}
{% endif -%}

--listen_ip_address={{ R_DISPATCHED_CONF["LISTEN_IP_ADDRESS"] | default(MY_POD_IPV6 | default("0.0.0.0")) }}
--min_upload_timeout_s={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["MIN_UPLOAD_TIMEOUT_S"] | default("600") }}
--max_upload_timeout_s={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["MAX_UPLOAD_TIMEOUT_S"] | default("21600") }}
--ufs_sync_min_interval={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_SYNC_MIN_INTERVAL"] | default("30") }}
--ufs_delete_for_overwrite={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_DELETE_FOR_OVERWRITE"] | default("true") }}
--ufs_delete_for_overwrite_appendable_file={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_DELETE_FOR_OVERWRITE_APPENDABLE_FILE"] | default("true") }}
--ufs_sync_for_overwrite_conflict={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_SYNC_FOR_OVERWRITE_CONFLICT"] | default("true") }}
--ufs_sync_for_overwrite_conflict_appendable_file={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_SYNC_FOR_OVERWRITE_CONFLICT_APPENDABLE_FILE"] | default("true") }}
--ufs_evict_append_object_max_block_cnt_when_persist={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_EVICT_APPEND_OBJECT_MAX_BLOCK_CNT_WHEN_PERSIST"] | default("10") }}
--block_ignore_not_exist_fatal={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["BLOCK_IGNORE_NOT_EXIST_FATAL"] | default("false") }}
--merge_block_support_acc_persisted_blocks={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["MERGE_BLOCK_SUPPORT_ACC_PERSISTED_BLOCKS"] | default("true") }}
--merge_block_support_acc_non_persisted_blocks={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["MERGE_BLOCK_SUPPORT_ACC_NON_PERSISTED_BLOCKS"] | default("false") }}
--ufs_rename_update_local_etag={{ R_DISPATCHED_CONF["DANCENN_FLAGS"]["UFS_RENAME_UPDATE_LOCAL_ETAG"] | default("true") }}

{% if not 'SKIP_TEMP_FLAGFILE' in R_DISPATCHED_CONF["DANCENN_FLAGS"] %}
--flagfile=/dancenn-temporary-flagfile/flagfile
{% endif %}
{% for item in R_DISPATCHED_CONF["NN_CONF_EXTRAS"] | default("") %}--{{ item }}={{ R_DISPATCHED_CONF["NN_CONF_EXTRAS"][item] }}
{% endfor %}
