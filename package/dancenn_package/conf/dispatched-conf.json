{"VERSION_ID": "2022-10-09-v2", "CFS_REGION": "", "CFS_ENV": "", "CFS_CLUSTER": "", "FS_ID": -1, "FS_NAME": "", "CLUSTER_ID": "", "NS_ID": -1, "NS_NAME": "", "NNS": [{"NAME": "cfs-namenode-hdfs-<ns_id>-0", "HOST_IP": "", "IS_OBSERVER": false, "BOOTSTRAP_AS_ACTIVE": true, "LAST_ONE_TO_DELETE": true}, {"NAME": "cfs-namenode-hdfs-<ns_id>-1", "HOST_IP": "", "IS_OBSERVER": false, "BOOTSTRAP_AS_ACTIVE": false, "LAST_ONE_TO_DELETE": false}], "DEPLOY_DIR": "/opt/tiger", "DANCENN_DATA_DIR": "/data00/dancenn_data", "DANCENN_LOG_DIR": "/var/log/tiger/dancenn_logs", "YARN_DATA_DIR": "/data00/yarn_data", "YARN_LOG_DIR": "/var/log/tiger/yarn_logs", "SHARINNGANN_PORT": 8090, "NEED_BOOTSTRAP": true, "HADOOP_DEV_FLAGS": {"ZK_ADDR": "", "ZK_ADDR_FOR_BK": "", "BK_LEDGERS": "/bk/ledgers", "BK_AVAILABLEBOOKIES": "/bk/ledgers/available"}, "DANCENN_FLAGS": {"HDFS_NN_ADDR": "", "HDFS_NN_PORT": 65212, "HDFS_USER": "hdfs_user", "HDFS_PREFIX": "", "HDFS_CONSUL": "", "IAM_TOP_URL": "", "IAM_ACCOUNT_ID": "", "CFS_SERVICE_AK": null, "CFS_SERVICE_SK": null, "ASSUME_ROLE_NAME": null, "TOS_ENDPOINT": "", "TOS_VPC_ENDPOINT": "", "TOS_INNER_ENDPOINT": "", "TOS_REGION": "", "TOS_BUCKET": "", "TOS_PREFIX": "", "TOS_SSE_ENABLED": "false", "TOS_SUFFIX_SALT": 8657420359196522496, "USE_FIXED_AK": "true", "TOS_AK": "", "TOS_SK": "", "TOS_STS_TOKEN": null, "NS_TYPE": 4, "RECYCLE_BIN_DEFAULT_POLICY_TIME_SEC": 0, "RECYCLE_BIN_ENABLE": "false", "RECYCLE_BIN_LISTENER_ENABLE": "false", "METRICS_SCRIPT_TYPE": "push", "METRICSERVER_ENV": "vpc", "VALIDATE_ACTIVE_WRITE_BATCH_MODE": 0, "PERMISSION_MODEL": "posix", "DFS_REPLICATION": 2, "BLOCK_PLACEMENT_POLICY": "cfs-default", "USAGE_SERVICE_ENDPOINT": "InvalidPassword", "USAGE_SERVICE_INTERVAL_SECONDS": 60, "USE_NEW_EDIT_LOG_OP": "true", "BLOCKMAP_NUM_SLICE": 32768, "BLOCKMAP_NUM_BUCKET_EACH_SLICE": 4096, "SCAN_ONE_BLOCK_MAP_SLICE_INTERVAL_S": 86400, "BLK_REPORT_THREAD_COUNT": 256, "FORCE_BLOCK_REPORT_AFTER_FAILOVER": "false", "DFS_META_STORAGE_ROCKSDB_BLOCK_CACHE_CAPACITY_MB": 8000, "DFS_META_STORAGE_ROCKSDB_BLOCK_CACHE_CAPACITY_FOR_BIP_MB": 8000, "DFS_META_STORAGE_ROCKSDB_MAX_WRITE_BUFFER_NUMBER": 64, "DFS_META_STORAGE_ROCKSDB_WRITE_BUFFER_SIZE_MB": 64, "MAX_ONGOING_BLOCK_REPORT_REQ_COUNT": 10, "FORCE_BLOCK_REPORT_AFTER_RESTART": "false", "UFS_SYNCENGINE_WORKER_NUM": 128, "UFS_SYNCENGINE_MAX_TASKS_NUM": 50000, "UFS_READ_ONLY": "false", "UFS_EVENT_MANAGER_ENABLED": "false", "INODE_KEY_V2": "true"}, "NN_CLIENT_RPC_PORT": 5060, "NN_DATANODE_RPC_PORT": 5061, "NN_HA_RPC_PORT": 5062, "NN_HTTP_PORT": 5070, "ZKFC_PORT": 8019, "METRICSERVER2": "", "NN_TRASH_VIRTUAL_ENDPOINT": null, "NN_TRASH_PREFIX": null, "ADMIN_SERVICE_ENDPOINT": "", "AGENT_DEPLOY_DIR": "/opt/tiger", "AGENT_LOGGING": {"OUTPUT": "/var/log/tiger/cfs_agent", "LEVEL": "info"}}