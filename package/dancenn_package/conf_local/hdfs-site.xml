<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="configuration.xsl"?>
<!--
  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. See accompanying LICENSE file.
-->

<!-- Put site-specific property overrides in this file. -->

<configuration>
	<!--
    <property>
        <name>dfs.namenode.shared.edits.dir</name>
        <value>bookkeeper://10.8.174.163:2181/bk_nn_dancenn</value>
    </property>

    <property>
        <name>dfs.namenode.edits.journal-plugin.bookkeeper</name>
        <value>org.apache.hadoop.contrib.bkjournal.BookKeeperJournalManager</value>
    </property>
    <property>
        <name>dfs.namenode.bookkeeperjournal.zk.ledgers</name>
        <value>/bookkeeper/ledgers</value>
    </property>
    <property>
        <name>dfs.namenode.bookkeeperjournal.zk.availablebookies</name>
        <value>/bookkeeper/ledgers/available</value>
    </property>
    <property>
        <name>dfs.namenode.bookkeeperjournal.zk.session.timeout</name>
        <value>600000</value>
    </property>
    -->

    <property>
        <name>dfs.namenode.shared.edits.dir</name>
        <value>/tmp/dancenn/nndata</value>
    </property>

    <property>
        <name>dfs.namenode.edits.dir</name>
        <value>/tmp/dancenn/nndata</value>
    </property>
    <property>
        <name>dfs.name.dir</name>
        <value>file:///tmp/dancenn/nndata</value>
        <final>true</final>
    </property>

    <property>
        <name>dfs.namenode.bookkeeperjournal.ensemble-size</name>
        <value>1</value>
    </property>
    <property>
        <name>dfs.namenode.bookkeeperjournal.quorum-size</name>
        <value>1</value>
    </property>
    <property>
        <name>dfs.namenode.bookkeeperjournal.ack-quorum-size</name>
        <value>1</value>
    </property>
    <property>
        <name>dfs.nameservice.id</name>
        <value>testbackend</value>
    </property>
    <property>
        <name>dfs.ha.namenode.id</name>
        <value>test1</value>
    </property>
    <property>
        <name>dfs.ha.namenodes.testbackend</name>
        <value>test1,test2</value>
    </property>
    <property>
        <name>dfs.namenode.http-address.testbackend.test1</name>
        <value>********:5070</value>
    </property>
    <property>
        <name>dfs.namenode.http-address.testbackend.test2</name>
        <value>********:5070</value>
    </property>
</configuration>
