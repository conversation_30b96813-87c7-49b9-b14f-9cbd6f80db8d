#!/usr/bin/env python3

import os
import sys
import time
from kazoo.client import KazooClient

def run_check(zk_servers: str, available_bookie_path: str, bookie_count: int):
    try:
        zk = KazooClient(hosts=zk_servers)
        zk.start()
        servers = zk.get_children(available_bookie_path)
        print("Available bk servers: {}".format(servers))

        if bookie_count + 1 > len(servers):
            raise RuntimeError("The AVAIABLE BOOKIE count is not enough for NN to start up.")
    finally:
        if zk != None:
            zk.stop()

if __name__ == "__main__":
    if len(sys.argv) < 4:
        print("Invalid paramters.")
        print("Usage: wait_bk_ready.py <zk_servers> <available_bookie_path> <bookie_count>")
        print("    Format of zk_servers: zk1:2181,zk2:2181,zk3:2181")
    zk_servers = sys.argv[1]
    available_bookie_path = sys.argv[2]
    bookie_count = int(sys.argv[3])

    while True:
        print("Will check if bk is ready: {}".format(zk_servers))
        try:
            run_check(zk_servers, available_bookie_path, bookie_count)
            print("Check bk ready successfully")
            break
        except:
            print("Failed to check bk. Sleep and retry.")
            time.sleep(2)
