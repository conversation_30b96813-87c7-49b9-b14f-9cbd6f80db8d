#!/bin/bash

set -uxo pipefail

RAW_CONF=$(sed -Ez 's/[[:space:]]*//g' /opt/tiger/conf/config-nn.json)

NNS=$(echo "$RAW_CONF" | sed -E 's/.*NNS[^[]*\[([^]]+)\].*/\1/')
declare -a NNS_ADDR=($(echo "$NNS" | sed -E 's/,/,\n/g' | grep '"HOST_IP"' | sed -E 's/.*"HOST_IP":"([^"]+)".*/\1/'))
declare -a NNS_NAME=($(echo "$NNS" | sed -E 's/,/,\n/g' | grep '"NAME"' | sed -E 's/.*"NAME":"([^"]+)".*/\1/'))
MY_POD_IDX=$(echo "$MY_POD_NAME" | sed -E 's/[a-z0-9-]*-([0-9]+)/\1/')
MY_NN_ADDR=${NNS_ADDR[$MY_POD_IDX]}
MY_NN_NAME=${NNS_NAME[$MY_POD_IDX]}

HTTP_PORT=$(echo "$RAW_CONF" | sed -E 's/.*"NN_HTTP_PORT":([0-9]+)[^0-9].*/\1/')
RPC_PORT=$(echo "$RAW_CONF" | sed -E 's/.*"NN_HA_RPC_PORT":([0-9]+)[^0-9].*/\1/')

FS_ID=$(echo "$RAW_CONF" | sed -E 's/.*"FS_ID":([0-9]+)[^0-9].*/\1/')
NS_NAME=$(echo "$RAW_CONF" | sed -E 's/.*"NS_NAME":"([^"]+)".*/\1/')
NS_ID=$(echo "$RAW_CONF" | sed -E 's/.*"NS_ID":([0-9]+)[^0-9].*/\1/')

if [ "$HTTP_PORT" == "" ] || [ "$RPC_PORT" == "" ]; then
  echo "$(date) Cannot get HTTP_PORT or RPC_PORT"
  rm -rf /data00/yarn_data/pid/hadoop-tiger-zkfc.pid
  pkill sh
  exit 1
fi

while true
do
  STATUS="$(curl -s "localhost:${HTTP_PORT}/status")" || STATUS=""
  HA_STATE=$(echo $STATUS | sed -E 's/.*"ha_state":"([^"]+)".*/\1/')
  if [ "$HA_STATE" = "ACTIVE" ]; then
    NN_INDEX=0
    while [ $NN_INDEX -lt 3 ]
    do
      NN=${NNS_ADDR[$NN_INDEX]}
      STATUS=$(curl -s "${NN}:${HTTP_PORT}/status") || STATUS=""
      HA_STATE=$(echo $STATUS | sed -E 's/.*"ha_state":"([^"]+)".*/\1/')
      if [ "$HA_STATE" = "STANDBY" ]; then
        NN_NAME=${NNS_NAME[$NN_INDEX]}
        if /opt/tiger/cfs_hdfs_deploy/hadoop/bin/hdfs haadmin -failover \
          -conf /opt/tiger/cfs_hdfs_deploy/hadoop/conf/hdfs-site.xml \
          -fsid $FS_ID \
          -ns $NS_NAME \
          -nsid $NS_ID \
          -fromnode $MY_NN_NAME \
          -fromaddr /${MY_NN_ADDR}:${RPC_PORT} \
          -tonode $NN_NAME \
          -toaddr /${NN}:${RPC_PORT}; then
          echo "$(date) Switch ACTIVE to ${NN} success"
          rm -rf /data00/yarn_data/pid/hadoop-tiger-zkfc.pid
          pkill sh
          exit 0
        else
          echo "$(date) Switch ACTIVE to ${NN} failed"
          NN_INDEX=0
          sleep 10
        fi
      else
        NN_INDEX=$(($NN_INDEX + 1))
      fi
    done
    echo "$(date) No STANDBY NN in cluster, giveup failover"
    rm -rf /data00/yarn_data/pid/hadoop-tiger-zkfc.pid
    pkill sh
    exit 1
  elif [ "$HA_STATE" = "STANDBY" ] || [ "$HA_STATE" = "OBSERVER" ]; then
    echo "$(date) Current HA_STATE is not ACTIVE"
    rm -rf /data00/yarn_data/pid/hadoop-tiger-zkfc.pid
    pkill sh
    exit 0
  else
    echo "$(date) Failed to get status or ha_state of current NN"
    exit 1
  fi
done
