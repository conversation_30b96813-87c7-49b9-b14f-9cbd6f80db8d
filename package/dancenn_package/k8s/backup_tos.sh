#!/bin/bash

set -e
set -x

SCRIPTROOT=$(cd $(dirname ${BASH_SOURCE:-$0}); pwd)

if [[ -z "${MY_POD_NAME}" ]]; then
  echo "MY_POD_NAME not specified."
  exit 1
fi

if [[ -z "${NN_CONF_FILE}" ]]; then
  echo "NN_CONF_FILE not specified."
  exit 1
fi

if [[ ! -f "${NN_CONF_FILE}" ]]; then
  echo "${NN_CONF_FILE} not found."
  exit 1
fi

function get_json_conf() {
  export __JSON_CONF_KEY=$2
  cat $1 | python3 -c 'import os,sys,json; \
    json_conf=json.load(sys.stdin); \
    key=os.environ["__JSON_CONF_KEY"]; \
    print(json_conf[key]) if key in json_conf else 0'
}

NS_NAME=$(get_json_conf ${NN_CONF_FILE} NS_NAME)
DANCENN_DATA_DIR=$(get_json_conf ${NN_CONF_FILE} DANCENN_DATA_DIR)
YARN_DATA_DIR=$(get_json_conf ${NN_CONF_FILE} YARN_DATA_DIR)
NN_TRASH_VIRTUAL_ENDPOINT=$(get_json_conf ${NN_CONF_FILE} NN_TRASH_VIRTUAL_ENDPOINT)
NN_TRASH_PREFIX=$(get_json_conf ${NN_CONF_FILE} NN_TRASH_PREFIX)
echo "Info: ns_name: ${NS_NAME}, my_pod_name: ${MY_POD_NAME}"
echo "Will backup ${DANCENN_DATA_DIR} and ${YARN_DATA_DIR} to TOS, virtual endpoint: ${NN_TRASH_VIRTUAL_ENDPOINT}, prefix: ${NN_TRASH_PREFIX}"

ts="$(date +%s)"
tar -czvf "${DANCENN_DATA_DIR}.${ts}.tar.gz" "${DANCENN_DATA_DIR}"
aws s3 --endpoint="${NN_TRASH_VIRTUAL_ENDPOINT}" cp "${DANCENN_DATA_DIR}.${ts}.tar.gz" "s3://${NN_TRASH_PREFIX}/${NS_NAME}/${MY_POD_NAME}${DANCENN_DATA_DIR}.${ts}.tar.gz"
tar -czvf "${YARN_DATA_DIR}.${ts}.tar.gz" "${YARN_DATA_DIR}"
aws s3 --endpoint="${NN_TRASH_VIRTUAL_ENDPOINT}" cp "${YARN_DATA_DIR}.${ts}.tar.gz" "s3://${NN_TRASH_PREFIX}/${NS_NAME}/${MY_POD_NAME}${YARN_DATA_DIR}.${ts}.tar.gz"

echo "Buckup succeeded."
