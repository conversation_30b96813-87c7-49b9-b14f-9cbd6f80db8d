#!/bin/bash

set -x
SCRIPT_DIR=$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )

export CLASSPATH=${SCRIPT_DIR}/../conf_bridger:${SCRIPT_DIR}/../jar:$CLASSPATH
export JAR_DIR=${SCRIPT_DIR}/../jar
export CONF_DIR=${SCRIPT_DIR}/../conf_bridger
pushd ${SCRIPT_DIR}/../conf_bridger/
if [[ -z "RANGER_CONF" ]]; then
export RANGER_CONF=jsonfile:///conf/config-nn.json
fi
e2j2
popd

BRIDGER_JAR=$(ls ${JAR_DIR}/cfs-ranger-bridger*.jar)

java -cp "${BRIDGER_JAR}:${CONF_DIR}/" com.bytedance.server.BridgerNettyServer
