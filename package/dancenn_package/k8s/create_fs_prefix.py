#!/usr/bin/env python3

import os
import sys
import time
import kazoo
from kazoo.client import KazooClient

def run(zk_servers: str, fs_prefix: str):
    try:
        zk = KazooClient(hosts=zk_servers)
        zk.start()
        if not zk.exists(fs_prefix):
            print("Create {}".format(fs_prefix))
            zk.create(fs_prefix)
    finally:
        if zk != None:
            zk.stop()

if __name__ == "__main__":
    if len(sys.argv) < 3:
        print("Invalid paramters.")
        print("Usage: create_fs_prefix.py <zk_servers> <fs_prefix>")
        print("    Format of zk_servers: zk1:2181,zk2:2181,zk3:2181")
    zk_servers = sys.argv[1]
    fs_prefix = sys.argv[2]

    while True:
        print("Will create fs prefix: {}, zk: {}".format(fs_prefix, zk_servers))
        try:
            run(zk_servers, fs_prefix)
            print("Create prefix successfully")
            break
        except:
            print("Failed to create prefix. Sleep and retry.")
            time.sleep(2)
