#!/bin/bash

set -euo pipefail

RAW_CONF=$(sed -Ez 's/[[:space:]]*//g' $NN_CONF_FILE)
HTTP_PORT=$(echo "$RAW_CONF" | sed -E 's/.*"NN_HTTP_PORT":([0-9]+)[^0-9].*/\1/')

if [ "$HTTP_PORT" != "" ]; then
  STATUS=$(curl -s "localhost:$HTTP_PORT/status")
  HA_STATE=$(sed -E 's/.*"ha_state":"([^"]+)".*/\1/' <<<"$STATUS")
else
  echo "$(date) check ready failed"
  exit 1
fi

if [ "$HA_STATE" = "ACTIVE" ] || [ "$HA_STATE" = "STANDBY" ]; then
  SAFEMODE=$(sed -E 's/.*"safemode":"([^"]+)".*/\1/' <<<"$STATUS")
  if [ "$SAFEMODE" = "Safe mode is OFF." ]; then
    exit 0
  else
    echo "$(date) check ready failed"
    exit 1
  fi
else
  if [ "$HA_STATE" = "OBSERVER" ]; then
    exit 0
  else
    echo "$(date) check ready failed"
    exit 1
  fi
fi
