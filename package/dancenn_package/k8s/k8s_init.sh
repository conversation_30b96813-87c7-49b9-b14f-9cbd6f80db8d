#!/bin/bash

set -e
set -x

SCRIPTROOT=$(cd $(dirname ${BASH_SOURCE:-$0}); pwd)

if [[ -z "${MY_POD_NAME}" ]]; then
  echo "MY_POD_NAME not specified."
  exit 1
fi

if [[ -z "${NN_CONF_FILE}" ]]; then
  echo "NN_CONF_FILE not specified."
  exit 1
fi

if [[ ! -f "${NN_CONF_FILE}" ]]; then
  echo "${NN_CONF_FILE} not found."
  exit 1
fi

function get_json_conf() {
  export __JSON_CONF_KEY=$2
  cat $1 | python3 -c 'import os, sys, json; print(json.load(sys.stdin)[os.environ["__JSON_CONF_KEY"]])'
}

MY_POD_INDEX=${MY_POD_NAME##*-}

# Render conf file
REAL_NN_CONF_DIR=/opt/tiger/conf
REAL_NN_CONF_FILE=${REAL_NN_CONF_DIR}/config-nn.json
mkdir -p ${REAL_NN_CONF_DIR}
${SCRIPTROOT}/render.py ${NN_CONF_FILE} ${REAL_NN_CONF_FILE} ${MY_POD_INDEX}

ZK_ADDR=$(get_json_conf ${REAL_NN_CONF_FILE} ZK_ADDR)

# Wait bk ready
if [[ -n ${BK_COUNT} ]]; then
  # If env BK_COUNT is specified, this script will check BKs' readiness.
  BK_AVAILABLE_BOOKIES=$(get_json_conf ${REAL_NN_CONF_FILE} BK_AVAILABLEBOOKIES)
  ${SCRIPTROOT}/wait_bk_ready.py ${ZK_ADDR} ${BK_AVAILABLE_BOOKIES} ${BK_COUNT}
fi

if [[ ${MY_POD_INDEX} -eq 0 ]]; then
  # If this is an active NN (pod 0), it will create fs prefix in ZK if not existed.
  FS_NAME=$(get_json_conf ${REAL_NN_CONF_FILE} FS_NAME)
  FS_ID=$(get_json_conf ${REAL_NN_CONF_FILE} FS_ID)
  FS_PREFIX=cfs-${FS_NAME}-${FS_ID}
  ${SCRIPTROOT}/create_fs_prefix.py ${ZK_ADDR} ${FS_PREFIX}
else
  # If this is not an active NN (not pod 0), it will check local nndata and rocksdb
  # If neither nndata nor rocksdb exists, it will wait for the active NN to be ready.
  NNDATA_DIR=$(get_json_conf ${REAL_NN_CONF_FILE} YARN_DATA_DIR)/nndata/current
  ROCKSDB_DIR=$(get_json_conf ${REAL_NN_CONF_FILE} DANCENN_DATA_DIR)/rocksdb
  if [[ ! -d ${NNDATA_DIR} && ! -d ${ROCKSDB_DIR} ]]; then
    ACTIVE_NN_IP=$(python -c "import json; conf = json.load(open('${REAL_NN_CONF_FILE}')); print(filter(lambda x: x['BOOTSTRAP_AS_ACTIVE'], conf['NNS'])[0]['HOST_IP'])")
    NN_HTTP_PORT=$(get_json_conf ${REAL_NN_CONF_FILE} NN_HTTP_PORT)
    ${SCRIPTROOT}/wait_nn_ready.py ${ACTIVE_NN_IP}:${NN_HTTP_PORT}
  fi
fi
