#!/usr/bin/python3

import json
import sys


def main():
    if len(sys.argv) < 3:
        print("Invalid paramters.")
        print("Usage: render.py <nn_conf_file> <real_nn_conf_file> <my_pod_index>")
        exit(1)
    nn_conf_file = sys.argv[1]
    real_nn_conf_file = sys.argv[2]
    my_pod_index = int(sys.argv[3])

    with open(nn_conf_file, "r") as infile:
        nn_conf = json.load(infile)
        nns = nn_conf["NNS"]
        current_nn = nns[my_pod_index]
        current_nn_name = current_nn["NAME"]
        nn_conf["CURRENT_NN"] = current_nn_name
        with open(real_nn_conf_file, "w") as outfile:
            json.dump(nn_conf, outfile)


if __name__ == "__main__":
    main()
