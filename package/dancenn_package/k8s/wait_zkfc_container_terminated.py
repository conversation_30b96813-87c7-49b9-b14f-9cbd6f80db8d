#!/usr/bin/python3

import argparse
import logging
import socket
import time

# # Don't use kubectl api to judge if zkfc container is terminated.
# # https://github.com/kubernetes/kubernetes/issues/106896#issuecomment-990779862
# # https://github.com/kubernetes/kubernetes/blob/master/pkg/kubelet/kubelet.go#L1785-L1786
# # cfs-namenode-root-*****************-2
# pod_name = os.getenv("MY_POD_NAME")
# prefix = "cfs-namenode-"
# # root-*****************
# assert pod_name.startswith(prefix)
# k8s_namespace = pod_name[len(prefix):][:-2]
# # https://kubernetes.io/docs/tasks/run-application/access-api-from-pod/#without-using-a-proxy
# api_server = "https://kubernetes.default.svc"
# url = "{}/api/v1/namespaces/{}/pods/{}/status".format(
#   api_server, k8s_namespace, pod_name)
# logging.debug("url is {}".format(url))
# service_account = "/var/run/secrets/kubernetes.io/serviceaccount"
# token = open("{}/token".format(service_account)).read()
# response = requests.get(
#   url,
#   headers={"Authorization": "Bearer {}".format(token)},
#   verify="{}/ca.crt".format(service_account))
# if not response.ok:
#   logging.warn("K8S api server returns error, code: {}, msg: {}".format(
#     response.status_code, response.text))
# else:
#   containerStatuses = json.loads(
#     response.text)["status"]["containerStatuses"]
#   logging.debug("containerStatuses is {}".format(
#     json.dumps(containerStatuses)))
#   zkfc_statuses = list(
#     filter(lambda container: container["name"] == "zkfc",
#            containerStatuses))
#   if len(zkfc_statuses) != 1:
#     logging.warn("len(zkfc statuses) == {}".format(len(zkfc_statuses)))
#   else:
#     zkfc_status = zkfc_statuses[0]["state"]
#     logging.info("zkfc status is {}".format(json.dumps(zkfc_status)))
#     if "terminated" not in zkfc_status:
#       logging.info("zkfc is not terminated")
#     else:
#       break

if __name__ == "__main__":
  parser = argparse.ArgumentParser(description="wait zkfc container terminated")
  parser.add_argument("-d",
                      "--log_dir",
                      type=str,
                      required=True,
                      help="log dir")
  parser.add_argument("-l", "--log-level", default="INFO", help="log level")
  parser.add_argument("-p",
                      "--port",
                      type=int,
                      required=True,
                      help="zkfc haadmin port")
  args = parser.parse_args()

  logging.basicConfig(
    level=args.log_level,
    format="%(asctime)s %(levelname)s %(message)s",
    filename="{}/prestop.log".format(args.log_dir),
    filemode="w+",
  )

  while True:
    try:
      s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
      s.settimeout(1)
      s.connect((socket.getfqdn(), args.port))
      s.shutdown(socket.SHUT_RDWR)
      s.close()
      logging.warn("zkfc haadmin port is still open")
      time.sleep(5)
    except Exception as e:
      logging.info("Cannot connect zkfc zkfc haadmin port: {}".format(e))
      break
