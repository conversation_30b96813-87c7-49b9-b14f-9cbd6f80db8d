#!/usr/bin/env python3

import os
import sys
import time
import requests

def run_check(nn: str):
    URI = "http://{}/status?details=1".format(nn)
    rsp = requests.get(URI)
    res = rsp.json()
    cluster_id = res['cluster_id']
    bpid = res['blockpool_id']
    print("Get nn {} status. cluster_id: {}, bpid: {}".format(nn, cluster_id, bpid))

if __name__ == '__main__':
    if len(sys.argv) != 2:
        print("Usage: ./wait_nn_ready.py <nn_host_name:http_port>")
        sys.exit(-1)
    nn = sys.argv[1].strip()

    while True:
        print("Will check if nn is ready: {}".format(nn))
        try:
            run_check(nn)
            print("Check nn ready successfully")
            break
        except requests.exceptions.ConnectionError:
            print("Failed to check nn. Sleep and retry.")
            time.sleep(2)
