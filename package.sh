#!/bin/bash
BASE_DIR=$(cd $(dirname ${BASH_SOURCE:-$0});pwd)
BUILD_TYPE="Release"

set -x -e -o pipefail

GIT_USER=${USER}
[ "x${GIT_USER}x" = 'xtigerx' -o -z "${GIT_USER}" ] && GIT_USER=deploy

cd ${BASE_DIR}
if [[ -z "JAVA_HOME" ]]; then
  [ -d jdk ] || git clone -b master --depth 1 --single-branch ssh://${GIT_USER}@git.byted.org:29418/jdk
  JAVA_HOME=${BASE_DIR}/jdk/jdk1.8
fi

arch=$(uname -m)
if [[ "$arch" == "x86_64" ]]; then
  /opt/tiger/bvc/bin/bvc clone \
    inf/hdfs/cfs_bk_deploy \
    package/dancenn_package/jar/cfs_bk_deploy \
    --version $(cat package/dancenn_package/jar/bookkeeper-server-shaded.scm)
  mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/bookkeeper-server-shaded-4.6.2.jar package/dancenn_package/jar
  mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/bookkeeper-stats-api-4.6.2.jar package/dancenn_package/jar
  mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/api-1.0.22.jar package/dancenn_package/jar
  mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/byted-codec-formatter-1.0.22.jar package/dancenn_package/jar
  mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/byted-config-service-1.0.22.jar package/dancenn_package/jar
  mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/byted-sdk-1.0.22.jar package/dancenn_package/jar
  mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/core-1.0.22.jar package/dancenn_package/jar
  mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/exporter-common-1.0.22.jar package/dancenn_package/jar
  mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/structured-1.0.22.jar package/dancenn_package/jar
  mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/unix-socket-exporter-1.0.22.jar package/dancenn_package/jar
  mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/byted-apm-jnr-unixsock-shade-1.0.9.jar package/dancenn_package/jar
  mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/byted-apm-vendor-1.0.9.jar package/dancenn_package/jar
  mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/byted-apm-yml-shade-1.0.9.jar package/dancenn_package/jar
  mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/config-loader-1.0.9.jar package/dancenn_package/jar
  mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/utils-1.0.9.jar package/dancenn_package/jar
  mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/vendor-1.0.9.jar package/dancenn_package/jar
  rm -f -r package/dancenn_package/jar/cfs_bk_deploy

  /opt/tiger/bvc/bin/bvc clone \
    inf/hdfs/cfs_dancenn_thirdparty \
    third_party/cfs_dancenn_thirdparty \
    --version $(cat third_party/thirdparty.scm)

  /opt/tiger/bvc/bin/bvc clone \
    data/inf/hdfs_client_debian9 \
    third_party/hdfs_client \
    --version $(cat third_party/hdfs_client.scm)
  mkdir -p third_party/hdfs_client/include/hdfs
  [ -f third_party/hdfs_client/include/hdfs.h ] && mv third_party/hdfs_client/include/hdfs.h third_party/hdfs_client/include/hdfs
  [ -f third_party/hdfs_client/include/hdfsAsyncContext.h ] && mv third_party/hdfs_client/include/hdfsAsyncContext.h third_party/hdfs_client/include/hdfs
  rm -rf third_party/hdfs_client/bin
  rm -rf third_party/hdfs_client/lib/glibc-bundle-minimal
elif [[ "$arch" == "aarch64" ]]; then
  /opt/tiger/bvc/bin/bvc clone \
    inf/hdfs/cfs_bk_deploy \
    package/dancenn_package/jar/cfs_bk_deploy \
    --version $(cat package/dancenn_package/jar/bookkeeper-server-shaded.scm)
  mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/bookkeeper-server-shaded-4.6.2.jar package/dancenn_package/jar
  mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/bookkeeper-stats-api-4.6.2.jar package/dancenn_package/jar
  mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/api-1.0.22.jar package/dancenn_package/jar
  mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/byted-codec-formatter-1.0.22.jar package/dancenn_package/jar
  mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/byted-config-service-1.0.22.jar package/dancenn_package/jar
  mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/byted-sdk-1.0.22.jar package/dancenn_package/jar
  mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/core-1.0.22.jar package/dancenn_package/jar
  mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/exporter-common-1.0.22.jar package/dancenn_package/jar
  mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/structured-1.0.22.jar package/dancenn_package/jar
  mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/unix-socket-exporter-1.0.22.jar package/dancenn_package/jar
  mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/byted-apm-jnr-unixsock-shade-1.0.9.jar package/dancenn_package/jar
  mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/byted-apm-vendor-1.0.9.jar package/dancenn_package/jar
  mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/byted-apm-yml-shade-1.0.9.jar package/dancenn_package/jar
  mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/config-loader-1.0.9.jar package/dancenn_package/jar
  mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/utils-1.0.9.jar package/dancenn_package/jar
  mv package/dancenn_package/jar/cfs_bk_deploy/bookkeeper-server/lib/vendor-1.0.9.jar package/dancenn_package/jar
  rm -f -r package/dancenn_package/jar/cfs_bk_deploy

  /opt/tiger/bvc/bin/bvc clone \
    inf/hdfs/cfs_dancenn_thirdparty \
    third_party/cfs_dancenn_thirdparty \
    --version $(cat third_party/thirdparty.scm_aarch64)
else
  exit 1
fi

BUILD_VERSION="${BUILD_VERSION:-0.0.0}"
declare -a VERSION_ARR=($(awk -F. '{printf "%d %d %d\n", $1, $2, $3}' <<< ${BUILD_VERSION}))
MAJOR_VERSION=${VERSION_ARR[0]}
MINOR_VERSION=${VERSION_ARR[1]}
PATCH_VERSION=${VERSION_ARR[2]}

# BUILD ranger things.
mkdir ~/.m2/
wget --directory-prefix=~/.m2 http://tosv.byted.org/obj/cloudfs/tools/settings.xml
mkdir builds
pushd builds
env JAVA_HOME=${JAVA_HOME} cmake ..
popd
pushd java/cfs-ranger-bridger && mvn assembly:assembly && popd
rm -rf builds

env JAVA_HOME=${JAVA_HOME} \
  cmake -H. \
    -Bbuilds \
    -DCMAKE_BUILD_TYPE=${BUILD_TYPE} \
    -DCMAKE_INSTALL_PREFIX=${BASE_DIR}/${BUILD_TYPE} \
    -DBUILD_VERSION=${BUILD_VERSION} \
    -DDANCENN_MAJOR_VERSION=${MAJOR_VERSION} \
    -DDANCENN_MINOR_VERSION=${MINOR_VERSION} \
    -DDANCENN_PATCH_VERSION=${PATCH_VERSION}

pushd builds && make dancenn -j64 && popd
pushd builds && make fsimage_transfer -j64 && popd
pushd builds && make dancenn_haadmin -j64 && popd
pushd builds && make print_rocksdb -j64 && popd
pushd builds && make dump_rocksdb -j64 && popd
pushd builds && make print_editlog -j64 && popd
pushd builds && make print_dirstat -j64 && popd
pushd builds && make path_viewer -j64 && popd
pushd builds && make gs_checker -j64 && popd
pushd builds && make ckpt_catcher -j64 && popd
pushd builds && make ckpt_comparator -j64 && popd
pushd builds && make verify_correctness -j64 && popd
pushd builds && make tos_util -j64 && popd
pushd builds && make tos_util_raw -j64 && popd
pushd builds && make tos_assumerole_verify -j64 && popd
[ -d dancenn_package ] && rm -fr dancenn_package
cp -r package/dancenn_package dancenn_package
cp builds/dancenn dancenn_package/bin/dancenn
cp builds/fsimage_transfer dancenn_package/bin/fsimage_transfer
cp builds/dancenn_haadmin dancenn_package/tools/dancenn_haadmin
cp builds/print_rocksdb dancenn_package/tools/print_rocksdb
cp builds/dump_rocksdb dancenn_package/tools/dump_rocksdb
cp builds/print_editlog dancenn_package/tools/print_editlog
cp builds/print_dirstat dancenn_package/tools/print_dirstat
cp builds/path_viewer dancenn_package/tools/path_viewer
cp builds/gs_checker dancenn_package/tools/gs_checker
cp builds/ckpt_catcher dancenn_package/tools/ckpt_catcher
cp builds/ckpt_comparator dancenn_package/tools/ckpt_comparator
cp builds/verify_correctness dancenn_package/tools/verify_correctness
cp builds/tos_util dancenn_package/tools/tos_util
cp builds/tos_util_raw dancenn_package/tools/tos_util_raw
cp builds/tos_assumerole_verify dancenn_package/tools/tos_assumerole_verify
cp src/tool/tos_assumerole_verify.sh dancenn_package/tools/tos_assumerole_verify.sh
cp java/cfs-ranger-bridger/target/cfs-ranger-bridger-*-with-dependencies.jar dancenn_package/jar/
cp third_party/cfs_dancenn_thirdparty/builds/third_party/rocksdb/output/tools/ldb dancenn_package/tools/ldb
cp tools/jeprof dancenn_package/tools/jeprof
mkdir -p dancenn_package/lib
cp third_party/cfs_dancenn_thirdparty/builds/third_party/snappy/output/lib/libsnappy.so.1* dancenn_package/lib


if [[ "$arch" == "x86_64" ]]; then
  cp third_party/hdfs_client/lib/libhdfs_client.so dancenn_package/lib
elif [[ "$arch" == "aarch64" ]]; then
  true
else
  exit 1
fi

# delete useless files
rm -rf dancenn_package/tools/regression_test
rm -rf dancenn_package/tools/dancereplay
rm -rf dancenn_package/tools/dump_rocksdb
rm -rf dancenn_package/tools/gs_checker
rm -rf dancenn_package/tools/hdfs_local
rm -rf dancenn_package/tools/print_editlog_local
rm -rf dancenn_package/tools/sst_dump
rm -rf dancenn_package/tools/tce_scripts
rm -rf dancenn_package/bin/placement_driver*
rm -rf dancenn_package/bin/danceproxy*
rm -rf dancenn_package/conf_common
rm -rf dancenn_package/conf_local
rm -rf dancenn_package/svc

# stripe debug symbols for tools binaries
strip --strip-debug --strip-unneeded dancenn_package/bin/fsimage_transfer
strip --strip-debug --strip-unneeded dancenn_package/tools/dancenn_haadmin
strip --strip-debug --strip-unneeded dancenn_package/tools/ldb
strip --strip-debug --strip-unneeded dancenn_package/tools/path_viewer
strip --strip-debug --strip-unneeded dancenn_package/tools/ckpt_catcher
strip --strip-debug --strip-unneeded dancenn_package/tools/ckpt_comparator
strip --strip-debug --strip-unneeded dancenn_package/tools/print_dirstat
strip --strip-debug --strip-unneeded dancenn_package/tools/print_editlog
strip --strip-debug --strip-unneeded dancenn_package/tools/print_rocksdb
strip --strip-debug --strip-unneeded dancenn_package/tools/verify_correctness
strip --strip-debug --strip-unneeded dancenn_package/tools/tos_util
strip --strip-debug --strip-unneeded dancenn_package/tools/tos_util_raw
strip --strip-debug --strip-unneeded dancenn_package/tools/tos_assumerole_verify

pushd dancenn_package && git status && popd
rm -fr dancenn_package/.git
[ -d output ] && rm -fr output
mv dancenn_package output
