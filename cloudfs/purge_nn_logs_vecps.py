#!/usr/bin/env python3

import json
import logging
import os
import requests
import traceback


def get_all_nn() -> list:
    nn_config = os.environ.get("NN_CONF_FILE")
    if nn_config == None:
        logging.error("Cannot get NN config file path")
        exit(1)
    with open(nn_config, 'r') as f:
        try:
            config_json = json.load(f)
        except Exception as err:
            logging.exception(err)
            logging.error(traceback.format_exc())
            return []
    return ["{}:{}".format(nn["HOST_IP"], config_json["NN_HTTP_PORT"]) for nn in config_json["NNS"]]


def get_active_nn(nns: list) -> str:
    active_nn = ""
    for nn in nns:
        try:
            ret = requests.get("http://{}/status".format(nn))
            ret_json = json.loads(ret.text)
        except Exception as err:
            logging.exception(err)
            logging.error(traceback.format_exc())
            continue
        if ret_json["ha_state"] == "ACTIVE":
            active_nn = nn
            break
    return active_nn


def get_txid(nn: str) -> int:
    try:
        ret = requests.get("http://{}/admin?cmd=get_last_ckpt_id".format(nn))
    except Exception as err:
        logging.exception(err)
        logging.error(traceback.format_exc())
        return float("-inf")

    try:
        txid = int(ret.text)
    except Exception as err:
        logging.exception(err)
        logging.error(traceback.format_exc())
        return float("-inf")
    return txid


def get_min_txid(nns: list) -> int:
    min_txid = float("inf")
    for nn in nns:
        txid = get_txid(nn)
        if txid < min_txid:
            min_txid = txid
    if min_txid > 0:
        min_txid -= 1
    return min_txid


def purge_log(nn: str, txid: int) -> bool:
    if nn == "" or txid == float("-inf"):
        return False
    try:
        ret = requests.get(
            "http://{}/admin?cmd=purge_logs&txid={}".format(nn, txid))
    except Exception as err:
        logging.exception(err)
        logging.error(traceback.format_exc())
        return False
    if ret.ok:
        return True
    return False


def purge_all() -> bool:
    nns = get_all_nn()
    active_nn = get_active_nn(nns)
    min_txid = get_min_txid(nns)
    if purge_log(active_nn, min_txid):
        logging.info("[SUCCESS] Purged log at NN[{}] TXID[{}]".format(
            active_nn, min_txid))
        return True
    else:
        logging.warning("[FAIL] Purged log at NN[{}] TXID[{}]".format(
            active_nn, min_txid))
        return False


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    if purge_all():
        exit(0)
    else:
        exit(1)
