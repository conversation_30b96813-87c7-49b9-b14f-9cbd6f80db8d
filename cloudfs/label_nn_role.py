#!/usr/bin/env python3

import json
import os
import requests
import time
from kubernetes import client, config


def get_nn_config() -> dict:
    nn_conf_file = os.environ.get("NN_CONF_FILE")
    if nn_conf_file == None:
        print("NN_CONF_FILE not specified.")
        exit(1)
    
    with open(nn_conf_file, "r") as f:
        return json.load(f)


def get_nn_hosts(nn_config: dict) -> list:
    nn_hosts = []
    nns = nn_config["NNS"]
    for nn in nns:
        nn_hosts.append(nn["HOST_IP"])

    return nn_hosts


def get_nn_ha_state(nn_endpoint: str) -> str:
    URL = "http://{}/status".format(nn_endpoint)
    rsp = requests.get(URL)
    res = rsp.json()
    return res["ha_state"]


def run(api, nn_config):
    nn_hosts = get_nn_hosts(nn_config)
    nn_http_port = nn_config["NN_HTTP_PORT"]
    for nn_host in nn_hosts:
        ha_state = None
        try:
            nn_endpoint = "{}:{}".format(nn_host, nn_http_port)
            ha_state = get_nn_ha_state(nn_endpoint)
            print("Got NN's ha_state, nn_endpoint: {}, ha_state: {}".format(nn_endpoint, ha_state))
        except requests.exceptions.ConnectionError:
            print("Failed to get NN state, nn_endpoint: {}".format(nn_endpoint))
        nn_addr_split = nn_host.split(".")
        pod_name = nn_addr_split[0]
        namespace_name = nn_addr_split[-1]
        body = [
            {
                "op": "replace",
                "path": "/metadata/labels/ha_state",
                "value": ha_state
            }
        ]
        api.patch_namespaced_pod(pod_name, namespace_name, body)
        print("Successfully patched pod.")


def main():
    config.load_incluster_config()
    api = client.CoreV1Api()
    nn_config = get_nn_config()

    while True:
        print("Will label NN role.")
        run(api, nn_config)
        print("Successfully labeled, will sleep.")
        time.sleep(1)


if __name__ == '__main__':
    main()
