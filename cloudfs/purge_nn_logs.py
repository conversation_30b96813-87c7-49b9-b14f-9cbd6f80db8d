#!/usr/bin/env python3

import json
import logging
import os
import requests
import traceback

from pyutil.program import metrics2 as metrics

excluded_ns = []

excluded_ns_file = os.environ.get("EXCLUDED_NS_FILE")
if excluded_ns_file != None and os.path.exists(excluded_ns_file):
    with open(excluded_ns_file, "r") as f:
        excluded_ns = f.read().splitlines()

adminsvc_endpoint = os.environ.get("ADMINSVC_ENDPOINT")
if adminsvc_endpoint == None:
    logging.error(
        "Cannot get admin service endpoint from environment variables")
    exit(1)

cluster_name = os.environ.get("CLUSTER_NAME")
if cluster_name == None:
    logging.error("Cannot get cluster id from environment variables")
    exit(1)

metrics_server = os.environ.get("METRICSERVER2")
if metrics_server == None:
    logging.error(
        "Cannot get metrics server hostname from environment variables")
    exit(1)

metrics_server_port = os.environ.get("METRICSERVER2_PORT")

def get_all_fs() -> list:
    try:
        ret = requests.get("{}/fs/ls/up".format(adminsvc_endpoint),
                           headers={"cluster-name": cluster_name})
        ret_json = json.loads(ret.text)
    except Exception as err:
        logging.exception(err)
        logging.error(traceback.format_exc())
        return []

    if ret_json["status"] != "OK":
        logging.error("Receive Non-OK status from admin service")
        return []
    return [fs["name"] for fs in ret_json["data"]]


def get_all_ns(fs_name: str) -> list:
    try:
        ret = requests.get(
            "{}/ns/ls/up?fs_name={}".format(adminsvc_endpoint, fs_name))
        ret_json = json.loads(ret.text)
    except Exception as err:
        logging.exception(err)
        logging.error(traceback.format_exc())
        return []

    if ret_json["status"] != "OK":
        logging.error("Receive Non-OK status from admin service")
        return []
    return [ns["name"] for ns in ret_json["data"] if ns["status"] == "UP"]


def get_all_nn(ns_name: str) -> list:
    try:
        ret = requests.get(
            "{}/mt/ns?name={}&has_ob=true".format(adminsvc_endpoint, ns_name))
        ret_json = json.loads(ret.text)
    except Exception as err:
        logging.exception(err)
        logging.error(traceback.format_exc())
        return []

    if ret_json["status"] != "OK":
        logging.error("Receive Non-OK status from admin service")
        return []
    return ["{}:{}".format(nn["ip"], nn["http_port"]) for nn in ret_json["data"]["nn_ip_port"].values()]


def get_active_nn(nns: list) -> str:
    active_nn = ""
    for nn in nns:
        try:
            ret = requests.get("http://{}/status".format(nn))
            ret_json = json.loads(ret.text)
        except Exception as err:
            logging.exception(err)
            logging.error(traceback.format_exc())
            continue
        if ret_json["ha_state"] == "ACTIVE":
            active_nn = nn
            break
    return active_nn


def get_txid(nn: str) -> int:
    try:
        ret = requests.get("http://{}/admin?cmd=get_last_ckpt_id".format(nn))
    except Exception as err:
        logging.exception(err)
        logging.error(traceback.format_exc())
        return float("-inf")

    try:
        txid = int(ret.text)
    except Exception as err:
        logging.exception(err)
        logging.error(traceback.format_exc())
        return float("-inf")
    return txid


def get_min_txid(nns: list) -> int:
    min_txid = float("inf")
    for nn in nns:
        txid = get_txid(nn)
        if txid < min_txid:
            min_txid = txid
    if min_txid > 0:
        min_txid -= 1
    return min_txid


def purge_log(nn: str, txid: int) -> bool:
    if nn == "" or txid == float("-inf"):
        return False
    try:
        ret = requests.get(
            "http://{}/admin?cmd=purge_logs&txid={}".format(nn, txid))
    except Exception as err:
        logging.exception(err)
        logging.error(traceback.format_exc())
        return False
    if ret.ok:
        return True
    return False


def purge_all() -> bool:
    ret = True
    all_fs = get_all_fs()
    for fs in all_fs:
        all_ns = get_all_ns(fs)
        for ns in all_ns:
            if ns in excluded_ns:
                continue
            nns = get_all_nn(ns)
            active_nn = get_active_nn(nns)
            min_txid = get_min_txid(nns)
            if purge_log(active_nn, min_txid):
                logging.info("[SUCCESS] Purged log at FS[{}] NS[{}] NN[{}] TXID[{}]".format(
                    fs, ns, active_nn, min_txid))
            else:
                logging.warning("[FAIL] Purged log at FS[{}] NS[{}] NN[{}] TXID[{}]".format(
                    fs, ns, active_nn, min_txid))
                ret = False
    return ret


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    metrics_args = {}
    metrics_args["metrics_namespace_prefix"] = "inf.cfs.dancenn"
    metrics_args["use_remote"] = True
    metrics_args["remote_addr"] = metrics_server
    metrics_args["remote_port"] = 9123 if metrics_server_port is None else int(metrics_server_port)
    metrics.init(metrics_args)
    metrics.define_store("purge_log.start")
    metrics.define_store("purge_log.success")

    metrics.emit_store("purge_log.start", 1, tagkv={"cluster": cluster_name})
    if purge_all():
        metrics.emit_store("purge_log.success", 1, tagkv={"cluster": cluster_name})
        exit(0)
    else:
        exit(1)
