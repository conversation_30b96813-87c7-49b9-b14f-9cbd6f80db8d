/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * These .proto interfaces are private and stable.
 * Please see http://wiki.apache.org/hadoop/Compatibility
 * for what changes are allowed for a *stable* .proto interface.
 */

// This file contains protocol buffers that are used throughout HDFS -- i.e.
// by the client, server, and data transfer protocols.
syntax = "proto2";

option java_package = "com.volcengine.cloudfs.proto";
option java_outer_classname = "DatanodeProtocolProtos";
option java_generic_services = true;
option java_generate_equals_and_hash = true;
option cc_generic_services = true;
package cloudfs.datanode;

import "hdfs.proto";
import "lifecycle.proto";


message AcquireRequest {
  enum QuotaType {
    Get = 0;
    Put = 1;
    GetRate = 3;
    PutRate = 4;
  }
  enum Priority {
    Level0 = 0;
    Level1 = 1;
  }
  required string    key = 1;
  required uint64    acquire = 3;
  required QuotaType quotaType = 4;
  optional Priority  priority = 5;
}

message AcquireResponse {
  enum AcquireStatus {
    Ok = 0;
    Insufficient = 1;
    OutOfQuota = 2;
  }
  required uint64        acquired = 1;
  required AcquireStatus status   = 2;
  required uint64        acquiredAt = 3;
}

/**ReceivedDeletedBlockInfoProto
 * Information to identify a datanode to a namenode
 */
message DatanodeRegistrationProto {
  required DatanodeIDProto datanodeID = 1;    // Datanode information
  required StorageInfoProto storageInfo = 2;  // Node information
  required ExportedBlockKeysProto keys = 3;   // Block keys
  required string softwareVersion = 4;        // Software version of the DN, e.g. "2.0.0"
  optional bool blockReportPreserved = 5 [ default = false ];  // Whether block report is preserved in NameNode memory
  optional uint32 dnMajorVersion = 1000 [ default = 0 ];
  optional uint32 dnMinorVersion = 1001 [ default = 0 ];
  optional uint32 dnPatchVersion = 1002 [ default = 0 ];
}

/**
 * Commands sent from namenode to the datanodes
 */
message DatanodeCommandProto {
  enum Type {
    BalancerBandwidthCommand = 0;
    BlockCommand = 1;
    BlockRecoveryCommand = 2;
    FinalizeCommand = 3;
    KeyUpdateCommand = 4;
    RegisterCommand = 5;
    UnusedUpgradeCommand = 6;
    NullDatanodeCommand = 7;
    BlockIdCommand = 8;
    UploadCommand = 1000;
    NotifyEvictableCommand = 1001;
    InvalidatePufsCommand = 1002;
    BlockReportCommand = 1003; // Make Full Block Report
    MergeCommand = 1004;
    AbortBlockReportCommand = 1005; // Abort current FBR
    LoadCommand = 1006;
  }

  required Type cmdType = 1;    // Type of the command

  // One of the following command is available when the corresponding
  // cmdType is set
  optional BalancerBandwidthCommandProto balancerCmd = 2;
  optional BlockCommandProto blkCmd = 3;
  optional BlockRecoveryCommandProto recoveryCmd = 4;
  optional FinalizeCommandProto finalizeCmd = 5;
  optional KeyUpdateCommandProto keyUpdateCmd = 6;
  optional RegisterCommandProto registerCmd = 7;
  optional BlockIdCommandProto blkIdCmd = 8;
  optional UploadCommandProto uploadCmd = 1000;
  optional NotifyEvictableCommandProto neCmd = 1001;
  optional InvalidatePufsCommandProto invalidatePufsCmd = 1002;
  optional BlockReportCommandProto blockreportCmd = 1003;
  optional MergeCommandProto mergeCmd = 1004;
  optional AbortBlockReportCommandProto abortBlockreportCmd = 1005;
  optional LoadCommandProto loadCmd = 1006;
}

/**
 * Command sent from namenode to datanode to set the
 * maximum bandwidth to be used for balancing.
 */
message BalancerBandwidthCommandProto {

  // Maximum bandwidth to be used by datanode for balancing
  required uint64 bandwidth = 1;
}

/**
 * Command to instruct datanodes to perform certain action
 * on the given set of blocks.
 */
message BlockCommandProto {
  enum Action {
    TRANSFER = 1;   // Transfer blocks to another datanode
    INVALIDATE = 2; // Invalidate blocks
    SHUTDOWN = 3;   // Shutdown the datanode
    FINALIZED = 4;   // Promote the block state to finalized and truncate redundant length
    REPLACE = 5;    // Transfer blocks to another datanode and delete local replica
  }

  required Action action = 1;
  required string blockPoolId = 2;
  repeated BlockProto blocks = 3;
  repeated DatanodeInfosProto targets = 4;
  repeated StorageUuidsProto targetStorageUuids = 5;
  repeated StorageTypesProto targetStorageTypes = 6;
  repeated IOPriority priorities = 7; // Highest priority doesn't require throttle
  repeated bool transferFullBlock = 8;
}

/**
 * Command to instruct datanodes to perform certain action
 * on the given set of block IDs.
 */
message BlockIdCommandProto {
  enum Action {
    // original command
    CACHE = 1;      // alloc memory copy of replica
    UNCACHE = 2;    // free memory copy of replica

    // StorageClass-related command
    SET_STORAGE_CLASS_HOT = 101;
    SET_STORAGE_CLASS_WARM = 102;
    SET_STORAGE_CLASS_COLD = 103;

    // Pin-related command
    PIN = 201;
    UNPIN = 202;
  }
  required Action action = 1;
  required string blockPoolId = 2;
  repeated uint64 blockIds = 3 [packed=true];
}

enum UploadType {
  UPLOAD = 0;
  PUT = 1;
  APPEND = 2;
}

message UploadCommandProto {
  required string dnUuid = 1;
  required string blockPoolId = 2;
  required BlockProto block = 3;
  required string blockPufsName = 4;
  required uint64 expTs = 5;
  required string uploadId = 6;
  repeated string abortedUploadIds = 7;
  optional UploadType uploadType = 8;
  repeated uint32 partNums = 9;
  repeated LocatedBlockProto prevBlocks = 10;
  optional uint64 appendOffset = 11;
}

message NotifyEvictableCommandProto {
  required string blockPoolId = 1;
  required BlockProto block = 2;
  optional StorageClassProto cls = 3;
  optional bool pinned = 4;
}

message InvalidatePufsCommandProto {
  repeated ExtendedBlockProto blocks = 1;
}

message BlockReportCommandProto {
  enum Type {
    NORMAL = 0;
    FAST = 1; // WriteCache only
  }
  optional uint64 reportInterval = 1;
  optional uint64 batchSize = 2;
  optional Type type = 3;
}

message MergeCommandProto {
  required ExtendedBlockProto block = 1;
  repeated LocatedBlockProto oldBlocks = 2;
}

message AbortBlockReportCommandProto {
  // The unique 64-bit ID of this block report
  optional int64 id = 1;
}

message LoadCommandProto {
  repeated ExtendedBlockProto blocks = 1;
}

/**
 * List of blocks to be recovered by the datanode
 */
message BlockRecoveryCommandProto {
  repeated RecoveringBlockProto blocks = 1;
}

/**
 * Finalize the upgrade at the datanode
 */
message FinalizeCommandProto {
  required string blockPoolId = 1; // Block pool to be finalized
}

/**
 * Update the block keys at the datanode
 */
message KeyUpdateCommandProto {
  required ExportedBlockKeysProto keys = 1;
}

/**
 * Instruct datanode to register with the namenode
 */
message RegisterCommandProto {
  // void
}

/**
 * registration - Information of the datanode registering with the namenode
 */
message RegisterDatanodeRequestProto {
  required DatanodeRegistrationProto registration = 1; // Datanode info
}

/**
 * registration - Update registration of the datanode that successfully
 *                registered. StorageInfo will be updated to include new
 *                storage ID if the datanode did not have one in the request.
 */
message RegisterDatanodeResponseProto {
  required DatanodeRegistrationProto registration = 1; // Datanode info
}

/**
 * failedStorageLocations - storage locations that have failed
 * lastVolumeFailureDate - date/time of last volume failure
 * estimatedCapacityLost - estimate of total capacity lost due to volume failures
 */
message VolumeFailureSummaryProto {
  repeated string failedStorageLocations = 1;
  required uint64 lastVolumeFailureDate = 2;
  required uint64 estimatedCapacityLostTotal = 3;
}

message DiskStatSummaryProto {
  required uint64 numDevices = 1;

  // scaled to 1000ms/device
  required uint64 timeSpentIOMs = 2;
  required uint64 readOps = 3;
  required uint64 writeOps = 4;
  required uint64 readMergedOps = 5;
  required uint64 writeMergedOps = 6;
  required uint64 sectorsReadOps = 7;
  required uint64 sectorsWriteOps = 8;
  required uint64 timeSpentReadingMs = 9;
  required uint64 timeSpentWritingMs = 10;
}

/**
 * registration - datanode registration information
 * capacity - total storage capacity available at the datanode
 * dfsUsed - storage used by HDFS
 * remaining - remaining storage available for HDFS
 * blockPoolUsed - storage used by the block pool
 * xmitsInProgress - number of transfers from this datanode to others
 * xceiverCount - number of active transceiver threads
 * failedVolumes - number of failed volumes.  This is redundant with the
 *     information included in volumeFailureSummary, but the field is retained
 *     for backwards compatibility.
 * cacheCapacity - total cache capacity available at the datanode
 * cacheUsed - amount of cache used
 * volumeFailureSummary - info about volume failures
 */
message HeartbeatRequestProto {
  required DatanodeRegistrationProto registration = 1; // Datanode info
  repeated StorageReportProto reports = 2;
  optional uint32 xmitsInProgress = 3 [ default = 0 ];
  optional uint32 xceiverCount = 4 [ default = 0 ];
  optional uint32 failedVolumes = 5 [ default = 0 ];
  optional uint64 cacheCapacity = 6 [ default = 0 ];
  optional uint64 cacheUsed = 7 [default = 0 ];
  optional VolumeFailureSummaryProto volumeFailureSummary = 8;
  optional DiskStatSummaryProto diskStatSummary = 9;
  optional uint64 tempBasedRemaining = 1000 [ default = 0 ];
  optional bool decommission = 1001 [ default = false ];
  optional bool stale = 1002 [ default = false ];
  optional DatanodeInfoProto.AdminState adminState = 1003;
  optional uint32 enabledCapabilities = 1004;
  optional uint32 disabledCapabilities = 1005;
}

/**
 * state - State the NN is in when returning response to the DN
 * txid - Highest transaction ID this NN has seen
 */
message NNHAStatusHeartbeatProto {
  enum State {
    ACTIVE = 0;
    STANDBY = 1;
  }
  required State state = 1;
  required uint64 txid = 2;
}

/**
 * cmds - Commands from namenode to datanode.
 * haStatus - Status (from an HA perspective) of the NN sending this response
 */
message HeartbeatResponseProto {
  repeated DatanodeCommandProto cmds = 1; // Returned commands can be null
  required NNHAStatusHeartbeatProto haStatus = 2;
  optional RollingUpgradeStatusProto rollingUpgradeStatus = 3;

  // The field number starts from 1000 from compatibility.
  optional TOSInfoProto tosInfo = 1000 [deprecated = true];
  optional RemoteBlockInfoProto remoteBlockInfo = 1001;

  optional bool allowEvictWriteCache = 1010 [deprecated = false];
}

/**
 * registration - datanode registration information
 * blockPoolID  - block pool ID of the reported blocks
 * blocks       - each block is represented as multiple longs in the array.
 *                first long represents block ID
 *                second long represents length
 *                third long represents gen stamp
 *                fourth long (if under construction) represents replica state
 * context      - An optional field containing information about the context
 *                of this block report.
 */
message BlockReportRequestProto {
  required DatanodeRegistrationProto registration = 1;
  required string blockPoolId = 2;
  repeated StorageBlockReportProto reports = 3;
  optional BlockReportContextProto context = 4;
}

message BlockReportBlockInfoProtoV2Entry {
  required BlockProto block = 1;
  required ReplicaStateProto replicaState = 2;
  optional StorageClassProto storageClass = 3 [ default = NONE ];
  optional bool pinned = 4 [ default = false ];
}
message BlockReportBlockInfoProtoV2 {
  repeated BlockReportBlockInfoProtoV2Entry blocks = 1;
}

message BlockReportContextProto  {
  // The total number of RPCs this block report is broken into.
  required int32 totalRpcs = 1;

  // The index of the current RPC (zero-based)
  required int32 curRpc = 2;

  // The unique 64-bit ID of this block report
  required int64 id = 3;
}

/**
 * Report of blocks in a storage
 */
message StorageBlockReportProto {
  enum BlocksFormat {
    V1 = 1;  // use 'blocks'
    V2 = 2;  // use 'blocksV2', gzip-compressed BlockReportBlockInfoProtoV2
  }

  required DatanodeStorageProto storage = 1;    // Storage
  optional BlockReportContextProto context = 1001;

  optional BlocksFormat blocksFormatVersion = 1002 [ default = V1 ];
  repeated uint64 blocks = 2 [packed=true];
  optional string blocksV2 = 1003;
}

/**
 * cmd - Command from namenode to the datanode
 */
message BlockReportResponseProto {
  optional DatanodeCommandProto cmd = 1;
}

/**
 * registration - datanode registration information
 * blockPoolId  - block pool ID of the reported blocks
 * blocks       - representation of blocks as longs for efficiency reasons
 */
message CacheReportRequestProto {
  required DatanodeRegistrationProto registration = 1;
  required string blockPoolId = 2;
  repeated uint64 blocks = 3 [packed=true];
}

message CacheReportResponseProto {
  optional DatanodeCommandProto cmd = 1;
}

message FailedMessage {
    optional string reason_string = 1;
    optional bool retryable = 2;

    // Upload failed
    optional uint64 uploaded_bytes = 3;
    optional uint64 total_bytes = 4;
};

/**
 * Data structure to send received or deleted block information
 * from datanode to namenode.
 */
message ReceivedDeletedBlockInfoProto {
  enum BlockStatus {
    RECEIVING = 1; // block being created
    RECEIVED = 2; // block creation complete
    DELETED = 3;
    SEALED = 4; // block is sealed in byterpc protocol

    UPLOAD_ID_NEGOED = 1000;
    UPLOAD_SUCCEED = 1001;
    UPLOAD_FAILED = 1020;

    PUFS_DELETED = 1002;
    MARKED_REPLICA_EVICTABLE = 1003;
    MERGED = 1004;
    REPORT_STORAGE_CLASS = 1010;

    LOAD_FAILED = 1021;
    
    // apply, NN will send cmd if passed
    EVICT_BLOCK = 1030;
    TRANSFER_BLOCK = 1031;
  }

  required BlockProto block = 1;
  required BlockStatus status = 3;
  optional string deleteHint = 2;
  optional string uploadId = 1000;
  optional string pufsName = 1001;
  optional string fiuCmd = 1002;
  optional string eTag = 1003;
  optional uint64 cachedNumBytes = 1007 [ default = 0 ];

  // effective when status is RECEIVED or REPORT_STORAGE_CLASS
  optional StorageClassProto storageClass = 1004;
  optional bool pinned = 1006;

  optional FailedMessage failed_msg = 1005;
}

/**
 * List of blocks received and deleted for a storage.
 */
message StorageReceivedDeletedBlocksProto {
  required string storageUuid = 1 [ deprecated = true ];
  repeated ReceivedDeletedBlockInfoProto blocks = 2;
  optional DatanodeStorageProto storage = 3;   // supersedes storageUuid.
}

/**
 * registration - datanode registration information
 * blockPoolID  - block pool ID of the reported blocks
 * blocks       - Received/deleted block list
 */
message BlockReceivedAndDeletedRequestProto {
  required DatanodeRegistrationProto registration = 1;
  required string blockPoolId = 2;
  repeated StorageReceivedDeletedBlocksProto blocks = 3;
}

/**
 * void response
 */
message BlockReceivedAndDeletedResponseProto {
}

/**
 * registartion - Datanode reporting the error
 * errorCode - error code indicating the error
 * msg - Free text description of the error
 */
message ErrorReportRequestProto {
  enum ErrorCode {
    NOTIFY = 0;           // Error report to be logged at the namenode
    DISK_ERROR = 1;       // DN has disk errors but still has valid volumes
    INVALID_BLOCK = 2;    // Command from namenode has invalid block ID
    FATAL_DISK_ERROR = 3; // No valid volumes left on datanode
  }
  required DatanodeRegistrationProto registartion = 1; // Registartion info
  required uint32 errorCode = 2;  // Error code
  required string msg = 3;        // Error message
}

/**
 * void response
 */
message ErrorReportResponseProto {
}

/**
 * blocks - list of blocks that are reported as corrupt
 */
message ReportBadBlocksRequestProto {
  repeated LocatedBlockProto blocks = 1;
}

/**
 * void response
 */
message ReportBadBlocksResponseProto {
}

/**
 * Commit block synchronization request during lease recovery
 */
message CommitBlockSynchronizationRequestProto {
  required ExtendedBlockProto block = 1;
  required uint64 newGenStamp = 2;
  required uint64 newLength = 3;
  required bool closeFile = 4;
  required bool deleteBlock = 5;
  repeated DatanodeIDProto newTaragets = 6;
  repeated string newTargetStorages = 7;
}

/**
 * void response
 */
message CommitBlockSynchronizationResponseProto {
}

message GetNamespacesRequestProto {
  required string filesystem = 1;
  required uint64 dngroup = 2;
}

message NamespaceInfoProto {
  required string name = 1;
  repeated string namenodes = 2;
}

message GetNamespacesResponseProto {
  required uint64 version = 1;
  repeated NamespaceInfoProto namespaces = 2;
}

/**
 * Protocol used from datanode to the namenode
 * See the request and response for details of rpc call.
 */
service DatanodeProtocolService {
  /**
   * Register a datanode at a namenode
   */
  rpc registerDatanode(RegisterDatanodeRequestProto)
      returns(RegisterDatanodeResponseProto);

  /**
   * Send heartbeat from datanode to namenode
   */
  rpc sendHeartbeat(HeartbeatRequestProto) returns(HeartbeatResponseProto);

  /**
   * Report blocks at a given datanode to the namenode
   */
  rpc blockReport(BlockReportRequestProto) returns(BlockReportResponseProto);

  /**
   * Report cached blocks at a datanode to the namenode
   */
  rpc cacheReport(CacheReportRequestProto) returns(CacheReportResponseProto);

  /**
   * Incremental block report from the DN. This contains info about recently
   * received and deleted blocks, as well as when blocks start being
   * received.
   */
  rpc blockReceivedAndDeleted(BlockReceivedAndDeletedRequestProto)
      returns(BlockReceivedAndDeletedResponseProto);

  /**
   * Report from a datanode of an error to the active namenode.
   * Used for debugging.
   */
  rpc errorReport(ErrorReportRequestProto) returns(ErrorReportResponseProto);

  /**
   * Request the version
   */
  rpc versionRequest(VersionRequestProto) returns(VersionResponseProto);

  /**
   * Report corrupt blocks at the specified location
   */
  rpc reportBadBlocks(ReportBadBlocksRequestProto) returns(ReportBadBlocksResponseProto);

  /**
   * Commit block synchronization during lease recovery.
   */
  rpc commitBlockSynchronization(CommitBlockSynchronizationRequestProto)
      returns(CommitBlockSynchronizationResponseProto);

  rpc acquire(AcquireRequest) returns(AcquireResponse);
  rpc getNamespaces(GetNamespacesRequestProto) returns(GetNamespacesResponseProto);
}
