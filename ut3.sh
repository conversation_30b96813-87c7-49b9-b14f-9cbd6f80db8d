#!/bin/bash

SCRIPTROOT=$(cd $(dirname ${BASH_SOURCE:-$0});pwd)
. $SCRIPTROOT/ut_def.sh

set -eo pipefail
trap "echo 'error: <PERSON><PERSON><PERSON> failed: see failed command above'" ERR

if [ "$#" -ne 1 ]; then
    echo "Usage: ${0} [Test|Debug|Release]"
    exit 1
fi
BUILD_TYPE=${1}
if [ "x${BUILD_TYPE}x" != "xTestx" -a "x${BUILD_TYPE}x" != "xDebugx" -a "x${BUILD_TYPE}x" != "xReleasex" ]
then
    echo "Usage: ${0} [Test|Debug|Release]"
    exit 1
fi

if [ ! -z "$TEST_CONCUR" ]; then
    GTEST_OPTIONS=" -w $TEST_CONCUR "
fi

cd "${BUILD_TYPE}/test"
source ../conf/dancenn_env
gtest_parallel=../../third_party/cfs_dancenn_thirdparty/builds/third_party/gtest-parallel/output/gtest-parallel

${gtest_parallel} $GTEST_OPTIONS ./test --gtest_filter=`echo ${flakiness_filters[@]} | sed 's/ /:/g'` --serialize_test_cases -r3
