#!/bin/bash

# disabled in UT CI
export blacklist_filters=("NnFoProcessUnTest.*"
                   "DnFoProcessUnTest.*"
                   "UploadProcessTest.*"
                   "TosReadBlockCrcTest.*"
                   "TosUfsPerfTest.*"
                   "TosUfsTest.*")

# ut3.sh
export flakiness_filters=("NameSpaceBlockPlacementTest.TestAddBlock"
                   "DatanodeManagerTest.TestChooseTarget4NewForFastCopy"
                   "VLock.Test01"
                   "BlockManagerTest.*"
                   "BlockPlacementDefaultTest.*"
                   "RuntimeMonitorTest.*")

# ut2.sh
export sequential_filters=("EditLogCfsOpTest.*"
                    "EditLogRecoveryTest.*"
                    "EditLogSenderTest.*"
                    "EditLogSerializer.*"
                    "EditLogTailerTest.*"
                    "NamespaceStatTest.*"
                    "NewEditLogTest.*"
                    "FailoveredRpcChannelTest.*"
                    "FailoveredRpcChannelTest2.*"
                    "HdfsClientTest.*"
                    "PooledRpcChannelTest.*"
                    "StatelessRpcChannelTest.*"
                    "StopWatchTest.*"
                    "ActiveStandbySyncEditlogTest.GracefulFailover")

# ut_release_only.sh
export release_filters=("DatanodeManagerPerfTest.*")

# ut_acc.sh
export acc_filters=("AccNamespaceTest.*"
             "AccNamespaceStandbyTest.*"
             "LockedPathTest.*"
             "UfsUtilTest.*"
             "TosEventTest.*")

# Others, run in parallel in ut.sh

if [[ ! -z "${LEAFBOAT_PIPELINE_ID}" ]]
then
  echo "In Codebase CI, prepare workaround for CPU Hyper-Threading"
  echo "Otherwise, sched_getcpu() < std::thread::hardware_concurrency() is possible"
  taskset -cp $(lscpu | grep "On-line CPU" | awk '{print $NF}') $$
fi
