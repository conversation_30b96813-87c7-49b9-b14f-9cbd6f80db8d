/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
syntax = "proto2";

option java_package = "com.volcengine.cloudfs.proto";
option java_outer_classname = "XAttrProtos";
option java_generate_equals_and_hash = true;
option cc_generic_services = true;
package cloudfs;

message XAttrProto {
  enum XAttrNamespaceProto {
    USER      = 0;
    TRUSTED   = 1;
    SECURITY  = 2;
    SYSTEM    = 3;
    RAW       = 4;
  }

  required XAttrNamespaceProto namespace = 1;
  required string name = 2;
  optional bytes value = 3;
}

message XAttrEditLogProto {
  optional string src = 1;
  repeated XAttrProto xAttrs = 2;
}

enum XAttrSetFlagProto {
  XATTR_CREATE     = 0x01;
  XATTR_REPLACE    = 0x02;
}

message SetXAttrRequestProto {
  required string src          = 1;
  optional XAttrProto xAttr    = 2;
  optional uint32 flag         = 3; //bits set using XAttrSetFlagProto
}

message SetXAttrResponseProto {
}

message GetXAttrsRequestProto {
  required string src = 1;
  repeated XAttrProto xAttrs = 2;
}

message GetXAttrsResponseProto {
  repeated XAttrProto xAttrs = 1;
}

message ListXAttrsRequestProto {
  required string src = 1;
}

message ListXAttrsResponseProto {
  repeated XAttrProto xAttrs = 1;
}

message RemoveXAttrRequestProto {
  required string src        = 1;
  optional XAttrProto xAttr  = 2;
}

message RemoveXAttrResponseProto {
}
