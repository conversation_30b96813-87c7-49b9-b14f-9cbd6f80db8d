/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * These .proto interfaces are private and stable.
 * Please see http://wiki.apache.org/hadoop/Compatibility
 * for what changes are allowed for a *stable* .proto interface.
 */
syntax = "proto2";

option java_package = "com.volcengine.cloudfs.proto";
option java_outer_classname = "LifecycleProtos";
option java_generate_equals_and_hash = true;

package cloudfs;

enum StorageClassProto {
  NONE = 0; // only for convenience inside of NN/DN
  HOT  = 1;
  WARM = 2;
  COLD = 3;
  IA   = 4;
  AR   = 5;
}

enum ExpirationTypeProto {
  ATIME_BASED = 0;
  MTIME_BASED = 1;
}

message ExpirationRuleProto {
  optional int64 days = 1; // deprecated in ACC

  optional int64 seconds = 2;

  // if true, whole directory will delete when directory expired.
  // if false, children directory/file will delete independently.
  optional bool recycle_whole_directory = 3;
  optional ExpirationTypeProto expiration_type = 4 [default = MTIME_BASED];
}

message TransitionRuleProto {
  optional int64 days = 1;
  optional StorageClassProto targetClass = 2;
}

// basic structure for message
message LifecyclePolicyProto {
  optional StorageClassProto defaultClass = 1;
  optional ExpirationRuleProto expRule = 2;
  repeated TransitionRuleProto transRules = 3;
}

// structure in rocksdb LifecyclePolicyCF
message LifecyclePolicyInfoProto {
  optional uint64 timestampMs = 1;
  optional LifecyclePolicyProto policy = 2;
}

// structure in rocksdb StorageClassStatCF
message StorageClassStatProto {
  optional uint64 timestampSec = 1;
  // in order of StorageClassProto NONE to AR
  repeated uint64 numReplica = 2;       // physical replica in DN
  repeated uint64 numByte = 3;          // physical byte in DN
  repeated uint64 numLogicalBlock = 4;  // logical block effective
  repeated uint64 numLogicalByte = 5;   // logical byte effective
  repeated uint64 numLogicalBlockExpected = 6;  // logical block expected
  repeated uint64 numLogicalByteExpected = 7;   // logical byte expected
}

// structure in rocksdb StorageClassReportCF
message StorageClassReportProto {
  // TODO(xuex)
  // Deprecated fields, will be cleanup during startup, see
  // LifecycleScanner::FilterDepredStorageClassReport() for detail.
  // Mark them deprecated explicitly in next version:
  // repeated string dnUuid = 1 [ deprecated = true ];
  // repeated StorageClassProto reportedClass = 2 [ deprecated = true ] ;
  repeated string dnUuid = 1;
  repeated StorageClassProto reportedClass = 2;

  // valid fields
  optional StorageClassProto stcls = 3;
  optional bool pinned = 4;
  optional bool finalized = 5;
}
