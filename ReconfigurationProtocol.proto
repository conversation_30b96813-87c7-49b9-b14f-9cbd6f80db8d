/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// This file contains protocol buffers that are used to reconfigure NameNode
// and DataNode by HDFS admin.
syntax = "proto2";

option java_package = "com.volcengine.cloudfs.proto";
option java_outer_classname = "ReconfigurationProtocolProtos";
option java_generic_services = true;
option java_generate_equals_and_hash = true;
option cc_generic_services = true;
package cloudfs;

/** Asks NN/DN to reload configuration file. */
message StartReconfigurationRequestProto {
}

message StartReconfigurationResponseProto {
}

/** Query the running status of reconfiguration process */
message GetReconfigurationStatusRequestProto {
}

message GetReconfigurationStatusConfigChangeProto {
  required string name = 1;
  required string oldValue = 2;
  optional string newValue = 3;
  optional string errorMessage = 4;  // It is empty if success.
}

message GetReconfigurationStatusResponseProto {
  required int64 startTime = 1;
  optional int64 endTime = 2;
  repeated GetReconfigurationStatusConfigChangeProto changes = 3;
}

/** Query the reconfigurable properties on NN/DN. */
message ListReconfigurablePropertiesRequestProto {
}

message ListReconfigurablePropertiesResponseProto {
  repeated string name = 1;
}

/**
 * Protocol used from client to the NN/DN.
 * See the request and response for details of rpc call.
 */
service ReconfigurationProtocolService {
  rpc getReconfigurationStatus(GetReconfigurationStatusRequestProto)
      returns(GetReconfigurationStatusResponseProto);

  rpc startReconfiguration(StartReconfigurationRequestProto)
      returns(StartReconfigurationResponseProto);

  rpc listReconfigurableProperties(
      ListReconfigurablePropertiesRequestProto)
      returns(ListReconfigurablePropertiesResponseProto);
}
