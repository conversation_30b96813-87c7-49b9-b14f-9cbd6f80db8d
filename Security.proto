/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * These .proto interfaces are private and stable.
 * Please see http://wiki.apache.org/hadoop/Compatibility
 * for what changes are allowed for a *stable* .proto interface.
 */
syntax = "proto2";

option java_package = "com.volcengine.cloudfs.proto";
option java_outer_classname = "SecurityProtos";
option java_generic_services = true;
option java_generate_equals_and_hash = true;
option cc_generic_services = true;
package cloudfs;

/**
 * Security token identifier
 */
message TokenProto {
  required bytes identifier = 1;
  required bytes password = 2;
  required string kind = 3;
  required string service = 4;
}

message GetDelegationTokenRequestProto {
  required string renewer = 1;
}

message GetDelegationTokenResponseProto {
  optional TokenProto token = 1;
}

message RenewDelegationTokenRequestProto {
  required TokenProto token = 1;
}

message RenewDelegationTokenResponseProto {
  required uint64 newExpiryTime = 1;
}

message CancelDelegationTokenRequestProto {
  required TokenProto token = 1;
}

message CancelDelegationTokenResponseProto { // void response
}

