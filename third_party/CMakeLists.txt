include(ExternalProject)

set(NEW_THIRDPARTY_DIR "${CMAKE_SOURCE_DIR}/third_party/cfs_dancenn_thirdparty/builds/third_party")

set(LIBFIU_INCLUDE_DIR "${NEW_THIRDPARTY_DIR}/libfiu/output/include")
set(LIBFIU_LIBRARY "${NEW_THIRDPARTY_DIR}/libfiu/output/lib/libfiu.a")

set(CURL_INCLUDE_DIR "${NEW_THIRDPARTY_DIR}/curl/output/include")
set(CURL_LIBRARY "${NEW_THIRDPARTY_DIR}/curl/output/lib/libcurl.a")

set(OPENSSL_INCLUDE_DIR "${NEW_THIRDPARTY_DIR}/openssl/output/include")
set(OPENSSL_SSL_LIBRARY "${NEW_THIRDPARTY_DIR}/openssl/output/lib/libssl.a")
set(OPENSSL_CRYPTO_LIBRARY "${NEW_THIRDPARTY_DIR}/openssl/output/lib/libcrypto.a")

set(AWS_CPP_SDK_S3_INCLUDE "${NEW_THIRDPARTY_DIR}/aws-sdk-cpp/output/include")
set(AWS_CPP_SDK_CORE_LIBRARY "${NEW_THIRDPARTY_DIR}/aws-sdk-cpp/output/lib/linux/intel64/RelWithDebInfo/libaws-cpp-sdk-core.a")
set(AWS_CPP_SDK_S3_LIBRARY "${NEW_THIRDPARTY_DIR}/aws-sdk-cpp/output/lib/linux/intel64/RelWithDebInfo/libaws-cpp-sdk-s3.a")
