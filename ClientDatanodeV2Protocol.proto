
// This file contains protocol buffers that are used throughout HDFS -- i.e.
// by the client, server, and data transfer protocols.
syntax = "proto2";

option java_package = "com.volcengine.cloudfs.proto";
option java_outer_classname = "ClientDatanodeV2ProtocolProtos";
option java_generic_services = true;
option java_generate_equals_and_hash = true;
option cc_generic_services = true;
package cloudfs;

import "Security.proto";
import "hdfs.proto";

enum TransType {
  KTCP = 0;
  UTCP = 1;
  RDMA_2S = 2;
  RDMA_1S = 3;
}

message BlockOpHeaderProto {
  optional ExtendedBlockProto block = 1;
  optional TokenProto token = 2;
  optional string clientName = 3;
  optional IOPriority iopriority = 4;
  // client location_tag. Used to compute cross-az thpt by DN
  optional LocationTag location_tag = 5;
  repeated TraceBaggageProto baggages = 100;
}

message GetReplicaInfoRequestProto {
  optional BlockOpHeaderProto header = 1;
}

message GetReplicaInfoResponseProto {
  optional CommonResponseHeaderProto header = 1;
  optional uint64 physicalLength = 2;
  optional uint64 visibleLength = 3;
  enum ReplicaState {
    UNFINALIZED = 0;
    SEALED = 1;
    FINALIZED = 2;
  }
  optional ReplicaState state = 4;
  optional uint32 diskId = 5;
  optional uint32 port = 6;
  optional bool evictable = 7;
  optional bool pin = 8;
  optional NamespaceType namespaceType = 9;
}

message CreateBlockRequestProto {
  optional BlockOpHeaderProto header = 1;
  optional StorageTypeProto storageType = 2;
  optional bool sync = 3;
  optional int64 timeoutMs = 10;
}

message CreateBlockResponseProto {
  optional CommonResponseHeaderProto header = 1;
  optional uint32 port = 2;
  optional string storage_id = 3;
}

message PrepareBlockRequestProto {
  optional BlockOpHeaderProto header = 1;
  optional uint64 length = 2;
  optional uint64 offset = 3;
  optional int64 timeoutMs = 10;
}

message PrepareBlockResponseProto {
  optional CommonResponseHeaderProto header = 1;
}

// Data is in byterpc attachment
message WriteBlockRequestProto {
  optional BlockOpHeaderProto header = 1;
  optional uint64 length = 2;
  optional uint64 offset = 3;
  optional bool sync = 4;
  optional uint64 visibleLength = 5;
  optional TransType trans_type = 6;
  optional int64 timeoutMs = 10;
}

message WriteBlockResponseProto {
  optional CommonResponseHeaderProto header = 1;
  optional uint32 redirectPort = 2;
}

message SyncBlockRequestProto {
  optional BlockOpHeaderProto header = 1;
  optional int64 timeoutMs = 10;
}

message SyncBlockResponseProto {
  optional CommonResponseHeaderProto header = 1;
  optional uint32 redirectPort = 2;
}

message ClientReadBlockRequestProto {
  enum ReadType {
    Read = 0;
    PRead = 1;
  }
  optional BlockOpHeaderProto header = 1;
  optional uint64 offset = 2;
  optional uint64 length = 3;
  optional ReadType type = 4;
  optional TransType trans_type = 5;
  optional uint64 stream_id = 10;
  optional CachingStrategyProto cachingStrategy = 20;
  optional int64 timeoutMs = 40;
}

// Data is in byterpc attachment
message ClientReadBlockResponseProto {
  enum ReadSource {
    SSD = 0;
    TOS = 1;
  };
  optional CommonResponseHeaderProto header = 1;
  optional uint32 redirectPort = 2;
  optional ReadSource source = 3;
}

// SealBlock will let block reject following write request.
message SealBlockRequestProto {
  optional BlockOpHeaderProto header = 1;
  optional int64 length = 2;
  optional int64 timeoutMs = 10;
}

message SealBlockResponseProto {
  optional CommonResponseHeaderProto header = 1;
  optional uint32 redirectPort = 2;
  optional int64 blockLength = 3;
  optional string storageUuid = 4;
}

message CalcBlockChecksumRequestProto {
  optional BlockOpHeaderProto header = 1;
  optional ChecksumTypeProto checksum_type = 2;
}

message CalcBlockChecksumResponseProto {
  optional CommonResponseHeaderProto header = 1;
  optional ChecksumTypeProto checksum_type = 2;
  optional uint32 checksum = 3;
  optional uint32 redirectPort = 4;
}

message PingBlockRequestProto {
  optional BlockOpHeaderProto header = 1;
  optional int64 visibleLength = 2;
}

message PingBlockResponseProto {
  optional CommonResponseHeaderProto header = 1;
  optional uint32 redirectPort = 2;
}

message FinalizeBlockRequestProto {
  optional BlockOpHeaderProto header = 1;
  optional int64 length = 2;
  optional int64 timeoutMs = 10;
}

message FinalizeBlockResponseProto {
  optional CommonResponseHeaderProto header = 1;
  optional uint32 redirectPort = 2;
}

/**
  * Protocol used from client to the Datanode.
  * See the request and response for details of rpc call.
  */
service ClientDatanodeIOService {
  rpc getReplicaInfo(GetReplicaInfoRequestProto)
      returns(GetReplicaInfoResponseProto);

  rpc createBlock(CreateBlockRequestProto)
      returns(CreateBlockResponseProto);

  rpc prepareBlock(PrepareBlockRequestProto)
      returns(PrepareBlockResponseProto);

  rpc writeBlock(WriteBlockRequestProto)
      returns(WriteBlockResponseProto);

  rpc syncBlock(SyncBlockRequestProto)
      returns(SyncBlockResponseProto);

  rpc readBlock(ClientReadBlockRequestProto)
      returns(ClientReadBlockResponseProto);

  rpc sealBlock(SealBlockRequestProto)
      returns(SealBlockResponseProto);

  rpc pingBlock(PingBlockRequestProto)
      returns(PingBlockResponseProto);

  rpc finalizeBlock(FinalizeBlockRequestProto)
      returns(FinalizeBlockResponseProto);

  rpc calcBlockChecksum(CalcBlockChecksumRequestProto)
      returns(CalcBlockChecksumResponseProto);
}