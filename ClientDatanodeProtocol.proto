/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * These .proto interfaces are private and stable.
 * Please see http://wiki.apache.org/hadoop/Compatibility
 * for what changes are allowed for a *stable* .proto interface.
 */

// This file contains protocol buffers that are used throughout HDFS -- i.e.
// by the client, server, and data transfer protocols.
syntax = "proto2";

option java_package = "com.volcengine.cloudfs.proto";
option java_outer_classname = "ClientDatanodeProtocolProtos";
option java_generic_services = true;
option java_generate_equals_and_hash = true;
option cc_generic_services = true;
package cloudfs;

import "Security.proto";
import "hdfs.proto";
import "ReconfigurationProtocol.proto";

/**
 * block - block for which visible length is requested
 */
message GetReplicaVisibleLengthRequestProto {
  required ExtendedBlockProto block = 1;
}

/**
 * length - visible length of the block
 */
message GetReplicaVisibleLengthResponseProto {
  required uint64 length = 1;
  optional uint64 physicalLength = 1000;
  optional uint64 latestGenerationStamp = 1001;
}

/**
 * block - block for which visible length is requested
 */
message GetReplicaVisibleLengthV2RequestProto {
    required ExtendedBlockProto block = 1;
}

/**
 * length - visible length of the block
 */
message GetReplicaVisibleLengthV2ResponseProto {
    required uint64 length = 1;
    required int32 state = 2;
    optional uint64 physicalLength = 1000;
    optional uint64 latestGenerationStamp = 1001 [default = 0];
}

/**
 * void request
 */
message RefreshNamenodesRequestProto {
}

/**
 * void response
 */
message RefreshNamenodesResponseProto {
}

/**
 * blockPool - block pool to be deleted
 * force - if false, delete the block pool only if it is empty.
 *         if true, delete the block pool even if it has blocks.
 */
message DeleteBlockPoolRequestProto {
  required string blockPool = 1;
  required bool force = 2;
}

/**
 * void response
 */
message DeleteBlockPoolResponseProto {
}

/**
 * Gets the file information where block and its metadata is stored
 * block - block for which path information is being requested
 * token - block token
 *
 * This message is deprecated in favor of file descriptor passing.
 */
message GetBlockLocalPathInfoRequestProto {
  required ExtendedBlockProto block = 1;
  required TokenProto token = 2;
}

/**
 * block - block for which file path information is being returned
 * localPath - file path where the block data is stored
 * localMetaPath - file path where the block meta data is stored
 *
 * This message is deprecated in favor of file descriptor passing.
 */
message GetBlockLocalPathInfoResponseProto {
  required ExtendedBlockProto block = 1;
  required string localPath = 2;
  required string localMetaPath = 3;
}

/**
 * Query for the disk locations of a number of blocks on this DN.
 * blockPoolId - the pool to query
 * blockIds - list of block IDs to query
 * tokens - list of access tokens corresponding to list of block IDs
 */
message GetHdfsBlockLocationsRequestProto {
  // Removed: HDFS-3969
  // repeated ExtendedBlockProto blocks = 1;
  repeated TokenProto tokens = 2;

  required string blockPoolId = 3;
  repeated sfixed64 blockIds = 4 [ packed = true ];
}

/**
 * volumeIds - id of each volume, potentially multiple bytes
 * volumeIndexes - for each block, an index into volumeIds specifying the volume
 *               on which it is located. If block is not present on any volume,
 *               index is set to MAX_INT.
 */
message GetHdfsBlockLocationsResponseProto {
  repeated bytes volumeIds = 1;
  repeated uint32 volumeIndexes = 2 [ packed = true ];
}

/**
 * forUpgrade - if true, clients are advised to wait for restart and quick
 *              upgrade restart is instrumented. Otherwise, datanode does
 *              the regular shutdown.
 */
message ShutdownDatanodeRequestProto {
  required bool forUpgrade = 1;
}

message ShutdownDatanodeResponseProto {
}

/**
 * Ping datanode for liveness and quick info
 */
message GetDatanodeInfoRequestProto {
}

message GetDatanodeInfoResponseProto {
  required DatanodeLocalInfoProto localInfo = 1;
}

message TriggerBlockReportRequestProto {
  required bool incremental = 1;
}

message TriggerBlockReportResponseProto {
}

message GetBlocksRequestProto {
    required string bpId = 1;
    required int64 size = 2;
}

message GetBlocksResponseProto {
    required BlocksWithLocationsProto blocks = 1;
}

/**
 * Protocol used from client to the Datanode.
 * See the request and response for details of rpc call.
 */
service ClientDatanodeProtocolService {
  /**
   * Returns the visible length of the replica
   */
  rpc getReplicaVisibleLength(GetReplicaVisibleLengthRequestProto)
      returns(GetReplicaVisibleLengthResponseProto);

  /**
   * Returns the visible length and state of the replica
   */
  rpc getReplicaVisibleLengthV2(GetReplicaVisibleLengthV2RequestProto)
      returns(GetReplicaVisibleLengthV2ResponseProto);

  /**
   * Refresh the list of federated namenodes from updated configuration.
   * Adds new namenodes and stops the deleted namenodes.
   */
  rpc refreshNamenodes(RefreshNamenodesRequestProto)
      returns(RefreshNamenodesResponseProto);

  /**
   * Delete the block pool from the datanode.
   */
  rpc deleteBlockPool(DeleteBlockPoolRequestProto)
      returns(DeleteBlockPoolResponseProto);

  /**
   * Retrieves the path names of the block file and metadata file stored on the
   * local file system.
   */
  rpc getBlockLocalPathInfo(GetBlockLocalPathInfoRequestProto)
      returns(GetBlockLocalPathInfoResponseProto);

  /**
   * Retrieve additional HDFS-specific metadata about a set of blocks stored
   * on the local file system.
   */
  rpc getHdfsBlockLocations(GetHdfsBlockLocationsRequestProto)
      returns(GetHdfsBlockLocationsResponseProto);

  rpc shutdownDatanode(ShutdownDatanodeRequestProto)
      returns(ShutdownDatanodeResponseProto);

  rpc getDatanodeInfo(GetDatanodeInfoRequestProto)
      returns(GetDatanodeInfoResponseProto);

  rpc getReconfigurationStatus(GetReconfigurationStatusRequestProto)
      returns(GetReconfigurationStatusResponseProto);

  rpc startReconfiguration(StartReconfigurationRequestProto)
      returns(StartReconfigurationResponseProto);

  rpc listReconfigurableProperties(
        ListReconfigurablePropertiesRequestProto)
        returns(ListReconfigurablePropertiesResponseProto);

  rpc triggerBlockReport(TriggerBlockReportRequestProto)
      returns(TriggerBlockReportResponseProto);

  rpc getBlocks(GetBlocksRequestProto)
      returns(GetBlocksResponseProto);
}
