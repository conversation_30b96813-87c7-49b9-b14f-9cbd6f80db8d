pipeline {
  agent { node { label 'dancenn_only' } }
  environment {
    PROJECT = 'inf/dancenn'
  }
  options {
    buildDiscarder(logRotator(numToKeepStr: '10'))
    disableConcurrentBuilds()
    skipDefaultCheckout()
  }
  triggers {
    gitlab(
      triggerOnPush: false,
      triggerOnMergeRequest: true,
      branchFilterType: 'All',
      triggerOpenMergeRequestOnPush: 'both',
      triggerOnNoteRequest: true,
      noteRegex: '<PERSON> please retry a build'
    )
  }
  post {
    success {
      updateGitlabCommitStatus name: 'data-jenkins', state: 'success'
    }
    failure {
      updateGitlabCommitStatus name: 'data-jenkins', state: 'failed'
    }
  }
  stages {
    stage("pull")  {
      steps {
        ansiColor('xterm') {
          checkout changelog: true, poll: true, scm: [
            $class: 'GitSCM',
            branches: [[name: 'origin/$gitlabSourceBranch']],
            doGenerateSubmoduleConfigurations: false,
            extensions: [[
              $class: 'PreBuildMerge',
              options: [
                fastForwardMode: 'FF',
                mergeRemote: 'origin',
                mergeStrategy: 'default',
                mergeTarget: '$gitlabTargetBranch'
              ]
            ], [
              $class: 'ChangelogToBranch',
              options: [
                compareRemote: 'origin',
                compareTarget: '$gitlabTargetBranch'
              ]
            ]],
            submoduleCfg: [],
            userRemoteConfigs: [[
              name: 'origin',
              refspec: '+refs/heads/$gitlabTargetBranch:refs/remotes/origin/$gitlabTargetBranch +refs/heads/$gitlabSourceBranch:refs/remotes/origin/$gitlabSourceBranch',
              url: "<EMAIL>:${PROJECT}"
            ]]
          ]
        }
      }
    }
    stage("test") {
      steps {
        ansiColor('xterm') {
          sh "bash build.sh Release"
        }
      }
    }
  }
}
