syntax = "proto2";

option java_package = "com.volcengine.cloudfs.proto";
option java_outer_classname = "TestProtocolProtos";
option java_generic_services = true;
option java_generate_equals_and_hash = true;
option cc_generic_services = true;
package cloudfs;

import "RpcHeader.proto";

message EchoRequestProto {
  required string payload = 1;
}

message EchoResponseProto {
  required string payload = 1;
}

service TestService {
  rpc echo(EchoRequestProto)
      returns (EchoResponseProto);
}
