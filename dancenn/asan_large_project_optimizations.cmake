# ASAN Large Project Performance Optimizations for dancenn
# Specialized configuration for large C++ projects

message("Loading ASAN large project optimizations for dancenn...")

# Performance-oriented ASAN runtime environment variables
# These should be set before running the executable
set(ASAN_RUNTIME_OPTIONS "")

# Memory allocation optimizations for large projects
list(APPEND ASAN_RUNTIME_OPTIONS "mmap_limit_mb=4096")          # Limit mmap to 4GB for large project
list(APPEND ASAN_RUNTIME_OPTIONS "malloc_context_size=5")       # Reduce stack trace depth
list(APPEND ASAN_RUNTIME_OPTIONS "max_malloc_fill_size=64")     # Limit fill size for performance

# Detection scope optimizations based on mode
if (ASAN_MINIMAL_MODE)
    # Minimal mode: fastest execution
    list(APPEND ASAN_RUNTIME_OPTIONS "detect_leaks=0")          # Disable leak detection
    list(APPEND ASAN_RUNTIME_OPTIONS "detect_odr_violation=0")  # Disable ODR violation detection
    list(APPEND ASAN_RUNTIME_OPTIONS "detect_stack_use_after_return=0") # Disable stack UAR
    list(APPEND ASAN_RUNTIME_OPTIONS "check_initialization_order=0")    # Disable init order checking
    list(APPEND ASAN_RUNTIME_OPTIONS "strict_init_order=0")     # Disable strict init order
    list(APPEND ASAN_RUNTIME_OPTIONS "fast_unwind_on_malloc=1") # Fast unwinding
    list(APPEND ASAN_RUNTIME_OPTIONS "fast_unwind_on_fatal=1")  # Fast unwinding on fatal
elseif (ASAN_FAST_MODE)
    # Fast mode: balanced performance and detection
    list(APPEND ASAN_RUNTIME_OPTIONS "detect_leaks=1")          # Keep leak detection
    list(APPEND ASAN_RUNTIME_OPTIONS "detect_odr_violation=0")  # Disable ODR violation detection
    list(APPEND ASAN_RUNTIME_OPTIONS "detect_stack_use_after_return=0") # Disable stack UAR
    list(APPEND ASAN_RUNTIME_OPTIONS "check_initialization_order=1")    # Keep init order checking
    list(APPEND ASAN_RUNTIME_OPTIONS "strict_init_order=0")     # Disable strict init order
    list(APPEND ASAN_RUNTIME_OPTIONS "fast_unwind_on_malloc=1") # Fast unwinding
elseif (ASAN_PROFILE_MODE)
    # Profile mode: optimized for profiling
    list(APPEND ASAN_RUNTIME_OPTIONS "detect_leaks=0")          # Disable leak detection for profiling
    list(APPEND ASAN_RUNTIME_OPTIONS "detect_odr_violation=0")  # Disable ODR violation detection
    list(APPEND ASAN_RUNTIME_OPTIONS "detect_stack_use_after_return=0") # Disable stack UAR
    list(APPEND ASAN_RUNTIME_OPTIONS "check_initialization_order=0")    # Disable init order checking
    list(APPEND ASAN_RUNTIME_OPTIONS "strict_init_order=0")     # Disable strict init order
    list(APPEND ASAN_RUNTIME_OPTIONS "fast_unwind_on_malloc=1") # Fast unwinding
    list(APPEND ASAN_RUNTIME_OPTIONS "fast_unwind_on_fatal=1")  # Fast unwinding on fatal
else()
    # Full mode: maximum detection capability
    list(APPEND ASAN_RUNTIME_OPTIONS "detect_leaks=1")          # Enable leak detection
    list(APPEND ASAN_RUNTIME_OPTIONS "detect_odr_violation=1")  # Enable ODR violation detection
    list(APPEND ASAN_RUNTIME_OPTIONS "detect_stack_use_after_return=1") # Enable stack UAR
    list(APPEND ASAN_RUNTIME_OPTIONS "check_initialization_order=1")    # Enable init order checking
    list(APPEND ASAN_RUNTIME_OPTIONS "strict_init_order=1")     # Enable strict init order
    list(APPEND ASAN_RUNTIME_OPTIONS "fast_unwind_on_malloc=0") # Detailed unwinding
endif()

# Large project specific optimizations
list(APPEND ASAN_RUNTIME_OPTIONS "quarantine_size_mb=256")      # Reduce quarantine size from default
list(APPEND ASAN_RUNTIME_OPTIONS "thread_local_quarantine_size_kb=64") # Reduce thread-local quarantine
list(APPEND ASAN_RUNTIME_OPTIONS "max_redzone=128")             # Limit redzone size
list(APPEND ASAN_RUNTIME_OPTIONS "redzone=32")                  # Set smaller redzone for better cache performance

# Memory mapping optimizations for large projects like dancenn
list(APPEND ASAN_RUNTIME_OPTIONS "disable_coredump=1")          # Disable core dumps for performance
list(APPEND ASAN_RUNTIME_OPTIONS "unmap_shadow_on_exit=1")      # Unmap shadow memory on exit
list(APPEND ASAN_RUNTIME_OPTIONS "print_stats=0")               # Disable statistics printing

# Error reporting optimizations
list(APPEND ASAN_RUNTIME_OPTIONS "halt_on_error=1")             # Stop on first error
list(APPEND ASAN_RUNTIME_OPTIONS "abort_on_error=1")            # Abort on error for faster debugging
list(APPEND ASAN_RUNTIME_OPTIONS "print_scariness=0")           # Disable scariness printing
list(APPEND ASAN_RUNTIME_OPTIONS "symbolize=1")                 # Keep symbolization for debugging

# dancenn specific optimizations
list(APPEND ASAN_RUNTIME_OPTIONS "replace_str=1")               # Replace string functions
list(APPEND ASAN_RUNTIME_OPTIONS "replace_intrin=1")            # Replace intrinsic functions

# Join all options with colons
string(REPLACE ";" ":" ASAN_OPTIONS_STRING "${ASAN_RUNTIME_OPTIONS}")

# Create environment setup script for dancenn
set(ASAN_ENV_SCRIPT "${CMAKE_BINARY_DIR}/setup_dancenn_asan_env.sh")
file(WRITE ${ASAN_ENV_SCRIPT} "#!/bin/bash\n")
file(APPEND ${ASAN_ENV_SCRIPT} "# ASAN Environment Setup for dancenn Large Project\n")
file(APPEND ${ASAN_ENV_SCRIPT} "export ASAN_OPTIONS=\"${ASAN_OPTIONS_STRING}\"\n")

# Additional environment variables for performance
file(APPEND ${ASAN_ENV_SCRIPT} "export MSAN_OPTIONS=\"print_stats=0\"\n")
file(APPEND ${ASAN_ENV_SCRIPT} "export TSAN_OPTIONS=\"print_stats=0\"\n")
file(APPEND ${ASAN_ENV_SCRIPT} "export UBSAN_OPTIONS=\"print_stacktrace=1:halt_on_error=1\"\n")

# Memory optimization for large projects like dancenn
file(APPEND ${ASAN_ENV_SCRIPT} "# Optimize memory usage for dancenn\n")
file(APPEND ${ASAN_ENV_SCRIPT} "ulimit -v 16777216  # Limit virtual memory to 16GB\n")
file(APPEND ${ASAN_ENV_SCRIPT} "ulimit -m 8388608   # Limit physical memory to 8GB\n")

# dancenn specific environment
file(APPEND ${ASAN_ENV_SCRIPT} "# dancenn specific optimizations\n")
file(APPEND ${ASAN_ENV_SCRIPT} "export DANCENN_ASAN_MODE=1\n")
file(APPEND ${ASAN_ENV_SCRIPT} "export GLOG_minloglevel=1  # Reduce logging overhead\n")

# Performance monitoring
file(APPEND ${ASAN_ENV_SCRIPT} "# Performance monitoring\n")
file(APPEND ${ASAN_ENV_SCRIPT} "echo \"ASAN Configuration for dancenn: ${ASAN_OPTIONS_STRING}\"\n")
file(APPEND ${ASAN_ENV_SCRIPT} "echo \"Starting dancenn with optimized ASAN settings...\"\n")

# Make script executable
execute_process(COMMAND chmod +x ${ASAN_ENV_SCRIPT})

# Print configuration summary
message("ASAN Runtime Options for dancenn: ${ASAN_OPTIONS_STRING}")
message("Environment setup script created: ${ASAN_ENV_SCRIPT}")

# Additional compiler optimizations for large projects
if (ASAN_FAST_MODE OR ASAN_MINIMAL_MODE OR ASAN_PROFILE_MODE)
    # Reduce debug info overhead
    add_compile_options(-fno-omit-frame-pointer)
    add_compile_options(-fno-optimize-sibling-calls)
    
    # Optimize for size to reduce memory overhead
    if (ASAN_MINIMAL_MODE)
        add_compile_options(-Os)  # Optimize for size
        add_compile_options(-DNDEBUG)  # Disable debug assertions
    endif()
    
    # Link-time optimizations for performance builds
    if (CMAKE_BUILD_TYPE STREQUAL "Release" OR CMAKE_BUILD_TYPE STREQUAL "RelWithDebInfo" OR ASAN_MINIMAL_MODE)
        if (GCC_VERSION VERSION_GREATER_EQUAL "4.9")
            add_compile_options(-flto=thin)  # Thin LTO for faster builds
            add_link_options(-flto=thin)
        endif()
    endif()
endif()

# Create performance testing targets for dancenn
add_custom_target(dancenn_asan_perf_test
    COMMAND echo "Running dancenn ASAN performance test..."
    COMMAND ${ASAN_ENV_SCRIPT} && time ./dancenn --help
    DEPENDS dancenn
    COMMENT "Testing dancenn ASAN performance with optimized settings"
)

# Create memory usage monitoring target for dancenn
add_custom_target(dancenn_asan_memory_monitor
    COMMAND echo "Monitoring dancenn ASAN memory usage..."
    COMMAND ${ASAN_ENV_SCRIPT} && /usr/bin/time -v ./dancenn --help
    DEPENDS dancenn
    COMMENT "Monitoring memory usage with ASAN optimizations for dancenn"
)

# Create benchmark target for different ASAN modes
add_custom_target(dancenn_asan_benchmark
    COMMAND echo "Benchmarking dancenn with different ASAN modes..."
    COMMAND echo "This will test startup time and memory usage"
    DEPENDS dancenn
    COMMENT "Benchmarking dancenn ASAN performance across different modes"
)

message("ASAN large project optimizations for dancenn loaded successfully")
