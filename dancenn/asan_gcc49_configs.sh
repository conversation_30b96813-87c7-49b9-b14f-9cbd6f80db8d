#!/bin/bash
# ASAN Runtime Configuration Script for dancenn Project - GCC 4.9.4 Compatible
# Provides optimized ASAN environment variables specifically for GCC 4.9.4

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}  ASAN Configuration for dancenn (GCC 4.9.4)${NC}"
    echo -e "${BLUE}========================================${NC}"
}

print_usage() {
    echo "Usage: $0 [mode]"
    echo ""
    echo "Available modes (GCC 4.9.4 compatible):"
    echo "  dev       - Development mode (basic detection and performance)"
    echo "  perf      - Performance mode (minimal detection, fastest)"
    echo "  debug     - Debug mode (maximum available detection)"
    echo ""
    echo "Note: This script is optimized for GCC 4.9.4 ASAN limitations"
    echo ""
    echo "Example:"
    echo "  source $0 dev    # Set development mode"
    echo "  $0 perf && ./dancenn  # Run with performance mode"
}

# Base ASAN options that work with GCC 4.9.4
BASE_OPTIONS=(
    "symbolize=1"
    "print_stacktrace=1"
    "halt_on_error=1"
    "abort_on_error=1"
)

# Development mode: basic detection and reasonable performance for GCC 4.9.4
setup_dev_mode() {
    echo -e "${GREEN}Setting up Development Mode for dancenn (GCC 4.9.4)...${NC}"
    
    local DEV_OPTIONS=(
        "${BASE_OPTIONS[@]}"
        "detect_leaks=1"
        "malloc_context_size=5"
        "quarantine_size_mb=128"
        "mmap_limit_mb=2048"
    )
    
    export ASAN_OPTIONS=$(IFS=:; echo "${DEV_OPTIONS[*]}")
    export DANCENN_FOR_ASAN=1
    export GLOG_minloglevel=1
    echo -e "${YELLOW}ASAN_OPTIONS=${ASAN_OPTIONS}${NC}"
    echo -e "${YELLOW}Note: Using GCC 4.9.4 compatible options only${NC}"
}

# Performance mode: minimal detection, maximum speed for GCC 4.9.4
setup_perf_mode() {
    echo -e "${GREEN}Setting up Performance Mode for dancenn (GCC 4.9.4)...${NC}"
    
    local PERF_OPTIONS=(
        "${BASE_OPTIONS[@]}"
        "detect_leaks=0"
        "malloc_context_size=3"
        "quarantine_size_mb=64"
        "mmap_limit_mb=1024"
    )
    
    export ASAN_OPTIONS=$(IFS=:; echo "${PERF_OPTIONS[*]}")
    export DANCENN_FOR_ASAN=1
    export GLOG_minloglevel=2
    echo -e "${YELLOW}ASAN_OPTIONS=${ASAN_OPTIONS}${NC}"
    echo -e "${YELLOW}Note: Performance mode with GCC 4.9.4 limitations${NC}"
}

# Debug mode: maximum available detection for GCC 4.9.4
setup_debug_mode() {
    echo -e "${GREEN}Setting up Debug Mode for dancenn (GCC 4.9.4)...${NC}"
    
    local DEBUG_OPTIONS=(
        "${BASE_OPTIONS[@]}"
        "detect_leaks=1"
        "malloc_context_size=10"
        "quarantine_size_mb=256"
        "mmap_limit_mb=4096"
    )
    
    export ASAN_OPTIONS=$(IFS=:; echo "${DEBUG_OPTIONS[*]}")
    export DANCENN_FOR_ASAN=1
    export GLOG_minloglevel=0
    echo -e "${YELLOW}ASAN_OPTIONS=${ASAN_OPTIONS}${NC}"
    echo -e "${RED}Warning: Debug mode will impact dancenn performance${NC}"
    echo -e "${YELLOW}Note: Limited detection capabilities due to GCC 4.9.4${NC}"
}

# Set environment variables for large projects with GCC 4.9.4
set_large_project_env() {
    # Conservative memory limits for GCC 4.9.4
    ulimit -v 8388608 2>/dev/null || echo "Warning: Could not set virtual memory limit"
    ulimit -m 4194304 2>/dev/null || echo "Warning: Could not set physical memory limit"
    
    # dancenn specific environment
    export DANCENN_FOR_ASAN=1
    
    echo -e "${BLUE}Large project environment variables set for dancenn (GCC 4.9.4)${NC}"
}

# Performance monitoring function
monitor_performance() {
    echo -e "${BLUE}Performance Monitoring for dancenn (GCC 4.9.4)${NC}"
    echo "Use these commands to monitor ASAN performance:"
    echo "  time ./dancenn [args]                    # Basic timing"
    echo "  /usr/bin/time -v ./dancenn [args]        # Detailed memory usage"
    echo ""
    echo "Note: GCC 4.9.4 ASAN has limited performance optimization options"
}

# Show GCC 4.9.4 specific limitations
show_limitations() {
    echo -e "${YELLOW}GCC 4.9.4 ASAN Limitations:${NC}"
    echo "  - No use-after-scope detection"
    echo "  - No ODR violation detection"
    echo "  - Limited runtime options"
    echo "  - Basic stack-use-after-return detection"
    echo "  - Fewer performance tuning options"
    echo ""
    echo "Consider upgrading to GCC 5.0+ or Clang for better ASAN support"
}

# Main execution
main() {
    print_header
    
    if [ $# -eq 0 ]; then
        print_usage
        show_limitations
        return 1
    fi
    
    case "$1" in
        "dev"|"development")
            setup_dev_mode
            ;;
        "perf"|"performance")
            setup_perf_mode
            ;;
        "debug")
            setup_debug_mode
            ;;
        "help"|"-h"|"--help")
            print_usage
            show_limitations
            return 0
            ;;
        *)
            echo -e "${RED}Error: Unknown mode '$1'${NC}"
            print_usage
            return 1
            ;;
    esac
    
    set_large_project_env
    monitor_performance
    
    echo ""
    echo -e "${GREEN}ASAN configuration applied successfully for dancenn (GCC 4.9.4)!${NC}"
    echo -e "${BLUE}You can now run dancenn with GCC 4.9.4 compatible ASAN settings.${NC}"
}

# Execute main function if script is run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
