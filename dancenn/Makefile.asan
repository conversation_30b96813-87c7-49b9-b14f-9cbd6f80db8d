# ASAN Optimized Build Makefile for dancenn
# Usage: make -f Makefile.asan [target]

# Build configuration
BUILD_TYPE ?= Debug
ASAN_MODE ?= fast
BUILD_SCRIPT := ./build.sh

# Available ASAN modes
VALID_ASAN_MODES := minimal fast profile full

# Validate ASAN mode
ifeq ($(filter $(ASAN_MODE),$(VALID_ASAN_MODES)),)
    $(error Invalid ASAN_MODE: $(ASAN_MODE). Valid modes: $(VALID_ASAN_MODES))
endif

# Default target
.PHONY: all
all: build

# Help target
.PHONY: help
help:
	@echo "ASAN Optimized Build System for dancenn"
	@echo ""
	@echo "Available targets:"
	@echo "  build         - Build dancenn with ASAN optimizations"
	@echo "  clean         - Clean build directory"
	@echo "  rebuild       - Clean and rebuild"
	@echo "  test          - Run basic tests with ASAN"
	@echo "  benchmark     - Run performance benchmarks"
	@echo "  profile       - Run with profiling"
	@echo "  run-dev       - Run with development ASAN settings"
	@echo "  run-perf      - Run with performance ASAN settings"
	@echo "  run-debug     - Run with debug ASAN settings"
	@echo "  memory-check  - Run with memory usage monitoring"
	@echo "  compare-modes - Compare performance across ASAN modes"
	@echo ""
	@echo "Configuration Options:"
	@echo "  BUILD_TYPE    - Build type (Debug|Release) [default: $(BUILD_TYPE)]"
	@echo "  ASAN_MODE     - ASAN mode (minimal|fast|profile|full) [default: $(ASAN_MODE)]"
	@echo ""
	@echo "ASAN Mode Details:"
	@echo "  minimal  - Fastest execution, basic detection (~1.5-2x slowdown)"
	@echo "  fast     - Balanced performance/detection (~2-3x slowdown)"
	@echo "  profile  - Optimized for profiling (~2.5-3.5x slowdown)"
	@echo "  full     - Maximum detection capability (~3-5x slowdown)"
	@echo ""
	@echo "Examples:"
	@echo "  make -f Makefile.asan BUILD_TYPE=Release ASAN_MODE=minimal build"
	@echo "  make -f Makefile.asan ASAN_MODE=profile run-dev"
	@echo "  make -f Makefile.asan benchmark"

# Build dancenn with ASAN
.PHONY: build
build:
	@echo "Building dancenn with ASAN optimizations..."
	@echo "Build Type: $(BUILD_TYPE)"
	@echo "ASAN Mode: $(ASAN_MODE)"
	$(BUILD_SCRIPT) $(BUILD_TYPE) --with-asan $(ASAN_MODE)
	@echo "Build complete!"

# Clean build directory
.PHONY: clean
clean:
	@echo "Cleaning build directory..."
	rm -rf builds $(BUILD_TYPE)
	@echo "Clean complete!"

# Rebuild (clean + build)
.PHONY: rebuild
rebuild: clean build

# Run basic tests
.PHONY: test
test: build
	@echo "Running basic tests with ASAN (GCC 4.9.4 compatible)..."
	cd $(BUILD_TYPE) && source ../asan_gcc49_configs.sh dev && ./bin/dancenn --help
	@echo "Basic tests complete!"

# Performance benchmark
.PHONY: benchmark
benchmark:
	@echo "Running performance benchmark across ASAN modes..."
	@echo "1. Building minimal mode..."
	$(MAKE) -f Makefile.asan ASAN_MODE=minimal BUILD_TYPE=Release build > /dev/null 2>&1
	@echo "   Testing minimal mode:"
	@cd Release && source ../asan_gcc49_configs.sh perf && time ./bin/dancenn --help 2>&1 | grep real || true

	@echo "2. Building fast mode..."
	$(MAKE) -f Makefile.asan ASAN_MODE=fast BUILD_TYPE=Release build > /dev/null 2>&1
	@echo "   Testing fast mode:"
	@cd Release && source ../asan_gcc49_configs.sh dev && time ./bin/dancenn --help 2>&1 | grep real || true

	@echo "3. Building full mode..."
	$(MAKE) -f Makefile.asan ASAN_MODE=full BUILD_TYPE=Debug build > /dev/null 2>&1
	@echo "   Testing full mode:"
	@cd Debug && source ../asan_gcc49_configs.sh debug && time ./bin/dancenn --help 2>&1 | grep real || true
	
	@echo "Performance benchmark complete!"

# Profiling run
.PHONY: profile
profile: 
	@echo "Building for profiling..."
	$(MAKE) -f Makefile.asan ASAN_MODE=profile BUILD_TYPE=Debug build
	@echo "Running with profiling configuration..."
	cd $(BUILD_TYPE) && source ../asan_runtime_configs.sh profile && ./bin/dancenn --help
	@echo "Profiling run complete!"

# Development run
.PHONY: run-dev
run-dev: build
	@echo "Running dancenn with development ASAN settings..."
	cd $(BUILD_TYPE) && source ../asan_runtime_configs.sh dev && ./bin/dancenn

# Performance run
.PHONY: run-perf
run-perf:
	@echo "Building and running with performance settings..."
	$(MAKE) -f Makefile.asan ASAN_MODE=minimal BUILD_TYPE=Release build
	cd Release && source ../asan_runtime_configs.sh perf && ./bin/dancenn

# Debug run
.PHONY: run-debug
run-debug:
	@echo "Building and running with debug settings..."
	$(MAKE) -f Makefile.asan ASAN_MODE=full BUILD_TYPE=Debug build
	cd Debug && source ../asan_runtime_configs.sh debug && ./bin/dancenn

# Memory usage check
.PHONY: memory-check
memory-check: build
	@echo "Running memory usage check..."
	cd $(BUILD_TYPE) && source ../asan_runtime_configs.sh dev && /usr/bin/time -v ./bin/dancenn --help

# Compare performance across different ASAN modes
.PHONY: compare-modes
compare-modes:
	@echo "Comparing performance across ASAN modes..."
	@echo "Building and testing all modes..."
	@echo ""
	
	@echo "=== Minimal Mode (Fastest) ==="
	@$(MAKE) -f Makefile.asan ASAN_MODE=minimal BUILD_TYPE=Release build > /dev/null 2>&1
	@cd Release && source ../asan_runtime_configs.sh perf && \
		echo "Memory usage:" && /usr/bin/time -f "Peak Memory: %M KB, Time: %E" ./bin/dancenn --help 2>&1 | grep -E "(Peak Memory|Time):" || true
	@echo ""
	
	@echo "=== Fast Mode (Balanced) ==="
	@$(MAKE) -f Makefile.asan ASAN_MODE=fast BUILD_TYPE=Release build > /dev/null 2>&1
	@cd Release && source ../asan_runtime_configs.sh dev && \
		echo "Memory usage:" && /usr/bin/time -f "Peak Memory: %M KB, Time: %E" ./bin/dancenn --help 2>&1 | grep -E "(Peak Memory|Time):" || true
	@echo ""
	
	@echo "=== Profile Mode (Analysis) ==="
	@$(MAKE) -f Makefile.asan ASAN_MODE=profile BUILD_TYPE=Debug build > /dev/null 2>&1
	@cd Debug && source ../asan_runtime_configs.sh profile && \
		echo "Memory usage:" && /usr/bin/time -f "Peak Memory: %M KB, Time: %E" ./bin/dancenn --help 2>&1 | grep -E "(Peak Memory|Time):" || true
	@echo ""
	
	@echo "=== Full Mode (Maximum Detection) ==="
	@$(MAKE) -f Makefile.asan ASAN_MODE=full BUILD_TYPE=Debug build > /dev/null 2>&1
	@cd Debug && source ../asan_runtime_configs.sh debug && \
		echo "Memory usage:" && /usr/bin/time -f "Peak Memory: %M KB, Time: %E" ./bin/dancenn --help 2>&1 | grep -E "(Peak Memory|Time):" || true
	@echo ""
	
	@echo "Performance comparison complete!"

# Install runtime configuration
.PHONY: install-config
install-config:
	@echo "Installing ASAN runtime configuration..."
	chmod +x asan_runtime_configs.sh
	@echo "Runtime configuration installed!"
	@echo "Usage: source ./asan_runtime_configs.sh [mode]"

# Show current configuration
.PHONY: show-config
show-config:
	@echo "Current ASAN Configuration:"
	@echo "  BUILD_TYPE: $(BUILD_TYPE)"
	@echo "  ASAN_MODE: $(ASAN_MODE)"
	@echo "  BUILD_SCRIPT: $(BUILD_SCRIPT)"

# Generate performance report
.PHONY: perf-report
perf-report: build
	@echo "Generating performance report..."
	@mkdir -p reports
	@echo "dancenn ASAN Performance Report - $(shell date)" > reports/asan_performance.txt
	@echo "=======================================" >> reports/asan_performance.txt
	@echo "" >> reports/asan_performance.txt
	@echo "Configuration:" >> reports/asan_performance.txt
	@echo "  Build Type: $(BUILD_TYPE)" >> reports/asan_performance.txt
	@echo "  ASAN Mode: $(ASAN_MODE)" >> reports/asan_performance.txt
	@echo "" >> reports/asan_performance.txt
	@echo "Performance Test Results:" >> reports/asan_performance.txt
	@cd $(BUILD_TYPE) && source ../asan_runtime_configs.sh $(ASAN_MODE) && \
		/usr/bin/time -v ./bin/dancenn --help 2>&1 >> ../reports/asan_performance.txt
	@echo "Performance report generated: reports/asan_performance.txt"

# Validate ASAN setup
.PHONY: validate
validate: build
	@echo "Validating ASAN setup for dancenn..."
	@echo "1. Checking if ASAN is properly linked..."
	@cd $(BUILD_TYPE) && ldd bin/dancenn | grep -i asan || echo "Warning: ASAN library not found in ldd output"
	@echo "2. Testing basic ASAN functionality..."
	@cd $(BUILD_TYPE) && source ../asan_runtime_configs.sh dev && ./bin/dancenn --help > /dev/null 2>&1 && echo "✓ Basic functionality test passed"
	@echo "3. Checking ASAN environment..."
	@cd $(BUILD_TYPE) && source ../asan_runtime_configs.sh dev && echo "ASAN_OPTIONS=$$ASAN_OPTIONS"
	@echo "ASAN validation complete!"

# Run unit tests with ASAN
.PHONY: unittest
unittest: build
	@echo "Running unit tests with ASAN..."
	cd $(BUILD_TYPE) && source ../asan_runtime_configs.sh dev && ./test/dancenn_unittest
	@echo "Unit tests complete!"

# Quick performance test
.PHONY: quick-perf
quick-perf:
	@echo "Quick performance test (minimal mode)..."
	$(MAKE) -f Makefile.asan ASAN_MODE=minimal BUILD_TYPE=Release build > /dev/null 2>&1
	cd Release && source ../asan_runtime_configs.sh perf && time ./bin/dancenn --help

.PHONY: all build clean rebuild test benchmark profile run-dev run-perf run-debug memory-check compare-modes install-config show-config perf-report validate unittest quick-perf help
