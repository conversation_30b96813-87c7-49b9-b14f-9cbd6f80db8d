cmake_minimum_required(VERSION 3.0.2)
project(dancenn C CXX)

set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

find_program(CCACHE_FOUND ccache)
if(CCACHE_FOUND)
    set_property(GLOBAL PROPERTY RULE_LAUNCH_COMPILE ccache)
    set_property(GLOBAL PROPERTY RULE_LAUNCH_LINK ccache)
endif()

if(DEFINED ENV{DANCENN_DISABLE_UFS_HDFS})
  set(CMAKE_DANCENN_DISABLE_UFS_HDFS "from_env")
endif ()

# Add arm64 support
execute_process(COMMAND uname -m OUTPUT_VARIABLE ARCH)
message("Arch :${ARCH}")
if ("${ARCH}" MATCHES "arm64")
  set(ARCH "aarch64")
endif ()
if ("${ARCH}" MATCHES "aarch64")
  set(CMAKE_DANCENN_DISABLE_UFS_HDFS "from_arm")
  set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fsigned-char -ffp-contract=off")
endif ()

if(DEFINED CMAKE_DANCENN_DISABLE_UFS_HDFS)
  message("set CMAKE_DANCENN_DISABLE_UFS_HDFS ${CMAKE_DANCENN_DISABLE_UFS_HDFS}")

  add_definitions(-DDANCENN_DISABLE_UFS_HDFS)
endif()

if (ENABLE_GCOV)
  message("ENABLE GCOV")
  add_compile_options("--coverage")
  add_link_options("--coverage")
else ()
  message("DISABLE GCOV")
endif ()

# AddressSanitizer (ASAN) Performance-Optimized Configuration
if (ENABLE_ASAN)
  message("ENABLE ASAN with performance optimizations")

  # Check compiler version for ASAN compatibility
  execute_process(COMMAND ${CMAKE_CXX_COMPILER} -dumpversion OUTPUT_VARIABLE COMPILER_VERSION)
  string(STRIP ${COMPILER_VERSION} COMPILER_VERSION)
  message("Compiler: ${CMAKE_CXX_COMPILER_ID} Version: ${COMPILER_VERSION}")

  # Detect Apple Clang specifically
  set(IS_APPLE_CLANG FALSE)
  if (CMAKE_CXX_COMPILER_ID STREQUAL "AppleClang")
    set(IS_APPLE_CLANG TRUE)
    message("Detected Apple Clang - using Apple Clang compatible ASAN flags")
  elseif (CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
    message("Detected Clang - using Clang compatible ASAN flags")
  elseif (CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
    message("Detected GCC - using GCC compatible ASAN flags")
  endif()

  # Performance-optimized ASAN configuration options
  option(ASAN_FAST_MODE "Enable fast ASAN mode with reduced checks" OFF)
  option(ASAN_MINIMAL_MODE "Enable minimal ASAN mode for performance testing" OFF)
  option(ASAN_PROFILE_MODE "Enable ASAN with profiling optimizations" OFF)
  option(ASAN_LARGE_PROJECT_MODE "Enable optimizations for large projects" ON)

  # Base ASAN compiler flags with performance optimizations
  add_compile_options(-fsanitize=address)
  add_compile_options(-fno-omit-frame-pointer)

  # Additional performance-oriented flags for large projects (GCC 4.9.4 compatible)
  if (ASAN_LARGE_PROJECT_MODE)
    # GCC 4.9.4 has very limited ASAN support - only use basic flags
    if (CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
      if (COMPILER_VERSION VERSION_GREATER_EQUAL "5.0")
        add_compile_options(-fsanitize-address-use-after-scope)
      else()
        message("GCC 4.9.4 detected - using basic ASAN flags only")
        # No advanced flags for GCC 4.9.4
      endif()
    elseif (CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
      # Clang flags (if using Clang)
      if (COMPILER_VERSION VERSION_GREATER_EQUAL "6.0")
        add_compile_options(-fsanitize-address-use-after-scope)
        add_compile_options(-fno-sanitize-recover=address)
      endif()
    endif()
  endif()

  # Optimize debug info generation for performance
  if (ASAN_FAST_MODE OR ASAN_MINIMAL_MODE)
    add_compile_options(-g1)  # Minimal debug info for better performance
    message("Using minimal debug info (-g1) for fast ASAN mode")
  else()
    add_compile_options(-g)   # Full debug info for complete stack traces
  endif()

  # Performance-oriented compiler optimizations (GCC 4.9.4 compatible)
  if (ASAN_MINIMAL_MODE)
    # Minimal mode: fastest execution with basic memory error detection
    add_compile_options(-O2)  # Higher optimization level
    message("ASAN Minimal Mode: O2 optimization (GCC 4.9.4 compatible)")
  elseif (ASAN_FAST_MODE)
    # Fast mode: balanced performance and detection capability
    add_compile_options(-O1)  # Moderate optimization
    message("ASAN Fast Mode: O1 optimization (GCC 4.9.4 compatible)")
  elseif (ASAN_PROFILE_MODE)
    # Profile mode: optimized for profiling and performance analysis
    add_compile_options(-O1)
    add_compile_options(-g1)  # Minimal debug info for profiling
    add_compile_options(-fno-inline-functions)  # Better profiling accuracy
    message("ASAN Profile Mode: Optimized for performance profiling (GCC 4.9.4 compatible)")
  else()
    # Full mode: maximum detection capability
    if (CMAKE_CXX_COMPILER_ID STREQUAL "GNU" AND COMPILER_VERSION VERSION_LESS "5.0")
      message("Using GCC 4.9.4 compatible ASAN flags")
      add_compile_options(-O1)  # GCC 4.9.4 works better with O1 than O0 for ASAN
    else()
      add_compile_options(-O0)  # No optimization for maximum detection
      # Only add advanced flags for newer compilers
      if (CMAKE_CXX_COMPILER_ID STREQUAL "GNU" AND COMPILER_VERSION VERSION_GREATER_EQUAL "5.0")
        add_compile_options(-fsanitize-address-use-after-scope)
      elseif (CMAKE_CXX_COMPILER_ID STREQUAL "Clang" AND COMPILER_VERSION VERSION_GREATER_EQUAL "6.0")
        add_compile_options(-fsanitize-address-use-after-scope)
      endif()
    endif()
    message("ASAN Full Mode: Maximum detection capability")
  endif()

  # ASAN linker flags with performance considerations
  add_link_options(-fsanitize=address)

  # Optimized static linking based on compiler version
  if (CMAKE_CXX_COMPILER_ID STREQUAL "GNU" AND COMPILER_VERSION VERSION_GREATER_EQUAL "4.9")
    add_link_options(-static-libasan)
    message("Using -static-libasan for static ASAN linking (GCC 4.9.4)")
  else()
    message("Using alternative static linking approach")
    add_link_options(-Wl,-Bstatic -lasan -Wl,-Bdynamic)
  endif()

  # Additional static linking for better portability
  add_link_options(-static-libgcc)
  add_link_options(-static-libstdc++)

  # Performance tuning definitions
  if (ASAN_FAST_MODE OR ASAN_MINIMAL_MODE OR ASAN_PROFILE_MODE)
    add_definitions(-DASAN_PERFORMANCE_MODE)
  endif()

  # Large project specific optimizations (GCC 4.9.4 compatible)
  if (ASAN_LARGE_PROJECT_MODE)
    add_definitions(-DASAN_LARGE_PROJECT_MODE)
    # Only add supported flags for GCC 4.9.4
    if (CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
      if (COMPILER_VERSION VERSION_GREATER_EQUAL "4.8")
        # GCC 4.9.4 supports these basic optimizations
        add_compile_options(-ftemplate-backtrace-limit=10)
        add_link_options(-Wl,--gc-sections)
      endif()
    endif()
  endif()

  # Disable jemalloc when using ASAN as they conflict
  add_definitions(-DDANCENN_FOR_ASAN)

  # Print configuration summary
  if (ASAN_MINIMAL_MODE)
    message("ASAN Configuration: Minimal Mode (fastest, basic detection)")
  elseif (ASAN_FAST_MODE)
    message("ASAN Configuration: Fast Mode (balanced performance/detection)")
  elseif (ASAN_PROFILE_MODE)
    message("ASAN Configuration: Profile Mode (optimized for profiling)")
  else()
    message("ASAN Configuration: Full Mode (maximum detection)")
  endif()

  if (ASAN_LARGE_PROJECT_MODE)
    message("Large Project Optimizations: ENABLED")
  endif()

  message("ASAN compile flags configured with performance optimizations")

  # Include large project optimizations
  include(asan_large_project_optimizations.cmake OPTIONAL)
else ()
  message("DISABLE ASAN")
endif ()

if (BUILD_VERSION)
message(STATUS "Build version is " ${BUILD_VERSION})
add_definitions(-DBUILD_VERSION=\"${BUILD_VERSION}\")
add_definitions(-DDANCENN_MAJOR_VERSION=${DANCENN_MAJOR_VERSION})
add_definitions(-DDANCENN_MINOR_VERSION=${DANCENN_MINOR_VERSION})
add_definitions(-DDANCENN_PATCH_VERSION=${DANCENN_PATCH_VERSION})
endif()

set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++14 -pthread -g -Werror=return-type -Wdelete-incomplete -DTHREADED")
IF(${CMAKE_SYSTEM_NAME} STREQUAL "Linux")
  set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -ldl -Wno-packed-bitfield-compat")
ENDIF()

IF (CMAKE_BUILD_TYPE STREQUAL "Release" OR CMAKE_BUILD_TYPE STREQUAL "RelWithDebInfo")
  MESSAGE("Cmake build type is ${CMAKE_BUILD_TYPE}, optimization level is set to O3")
  set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -O3")
ELSE()
  MESSAGE("Cmake build type is ${CMAKE_BUILD_TYPE}, optimization level is set to O0")
  set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -O0 -DFIU_ENABLE")
ENDIF()

include(${CMAKE_SOURCE_DIR}/third_party/cfs_dancenn_thirdparty/exported_modules/CMakeLists.txt)
list(APPEND CMAKE_MODULE_PATH "${CMAKE_SOURCE_DIR}/cmake/modules/")

find_package(DancennJni REQUIRED)

if(NOT DEFINED CMAKE_DANCENN_DISABLE_UFS_HDFS)
  find_package(Hdfsclient REQUIRED)
endif ()

set(DANCENN_INCLUDE_DIR
  "src"
  "src/proto/generated/cloudfs"
  "src/proto/generated/dancenn"
  "src/proto/generated/btrace"
  "src/proto/generated/databus"
  "${PROTOBUF_INCLUDE_DIR}"
  "${ROCKSDB_INCLUDE_DIR}"
  "${Z_INCLUDE_DIR}"
  "${SNAPPY_INCLUDE_DIR}"
  "${ZSTD_INCLUDE_DIR}"
  "${BZ2_INCLUDE_DIR}"
  "${JEMALLOC_INCLUDE_DIR}"
  "${LZ4_INCLUDE_DIR}"
  "${GFLAGS_INCLUDE_DIR}"
  "${GTEST_INCLUDE_DIR}"
  "${LZMA_INCLUDE_DIR}"
  "${GLOG_INCLUDE_DIR}"
  "${CNETPP_INCLUDE_DIR}"
  "${OPENSSL_INCLUDE_DIR}"
  "${JNI_INCLUDE_DIR}"
  "${JNI_MD_INCLUDE_DIR}"
  "${ZOOKEEPER_INCLUDE_DIR}"
  "${HIREDIS_INCLUDE_DIR}"
  "${ARROW_INCLUDE_DIR}"
  "${BOOST_INCLUDE_DIR}"
  "${CURL_INCLUDE_DIR}"
  "${AWS_S3_INCLUDE_DIR}"
  "${LIBFIU_INCLUDE_DIR}"
  "${NLOHMANN_JSON_INCLUDE_DIR}"
  "${ABSEIL_INCLUDE_DIR}"
  "${LIBRDKAFKA_INCLUDE_DIR}"
  "${UNWIND_INCLUDE_DIR}"
  "${ROCKETMQ_INCLUDE_DIR}"
)
if(NOT DEFINED CMAKE_DANCENN_DISABLE_UFS_HDFS)
  list(APPEND DANCENN_INCLUDE_DIR "${HDFSCLIENT_INCLUDE_DIR}")
endif ()
INCLUDE_DIRECTORIES(AFTER ${DANCENN_INCLUDE_DIR})

IF (NOT UNWIND_INCLUDE_DIR STREQUAL "")
  INCLUDE_DIRECTORIES(AFTER "${UNWIND_INCLUDE_DIR}")
ENDIF()

execute_process(COMMAND "src/gen_version.sh" "src/version.h"
  WORKING_DIRECTORY ${PROJECT_SOURCE_DIR} RESULT_VARIABLE res_var)
if(NOT "${res_var}" STREQUAL "0")
  message(FATAL_ERROR "process failed res_var='${res_var}' for src/gen_version.sh")
endif()

set(CCACHE_DIR $ENV{CCACHE_DIR})
if(NOT DEFINED CCACHE_DIR)
  message("CCACHE_DIR not found, in full build mode")
  execute_process(COMMAND "src/proto/build.sh"
    WORKING_DIRECTORY ${PROJECT_SOURCE_DIR})
  execute_process(COMMAND "src/edit/gen.py"
    WORKING_DIRECTORY ${PROJECT_SOURCE_DIR})
endif()

file(GLOB FLAG_FILE "src/flags.cc")
file(GLOB DANCENN_SERVER_FILES "src/server/dancenn.cc")
file(GLOB IMAGE_DIFF_TOOL_FILES "src/tool/image_diff.cc")
file(GLOB PRINT_EDITLOG_TOOL_FILES "src/tool/print_editlog.cc")
file(GLOB PRINT_ROCKSDB_TOOL_FILES "src/tool/print_rocksdb.cc")
file(GLOB DUMP_ROCKSDB_TOOL_FILES "src/tool/dump_rocksdb.cc")
file(GLOB VERIFY_CORRECTNESS_TOOL_FILES "src/tool/verify_correctness.cc")
file(GLOB CROP_INODE_TREE_TOOL_FILES "src/tool/crop_inode_tree.cc")
file(GLOB PATH_VIEWER_TOOL_FILES "src/tool/path_viewer.cc")
file(GLOB PRINT_DIRSTAT_TOOL_FILS "src/tool/print_dirstat.cc")
file(GLOB RECOVER_LEASE_TOOL_FILES "src/tool/recover_lease.cc")
file(GLOB DELETE_TOS_OBJECTS_FILES "src/tool/delete_tos_objects.cc")
file(GLOB BLOCK_RECYCLER_BENCHMARK_TOOL_FILES "src/tool/block_recycler_benchmark.cc")
file(GLOB FSIMAGE_TRANSFER_TOOL_FILES "src/tool/fsimage_transfer.cc")
file(GLOB DANCENN_HAADMIN_TOOL_FILES "src/tool/dancenn_haadmin.cc")
file(GLOB GS_CHECKER_TOOL_FILES "src/tool/gs_checker.cc")
file(GLOB TOS_UTIL_TOOL_FILES "src/tool/tos_util.cc")
file(GLOB TOS_UTIL_RAW_TOOL_FILES "src/tool/tos_util_raw.cc")
file(GLOB SERVICE_FILES "src/service/*.cc" "src/service/*.h" "src/service/acc/*.cc" "src/service/acc/*.h")
file(GLOB CKPT_CATCHER_TOOL_FILES "src/tool/ckpt_catcher.cc")
file(GLOB CKPT_COMPARATOR_TOOL_FILES "src/tool/ckpt_comparator.cc")
file(GLOB TOS_ASSUMEROLE_VERIFY_TOOL_FILES "src/tool/tos_assumerole_verify.cc")
file(GLOB BASE_FILES "src/base/*.cc" "src/base/*.h" "src/base/trace/*.cc" "src/base/trace/*.h")
file(GLOB EDIT_FILES "src/edit/*.cc" "src/edit/*.h")
file(GLOB RPC_FILES "src/rpc/*.cc" "src/rpc/*.h")
file(GLOB HTTP_FILES "src/http/*.cc" "src/http/*.h")
file(GLOB BLOCK_MANAGER_FILES "src/block_manager/*.cc" "src/block_manager/*.h")
file(GLOB DATANODE_MANAGER_FILES "src/datanode_manager/*.cc" "src/datanode_manager/*.h")
file(GLOB NAMESPACE_FILES "src/namespace/*.cc" "src/namespace/*.h")
file(GLOB ACC_FILES
  "src/acc/*.cc" "src/acc/*.h"
  "src/acc/task/*.cc" "src/acc/task/*.h"
)
file(GLOB SNAPSHOT_FILES "src/snapshot/*.cc" "src/snapshot/*.h")
file(GLOB LEASE_FILES "src/lease/*.cc" "src/lease/*.h")
file(GLOB FSIMAGE_FILES "src/fsimage/*.cc" "src/fsimage/*.h")
file(GLOB HA_FILES "src/ha/*.cc" "src/ha/*.h")
file(GLOB SAFEMODE_FILES "src/safemode/*.cc" "src/safemode/*.h")
file(GLOB OP_TASK_FILES "src/op_task/*.cc" "src/op_task/*.h")
file(GLOB PROXY_FILES "src/proxy/*.cc" "src/proxy/*.h")
file(GLOB SECURITY_FILES "src/security/*.cc" "src/security/*.h")
file(GLOB MOCKDN_FILES "src/mockdn/*.cc" "src/mockdn/*.h")
file(GLOB IAM_FILES "src/iam/*.cc" "src/iam/*.h" "src/iam/model/*.cc" "src/iam/model/*.h")
file(GLOB BRIDGER_FILES "src/bridger/*.cc" "src/bridger/*.h")
file(GLOB JOB_MANAGER_FILES "src/job_manager/*.cc" "src/job_manager/*.h")
file(GLOB THIRD_FILES "src/third/*.cc" "src/third/*.h")
file(GLOB UFS_FILES
  "src/ufs/*.cc" "src/ufs/*.h"
  "src/ufs/**/*.cc" "src/ufs/**/*.h"
)
file(GLOB VOLC_FILES "src/volc/*.cc" "src/volc/*.h")
file(GLOB CLOUDFS_PROTO_FILES
  "src/proto/generated/cloudfs/*.pb.cc"
  "src/proto/generated/cloudfs/*.pb.h")
file(GLOB DANCENN_PROTO_FILES
        "src/proto/generated/dancenn/*.pb.cc"
        "src/proto/generated/dancenn/*.pb.h")
file(GLOB BTRACE_PROTO_FILES
        "src/proto/generated/btrace/*.pb.cc"
        "src/proto/generated/btrace/*.pb.h")
file(GLOB DATABUS_PROTO_FILES
        "src/proto/generated/databus/*.pb.cc"
        "src/proto/generated/databus/*.pb.h")


add_library(dancenn_common STATIC
  ${FLAG_FILE}
  ${BASE_FILES}
  ${EDIT_FILES}
  ${RPC_FILES}
  ${HTTP_FILES}
  ${SERVICE_FILES}
  ${BLOCK_MANAGER_FILES}
  ${DATANODE_MANAGER_FILES}
  ${NAMESPACE_FILES}
  ${ACC_FILES}
  ${SNAPSHOT_FILES}
  ${LEASE_FILES}
  ${FSIMAGE_FILES}
  ${HA_FILES}
  ${SAFEMODE_FILES}
  ${OP_TASK_FILES}
  ${PROXY_FILES}
  ${SECURITY_FILES}
  ${MOCKDN_FILES}
  ${IAM_FILES}
  ${BRIDGER_FILES}
  ${JOB_MANAGER_FILES}
  ${THIRD_FILES}
  ${TOS_FILES}
  ${UFS_FILES}
  ${VOLC_FILES}
  ${CLOUDFS_PROTO_FILES}
  ${DANCENN_PROTO_FILES}
  ${BTRACE_PROTO_FILES}
  ${DATABUS_PROTO_FILES}
)
set_target_properties(dancenn_common PROPERTIES LINKER_LANGUAGE CXX)

set(DANCENN_COMMON_LINK_LIBRARIES
  ${JVM_LIBRARY}
  ${JAVA_LIBRARY}
  ${CNETPP_LIBRARY}
  ${GLOG_LIBRARY}
  ${GFLAGS_LIBRARY}
  ${GTEST_LIBRARY}
  ${PROTOBUF_LIBRARY}
  ${AWS_S3_LIBRARY}
  ${AWS_CORE_LIBRARY}
  ${AWS_CORE_CRT_LIBRARY}
  ${CURL_LIBRARY}
  ${ROCKSDB_LIBRARY}
  ${OPENSSL_SSL_LIBRARY}
  ${OPENSSL_CRYPTO_LIBRARY}
  ${Z_LIBRARY}
  ${LZ4_LIBRARY}
  ${BZ2_LIBRARY}
  ${SNAPPY_LIBRARY}
  ${ZSTD_LIBRARY}
  ${UNWIND_LIBRARY}
  ${LZMA_LIBRARY}
  ${ZOOKEEPER_LIBRARY}
  ${HIREDIS_LIBRARY}
  ${PARQUET_LIBRARY}
  ${ARROW_LIBRARY}
  ${ARROW_DEPS_LIBRARY}
  ${BOOST_ATOMIC_LIBRARY}
  ${BOOST_CHRONO_LIBRARY}
  ${BOOST_DATE_TIME_LIBRARY}
  ${BOOST_FILESYSTEM_LIBRARY}
  ${BOOST_IOSTREAMS_LIBRARY}
  ${BOOST_LOCALE_LIBRARY}
  ${BOOST_LOG_LIBRARY}
  ${BOOST_REGEX_LIBRARY}
  ${BOOST_SERIALIZATION_LIBRARY}
  ${BOOST_SYSTEM_LIBRARY}
  ${BOOST_THREAD_LIBRARY}
  ${LIBFIU_LIBRARY}
  # ${ABSL_STRING_LIBRARY}
  # ${ABSL_STRINGS_INTERNAL_LIBRARY}
  # ${ABSL_STR_FORMAT_INTERNAL_LIBRARY}
  # ${ABSL_BASE_LIBRARY}
  # absl::strings
  ${ABSEIL_LIBRARY}
  ${LIBRDKAFKA_CXX_LIBRARY}
  ${LIBRDKAFKA_LIBRARY}
  ${UNWIND_LIBRARY}
  ${HDFSCLIENT_LIBRARY}
  ${ROCKETMQ_LIBRARY}
)

# Conditionally add jemalloc - exclude when using ASAN
if (NOT ENABLE_ASAN)
  list(APPEND DANCENN_COMMON_LINK_LIBRARIES ${JEMALLOC_LIBRARY})
  message("Including jemalloc library: ${JEMALLOC_LIBRARY}")
else()
  message("Excluding jemalloc library due to ASAN conflict")
endif()
if(NOT DEFINED CMAKE_DANCENN_DISABLE_UFS_HDFS)
  list(APPEND DANCENN_COMMON_LINK_LIBRARIES ${HDFSCLIENT_LIBRARY})
endif ()
target_link_libraries(dancenn_common
  -Wl,--start-group

  ${DANCENN_COMMON_LINK_LIBRARIES}

  resolv
  -Wl,--end-group
)
target_include_directories(dancenn_common PRIVATE ${GTEST_INCLUDE_DIR})

add_executable(dancenn ${DANCENN_SERVER_FILES})
set_target_properties(dancenn PROPERTIES LINKER_LANGUAGE CXX)
target_link_libraries(dancenn dancenn_common)

add_executable(image_diff ${IMAGE_DIFF_TOOL_FILES})
set_target_properties(image_diff PROPERTIES LINKER_LANGUAGE CXX)
target_link_libraries(image_diff dancenn_common)

add_executable(fsimage_transfer ${FSIMAGE_TRANSFER_TOOL_FILES})
set_target_properties(fsimage_transfer PROPERTIES LINKER_LANGUAGE CXX)
target_link_libraries(fsimage_transfer dancenn_common)

add_executable(print_editlog ${PRINT_EDITLOG_TOOL_FILES})
target_include_directories(print_editlog PRIVATE ${GTEST_INCLUDE_DIR})
set_target_properties(print_editlog PROPERTIES LINKER_LANGUAGE CXX)
target_link_libraries(print_editlog dancenn_common ${GTEST_LIBRARY})

add_executable(print_rocksdb ${PRINT_ROCKSDB_TOOL_FILES})
set_target_properties(print_rocksdb PROPERTIES LINKER_LANGUAGE CXX)
target_link_libraries(print_rocksdb dancenn_common)

add_executable(dump_rocksdb ${DUMP_ROCKSDB_TOOL_FILES})
set_target_properties(dump_rocksdb PROPERTIES LINKER_LANGUAGE CXX)
target_link_libraries(dump_rocksdb dancenn_common)

add_executable(verify_correctness ${VERIFY_CORRECTNESS_TOOL_FILES})
set_target_properties(verify_correctness PROPERTIES LINKER_LANGUAGE CXX)
target_link_libraries(verify_correctness dancenn_common)

add_executable(crop_inode_tree ${CROP_INODE_TREE_TOOL_FILES})
set_target_properties(crop_inode_tree PROPERTIES LINKER_LANGUAGE CXX)
target_link_libraries(crop_inode_tree dancenn_common)

add_executable(path_viewer ${PATH_VIEWER_TOOL_FILES})
set_target_properties(path_viewer PROPERTIES LINKER_LANGUAGE CXX)
target_link_libraries(path_viewer dancenn_common)

add_executable(print_dirstat ${PRINT_DIRSTAT_TOOL_FILS})
set_target_properties(print_dirstat PROPERTIES LINKER_LANGUAGE CXX)
target_link_libraries(print_dirstat dancenn_common)

add_executable(recover_lease ${RECOVER_LEASE_TOOL_FILES})
set_target_properties(recover_lease PROPERTIES LINKER_LANGUAGE CXX)
target_link_libraries(recover_lease dancenn_common)

add_executable(delete_tos_objects ${DELETE_TOS_OBJECTS_FILES})
set_target_properties(delete_tos_objects PROPERTIES LINKER_LANGUAGE CXX)
target_link_libraries(delete_tos_objects dancenn_common)

add_executable(block_recycler_benchmark ${BLOCK_RECYCLER_BENCHMARK_TOOL_FILES})
set_target_properties(block_recycler_benchmark PROPERTIES LINKER_LANGUAGE CXX)
target_link_libraries(block_recycler_benchmark dancenn_common)

add_executable(dancenn_haadmin ${DANCENN_HAADMIN_TOOL_FILES})
set_target_properties(dancenn_haadmin PROPERTIES LINKER_LANGUAGE CXX)
target_link_libraries(dancenn_haadmin dancenn_common)

add_executable(gs_checker ${GS_CHECKER_TOOL_FILES})
set_target_properties(gs_checker PROPERTIES LINKER_LANGUAGE CXX)
target_link_libraries(gs_checker dancenn_common)

add_executable(tos_util ${TOS_UTIL_TOOL_FILES})
set_target_properties(tos_util PROPERTIES LINKER_LANGUAGE CXX)
target_link_libraries(tos_util dancenn_common)

add_executable(tos_util_raw ${TOS_UTIL_RAW_TOOL_FILES})
set_target_properties(tos_util_raw PROPERTIES LINKER_LANGUAGE CXX)
target_link_libraries(tos_util_raw dancenn_common)

add_executable(ckpt_catcher ${CKPT_CATCHER_TOOL_FILES})
set_target_properties(ckpt_catcher PROPERTIES LINKER_LANGUAGE CXX)
target_link_libraries(ckpt_catcher dancenn_common)

add_executable(ckpt_comparator ${CKPT_COMPARATOR_TOOL_FILES})
set_target_properties(ckpt_comparator PROPERTIES LINKER_LANGUAGE CXX)
target_link_libraries(ckpt_comparator dancenn_common)

add_executable(tos_assumerole_verify ${TOS_ASSUMEROLE_VERIFY_TOOL_FILES})
set_target_properties(tos_assumerole_verify PROPERTIES LINKER_LANGUAGE CXX)
target_link_libraries(tos_assumerole_verify dancenn_common)

aux_source_directory(src/test TEST_FILES)
aux_source_directory(src/test/acc TEST_FILES)
aux_source_directory(src/test/acc/task TEST_FILES)
aux_source_directory(src/test/base TEST_FILES)
aux_source_directory(src/test/block_manager TEST_FILES)
aux_source_directory(src/test/config TEST_FILES)
aux_source_directory(src/test/datanode_manager TEST_FILES)
aux_source_directory(src/test/http TEST_FILES)
aux_source_directory(src/test/edit TEST_FILES)
aux_source_directory(src/test/fsimage TEST_FILES)
aux_source_directory(src/test/namespace TEST_FILES)
aux_source_directory(src/test/lease TEST_FILES)
aux_source_directory(src/test/ha TEST_FILES)
aux_source_directory(src/test/safemode TEST_FILES)
aux_source_directory(src/test/security TEST_FILES)
aux_source_directory(src/test/rpc TEST_FILES)
aux_source_directory(src/test/proxy TEST_FILES)
aux_source_directory(src/test/service TEST_FILES)
aux_source_directory(src/test/tos TEST_FILES)
aux_source_directory(src/test/volc TEST_FILES)
aux_source_directory(src/test/iam/model TEST_FILES)
aux_source_directory(src/test/proto/generated/cloudfs TEST_FILES)
aux_source_directory(src/test/proto/generated/dancenn TEST_FILES)
aux_source_directory(src/test/job_manager TEST_FILES)
aux_source_directory(src/test/ufs TEST_FILES)
aux_source_directory(src/test/ufs/tos_ufs TEST_FILES)
aux_source_directory(src/test/ufs/hdfs_ufs TEST_FILES)
add_executable(dancenn_unittest ${TEST_FILES})
target_compile_options(dancenn_unittest PRIVATE -fno-access-control)
target_include_directories(dancenn_unittest PRIVATE ${GTEST_INCLUDE_DIR} unittest)
target_include_directories(dancenn_unittest PRIVATE "src/test" unittest)
target_link_libraries(dancenn_unittest
  dancenn_common
  ${GTEST_LIBRARY}
  ${GMOCK_LIBRARY}
  pthread)

enable_testing()
add_test(unittest dancenn_unittest)

install(TARGETS dancenn
  RUNTIME DESTINATION bin
)
install(CODE "execute_process(COMMAND \"cmake/installations/bin/dancenn_run\" \"${JVM_LIBRARY_DIR}\" \"${JAVA_LIBRARY_DIR}\" \"${CMAKE_INSTALL_PREFIX}/bin/dancenn_run\" WORKING_DIRECTORY ${PROJECT_SOURCE_DIR})")

install(FILES cmake/installations/conf/dancenn_flags.conf DESTINATION conf)
install(FILES cmake/installations/conf/namenodes.json DESTINATION conf)
install(FILES cmake/installations/conf/datacenters_topology.conf DESTINATION conf)

install(FILES cmake/installations/conf/dancenn_flags_local.conf DESTINATION conf)
install(FILES cmake/installations/conf/dancenn_env DESTINATION conf)
install(FILES cmake/installations/conf/log4j.properties DESTINATION conf)
install(FILES cmake/installations/conf/hdfs-site.xml DESTINATION conf)
install(FILES cmake/installations/conf/core-site.xml DESTINATION conf)
install(CODE "execute_process(COMMAND \"chmod\" \"0755\" \"${CMAKE_INSTALL_PREFIX}/conf/dancenn_env\" WORKING_DIRECTORY ${PROJECT_SOURCE_DIR})")

install(DIRECTORY cmake/installations/test/conf DESTINATION test)
install(FILES cmake/installations/test/test_local DESTINATION test)
install(FILES cmake/installations/test/test_local_gdb DESTINATION test)
install(FILES cmake/installations/test/print_editlog_local DESTINATION test)
install(FILES cmake/installations/test/hdfs_local DESTINATION test)
install(FILES cmake/installations/test/dancenn_haadmin_local DESTINATION test)
install(FILES cmake/installations/test/test_single_dancenn DESTINATION test)
install(CODE "execute_process(COMMAND \"chmod\" \"0755\" \"${CMAKE_INSTALL_PREFIX}/test/test_local\" WORKING_DIRECTORY ${PROJECT_SOURCE_DIR})")
install(CODE "execute_process(COMMAND \"chmod\" \"0755\" \"${CMAKE_INSTALL_PREFIX}/test/test_local_gdb\" WORKING_DIRECTORY ${PROJECT_SOURCE_DIR})")
install(CODE "execute_process(COMMAND \"chmod\" \"0755\" \"${CMAKE_INSTALL_PREFIX}/test/hdfs_local\" WORKING_DIRECTORY ${PROJECT_SOURCE_DIR})")
install(CODE "execute_process(COMMAND \"chmod\" \"0755\" \"${CMAKE_INSTALL_PREFIX}/test/print_editlog_local\" WORKING_DIRECTORY ${PROJECT_SOURCE_DIR})")
install(CODE "execute_process(COMMAND \"chmod\" \"0755\" \"${CMAKE_INSTALL_PREFIX}/test/dancenn_haadmin_local\" WORKING_DIRECTORY ${PROJECT_SOURCE_DIR})")
install(CODE "execute_process(COMMAND \"chmod\" \"0755\" \"${CMAKE_INSTALL_PREFIX}/test/test_single_dancenn\" WORKING_DIRECTORY ${PROJECT_SOURCE_DIR})")

install(FILES cmake/installations/bin/dancenn_run_local DESTINATION bin)
install(CODE "execute_process(COMMAND \"chmod\" \"0755\" \"${CMAKE_INSTALL_PREFIX}/bin/dancenn_run_local\" WORKING_DIRECTORY ${PROJECT_SOURCE_DIR})")

install(DIRECTORY cmake/installations/jar DESTINATION .)

if(NOT DEFINED CMAKE_DANCENN_DISABLE_UFS_HDFS)
  install(FILES ${HDFSCLIENT_LIBRARY} DESTINATION lib)
endif ()

install(TARGETS fsimage_transfer
  RUNTIME DESTINATION bin
)

install(TARGETS dancenn_unittest
  RUNTIME DESTINATION test
)

install(TARGETS print_editlog
  RUNTIME DESTINATION test
)

install(TARGETS dancenn_haadmin
  RUNTIME DESTINATION test
)

install(DIRECTORY cmake/installations/test/data DESTINATION test)

install(CODE "execute_process(COMMAND \"cmake/installations/test/test\" \"${JVM_LIBRARY_DIR}\" \"${JAVA_LIBRARY_DIR}\" \"${CMAKE_INSTALL_PREFIX}/test/test\" WORKING_DIRECTORY ${PROJECT_SOURCE_DIR})")
