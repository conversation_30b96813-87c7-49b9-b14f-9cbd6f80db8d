cmake_minimum_required(VERSION 3.30)
project(nn_coding3)

set(CMAKE_CXX_STANDARD 14)

include_directories(.)
include_directories(dancenn)
include_directories(dancenn/src)
include_directories(dancenn/src/acc)
include_directories(dancenn/src/acc/task)
include_directories(dancenn/src/base)
include_directories(dancenn/src/base/trace)
include_directories(dancenn/src/block_manager)
include_directories(dancenn/src/bridger)
include_directories(dancenn/src/datanode_manager)
include_directories(dancenn/src/edit)
include_directories(dancenn/src/fsimage)
include_directories(dancenn/src/ha)
include_directories(dancenn/src/http)
include_directories(dancenn/src/iam)
include_directories(dancenn/src/iam/model)
include_directories(dancenn/src/job_manager)
include_directories(dancenn/src/lease)
include_directories(dancenn/src/mockdn)
include_directories(dancenn/src/namespace)
include_directories(dancenn/src/op_task)
include_directories(dancenn/src/proxy)
include_directories(dancenn/src/rpc)
include_directories(dancenn/src/safemode)
include_directories(dancenn/src/security)
include_directories(dancenn/src/server)
include_directories(dancenn/src/service)
include_directories(dancenn/src/snapshot)
include_directories(dancenn/src/test)
include_directories(dancenn/src/test/base)
include_directories(dancenn/src/test/block_manager)
include_directories(dancenn/src/test/config)
include_directories(dancenn/src/test/datanode_manager)
include_directories(dancenn/src/test/edit)
include_directories(dancenn/src/test/job_manager)
include_directories(dancenn/src/test/lease)
include_directories(dancenn/src/test/namespace)
include_directories(dancenn/src/test/proto/generated)
include_directories(dancenn/src/test/proto/generated/cloudfs)
include_directories(dancenn/src/test/proto/generated/dancenn)
include_directories(dancenn/src/test/rpc)
include_directories(dancenn/src/test/safemode)
include_directories(dancenn/src/test/tos)
include_directories(dancenn/src/test/ufs)
include_directories(dancenn/src/test/ufs/tos_ufs)
include_directories(dancenn/src/third)
include_directories(dancenn/src/ufs)
include_directories(dancenn/src/ufs/hdfs)
include_directories(dancenn/src/ufs/hdfs_ufs)
include_directories(dancenn/src/ufs/tos)
include_directories(dancenn/src/ufs/tos_ufs)
include_directories(dancenn/src/ufs/upload)
include_directories(dancenn/src/volc)

add_executable(nn_coding3
    dancenn/src/acc/task/create_file_task.cc
    dancenn/src/acc/task/delete_task.cc
    dancenn/src/acc/task/file_status_sync_task.cc
    dancenn/src/acc/task/listing_sync_task.cc
    dancenn/src/acc/task/mkdir_task.cc
    dancenn/src/acc/task/rename_task.cc
    dancenn/src/acc/task/sync_task.h
    dancenn/src/acc/task/tos_event_sync_task.cc
    dancenn/src/acc/task/tos_event_sync_task.h
    dancenn/src/acc/acc.h
    dancenn/src/acc/acc_namespace.cc
    dancenn/src/acc/acc_namespace.h
    dancenn/src/acc/acc_namespace_def.h
    dancenn/src/acc/inode_sync_checker.cc
    dancenn/src/acc/inode_sync_checker.h
    dancenn/src/acc/meta_sync_context.h
    dancenn/src/acc/mkdir_create_in_ufs_scanner.cc
    dancenn/src/acc/mkdir_create_in_ufs_scanner.h
    dancenn/src/acc/sync_engine.cc
    dancenn/src/acc/sync_engine.h
    dancenn/src/base/actor.h
    dancenn/src/base/audit_logger.cc
    dancenn/src/base/audit_logger.h
    dancenn/src/base/base64.cc
    dancenn/src/base/base64.h
    dancenn/src/base/block_event_logger.cc
    dancenn/src/base/block_event_logger.h
    dancenn/src/base/closure.h
    dancenn/src/base/committer_channel_context.cc
    dancenn/src/base/committer_channel_context.h
    dancenn/src/base/constants.cc
    dancenn/src/base/constants.h
    dancenn/src/base/count_down_latch.cc
    dancenn/src/base/count_down_latch.h
    dancenn/src/base/cpu_local.h
    dancenn/src/base/data_center_table.cc
    dancenn/src/base/data_center_table.h
    dancenn/src/base/databus.cc
    dancenn/src/base/databus.h
    dancenn/src/base/databus_sender.cc
    dancenn/src/base/databus_sender.h
    dancenn/src/base/debugging.h
    dancenn/src/base/defer.h
    dancenn/src/base/dictionary.h
    dancenn/src/base/edit_logger.cc
    dancenn/src/base/edit_logger.h
    dancenn/src/base/escape.cc
    dancenn/src/base/escape.h
    dancenn/src/base/file_utils.h
    dancenn/src/base/hash.cc
    dancenn/src/base/hash.h
    dancenn/src/base/http_metric_emitter_plugin.cc
    dancenn/src/base/http_metric_emitter_plugin.h
    dancenn/src/base/hyper_file_utils.h
    dancenn/src/base/java.cc
    dancenn/src/base/java.h
    dancenn/src/base/java_exceptions.cc
    dancenn/src/base/java_exceptions.h
    dancenn/src/base/kafka_util.cc
    dancenn/src/base/kafka_util.h
    dancenn/src/base/logger_metrics.cc
    dancenn/src/base/logger_metrics.h
    dancenn/src/base/lru_cache.h
    dancenn/src/base/md5_calculator.cc
    dancenn/src/base/md5_calculator.h
    dancenn/src/base/metric.h
    dancenn/src/base/metric_emitter_plugin.cc
    dancenn/src/base/metric_emitter_plugin.h
    dancenn/src/base/metrics.cc
    dancenn/src/base/metrics.h
    dancenn/src/base/mime_type.cc
    dancenn/src/base/mime_type.h
    dancenn/src/base/murmur_hash.cc
    dancenn/src/base/murmur_hash.h
    dancenn/src/base/net_utils.cc
    dancenn/src/base/net_utils.h
    dancenn/src/base/network_location_info.cc
    dancenn/src/base/network_location_info.h
    dancenn/src/base/object_pool.h
    dancenn/src/base/parquet_writer.cc
    dancenn/src/base/parquet_writer.h
    dancenn/src/base/path_util.cc
    dancenn/src/base/path_util.h
    dancenn/src/base/pb_converter.cc
    dancenn/src/base/pb_converter.h
    dancenn/src/base/platform.h
    dancenn/src/base/policy_cache.h
    dancenn/src/base/properties.cc
    dancenn/src/base/properties.h
    dancenn/src/base/rack_aware.cc
    dancenn/src/base/rack_aware.h
    dancenn/src/base/rand_number.cc
    dancenn/src/base/rand_number.h
    dancenn/src/base/read_write_lock.h
    dancenn/src/base/redis_manager.cc
    dancenn/src/base/redis_manager.h
    dancenn/src/base/refresher.h
    dancenn/src/base/retry_cache.cc
    dancenn/src/base/retry_cache.h
    dancenn/src/base/ring_buffer.h
    dancenn/src/base/rpc_server_metrics.h
    dancenn/src/base/runtime_monitor.cc
    dancenn/src/base/runtime_monitor.h
    dancenn/src/base/rw_spinlock.cc
    dancenn/src/base/rw_spinlock.h
    dancenn/src/base/rwlock_manager.cc
    dancenn/src/base/rwlock_manager.h
    dancenn/src/base/rwlock_table.cc
    dancenn/src/base/rwlock_table.h
    dancenn/src/base/script_metric_emitter_plugin.cc
    dancenn/src/base/script_metric_emitter_plugin.h
    dancenn/src/base/state_transition_lock.h
    dancenn/src/base/status.cc
    dancenn/src/base/status.h
    dancenn/src/base/stop_watch.h
    dancenn/src/base/string_utils.cc
    dancenn/src/base/string_utils.h
    dancenn/src/base/threading.h
    dancenn/src/base/throttler.cc
    dancenn/src/base/throttler.h
    dancenn/src/base/time_util.cc
    dancenn/src/base/time_util.h
    dancenn/src/base/timer_task_manager.cc
    dancenn/src/base/timer_task_manager.h
    dancenn/src/base/to_json_string.h
    dancenn/src/base/token_bucket.cc
    dancenn/src/base/token_bucket.h
    dancenn/src/base/trusted_ip_table.cc
    dancenn/src/base/trusted_ip_table.h
    dancenn/src/base/two_step_vlock.cc
    dancenn/src/base/two_step_vlock.h
    dancenn/src/base/uuid.cc
    dancenn/src/base/uuid.h
    dancenn/src/base/vlock.cc
    dancenn/src/base/vlock.h
    dancenn/src/base/writable_util.h
    dancenn/src/base/zlib_compress.cc
    dancenn/src/base/zlib_compress.h
    dancenn/src/block_manager/bip_write_manager.cc
    dancenn/src/block_manager/bip_write_manager.h
    dancenn/src/block_manager/block.h
    dancenn/src/block_manager/block_info.cc
    dancenn/src/block_manager/block_info.h
    dancenn/src/block_manager/block_info_proto.h
    dancenn/src/block_manager/block_lifecycle_logger.cc
    dancenn/src/block_manager/block_lifecycle_logger.h
    dancenn/src/block_manager/block_lifecycle_map_slice.cc
    dancenn/src/block_manager/block_lifecycle_map_slice.h
    dancenn/src/block_manager/block_manager.cc
    dancenn/src/block_manager/block_manager.h
    dancenn/src/block_manager/block_manager_metrics.cc
    dancenn/src/block_manager/block_manager_metrics.h
    dancenn/src/block_manager/block_map_slice.cc
    dancenn/src/block_manager/block_map_slice.h
    dancenn/src/block_manager/block_pufs_info.cc
    dancenn/src/block_manager/block_pufs_info.h
    dancenn/src/block_manager/block_pufs_info_monitor.cc
    dancenn/src/block_manager/block_pufs_info_monitor.h
    dancenn/src/block_manager/block_pufs_name_gen.cc
    dancenn/src/block_manager/block_pufs_name_gen.h
    dancenn/src/block_manager/block_recycler.cc
    dancenn/src/block_manager/block_recycler.h
    dancenn/src/block_manager/block_report_handler.cc
    dancenn/src/block_manager/block_report_handler.h
    dancenn/src/block_manager/block_report_handler_metrics.cc
    dancenn/src/block_manager/block_report_handler_metrics.h
    dancenn/src/block_manager/block_uploader.cc
    dancenn/src/block_manager/blocks_scanner_listener.cc
    dancenn/src/block_manager/blocks_scanner_listener.h
    dancenn/src/block_manager/datanode_command.h
    dancenn/src/block_manager/datanode_command_manager.cc
    dancenn/src/block_manager/datanode_command_manager.h
    dancenn/src/block_manager/invalidate_block_monitor.cc
    dancenn/src/block_manager/invalidate_block_monitor.h
    dancenn/src/block_manager/misinvalidated_block_monitor.cc
    dancenn/src/block_manager/misinvalidated_block_monitor.h
    dancenn/src/block_manager/pending_replication_blocks.cc
    dancenn/src/block_manager/pending_replication_blocks.h
    dancenn/src/block_manager/replication_monitor.cc
    dancenn/src/block_manager/replication_monitor.h
    dancenn/src/block_manager/status_monitor.cc
    dancenn/src/block_manager/status_monitor.h
    dancenn/src/block_manager/truncatable_block_monitor.cc
    dancenn/src/block_manager/truncatable_block_monitor.h
    dancenn/src/block_manager/under_construction_state.cc
    dancenn/src/block_manager/under_construction_state.h
    dancenn/src/block_manager/under_replicated_blocks.cc
    dancenn/src/block_manager/under_replicated_blocks.h
    dancenn/src/bridger/socket_client.cc
    dancenn/src/bridger/socket_client.h
    dancenn/src/bridger/socket_wrapper.h
    dancenn/src/datanode_manager/advice.cc
    dancenn/src/datanode_manager/advice.h
    dancenn/src/datanode_manager/block_index.h
    dancenn/src/datanode_manager/block_index_impl_stl.cc
    dancenn/src/datanode_manager/block_placement.cc
    dancenn/src/datanode_manager/block_placement.h
    dancenn/src/datanode_manager/block_placement_cfs_default.cc
    dancenn/src/datanode_manager/block_placement_cfs_default.h
    dancenn/src/datanode_manager/block_placement_cfs_multidc.cc
    dancenn/src/datanode_manager/block_placement_cfs_multidc.h
    dancenn/src/datanode_manager/block_placement_consts.cc
    dancenn/src/datanode_manager/block_placement_default.cc
    dancenn/src/datanode_manager/block_placement_multidc.cc
    dancenn/src/datanode_manager/block_placement_nodezone.cc
    dancenn/src/datanode_manager/block_placement_nodezone.h
    dancenn/src/datanode_manager/block_placement_nodezone_metrics.cc
    dancenn/src/datanode_manager/block_placement_nodezone_metrics.h
    dancenn/src/datanode_manager/data_centers.cc
    dancenn/src/datanode_manager/data_centers.h
    dancenn/src/datanode_manager/data_centers_topology.cc
    dancenn/src/datanode_manager/data_centers_topology.h
    dancenn/src/datanode_manager/datanode_info.cc
    dancenn/src/datanode_manager/datanode_info.h
    dancenn/src/datanode_manager/datanode_manager.cc
    dancenn/src/datanode_manager/datanode_manager.h
    dancenn/src/datanode_manager/datanode_manager_metrics.cc
    dancenn/src/datanode_manager/datanode_manager_metrics.h
    dancenn/src/datanode_manager/ordered_block_index_impl.cc
    dancenn/src/datanode_manager/storage_info.cc
    dancenn/src/datanode_manager/storage_info.h
    dancenn/src/datanode_manager/storage_policy.cc
    dancenn/src/datanode_manager/storage_policy.h
    dancenn/src/datanode_manager/unordered_block_index_impl.cc
    dancenn/src/edit/committer.cc
    dancenn/src/edit/committer.h
    dancenn/src/edit/deserializer.h
    dancenn/src/edit/edit_log_cfs_op.cc
    dancenn/src/edit/edit_log_cfs_op.h
    dancenn/src/edit/edit_log_context.h
    dancenn/src/edit/edit_log_context_base.h
    dancenn/src/edit/edit_log_op.h
    dancenn/src/edit/edit_log_sync_listener.h
    dancenn/src/edit/edit_log_task.cc
    dancenn/src/edit/edit_log_task.h
    dancenn/src/edit/edit_types.h
    dancenn/src/edit/ha_edit_log_context.cc
    dancenn/src/edit/ha_edit_log_context.h
    dancenn/src/edit/ha_flexible_edit_log_context.cc
    dancenn/src/edit/ha_flexible_edit_log_context.h
    dancenn/src/edit/log_rpc_info.h
    dancenn/src/edit/non_ha_edit_log_context.cc
    dancenn/src/edit/non_ha_edit_log_context.h
    dancenn/src/edit/roller.cc
    dancenn/src/edit/roller.h
    dancenn/src/edit/sender.cc
    dancenn/src/edit/sender.h
    dancenn/src/edit/sender_base.h
    dancenn/src/edit/sender_v2.cc
    dancenn/src/edit/serializer.h
    dancenn/src/edit/syncer.cc
    dancenn/src/edit/syncer.h
    dancenn/src/edit/tailer.cc
    dancenn/src/edit/tailer.h
    dancenn/src/edit/tailer_base.h
    dancenn/src/fsimage/fsimage_loader.cc
    dancenn/src/fsimage/fsimage_loader.h
    dancenn/src/ft/hdfs_mode_without_dn.cc
    dancenn/src/ha/ha_mode_transition_manager.cc
    dancenn/src/ha/ha_mode_transition_manager.h
    dancenn/src/ha/ha_state.cc
    dancenn/src/ha/ha_state.h
    dancenn/src/ha/ha_state_base.h
    dancenn/src/ha/operations.h
    dancenn/src/http/configurations_handler.cc
    dancenn/src/http/configurations_handler.h
    dancenn/src/http/dancenn_admin_handler.cc
    dancenn/src/http/dancenn_admin_handler.h
    dancenn/src/http/dancenn_fs_handler.cc
    dancenn/src/http/dancenn_fs_handler.h
    dancenn/src/http/dancenn_fsck_handler.cc
    dancenn/src/http/dancenn_fsck_handler.h
    dancenn/src/http/dancenn_http_server.cc
    dancenn/src/http/dancenn_http_server.h
    dancenn/src/http/dancenn_job_handler.cc
    dancenn/src/http/dancenn_job_handler.h
    dancenn/src/http/dancenn_status_handler.cc
    dancenn/src/http/dancenn_status_handler.h
    dancenn/src/http/danceproxy_http_server.cc
    dancenn/src/http/danceproxy_http_server.h
    dancenn/src/http/danceproxy_status_handler.cc
    dancenn/src/http/danceproxy_status_handler.h
    dancenn/src/http/decommission_handler.cc
    dancenn/src/http/decommission_handler.h
    dancenn/src/http/dirstat_handler.cc
    dancenn/src/http/dirstat_handler.h
    dancenn/src/http/http_handler.cc
    dancenn/src/http/http_handler.h
    dancenn/src/http/http_server.cc
    dancenn/src/http/http_server.h
    dancenn/src/http/lifecycle_handler.cc
    dancenn/src/http/lifecycle_handler.h
    dancenn/src/http/metrics_handler.cc
    dancenn/src/http/metrics_handler.h
    dancenn/src/http/open_api_handler.cc
    dancenn/src/http/open_api_handler.h
    dancenn/src/http/prometheus_handler.cc
    dancenn/src/http/prometheus_handler.h
    dancenn/src/http/quota_handler.cc
    dancenn/src/http/quota_handler.h
    dancenn/src/http/refresh_handler.cc
    dancenn/src/http/refresh_handler.h
    dancenn/src/http/stale_handler.cc
    dancenn/src/http/stale_handler.h
    dancenn/src/http/ufs_event_http_handler.cc
    dancenn/src/http/ufs_event_http_handler.h
    dancenn/src/http/view_handler.cc
    dancenn/src/http/view_handler.h
    dancenn/src/iam/model/assumerole_request.h
    dancenn/src/iam/model/assumerole_response.cc
    dancenn/src/iam/model/assumerole_response.h
    dancenn/src/iam/model/policy_document.cc
    dancenn/src/iam/model/policy_document.h
    dancenn/src/iam/algorithms.cc
    dancenn/src/iam/algorithms.h
    dancenn/src/iam/assume_http_wrapper.h
    dancenn/src/iam/assume_role_client.cc
    dancenn/src/iam/assume_role_client.h
    dancenn/src/iam/assume_role_metrics.cc
    dancenn/src/iam/assume_role_metrics.h
    dancenn/src/job_manager/block_task_iterator.cc
    dancenn/src/job_manager/chain_job_task_iterator.cc
    dancenn/src/job_manager/custom_task_iterator.cc
    dancenn/src/job_manager/job_manager.cc
    dancenn/src/job_manager/job_manager.h
    dancenn/src/job_manager/job_manager_def.h
    dancenn/src/job_manager/job_manager_metrics.cc
    dancenn/src/job_manager/job_manager_metrics.h
    dancenn/src/job_manager/job_tracker.cc
    dancenn/src/job_manager/job_tracker.h
    dancenn/src/job_manager/managed_job.cc
    dancenn/src/job_manager/managed_job.h
    dancenn/src/job_manager/managed_job_handler.cc
    dancenn/src/job_manager/managed_job_handler.h
    dancenn/src/job_manager/managed_job_impl.h
    dancenn/src/job_manager/managed_job_state.cc
    dancenn/src/job_manager/managed_job_state.h
    dancenn/src/job_manager/managed_task.cc
    dancenn/src/job_manager/managed_task.h
    dancenn/src/job_manager/managed_task_handler.cc
    dancenn/src/job_manager/managed_task_handler.h
    dancenn/src/job_manager/managed_task_impl.h
    dancenn/src/job_manager/reconcile_inode_attr_task_iterator.cc
    dancenn/src/job_manager/task_creator.cc
    dancenn/src/job_manager/task_creator.h
    dancenn/src/job_manager/workflow.cc
    dancenn/src/job_manager/workflow.h
    dancenn/src/job_manager/workflow_id_generator.h
    dancenn/src/lease/lease.h
    dancenn/src/lease/lease_manager.cc
    dancenn/src/lease/lease_manager.h
    dancenn/src/lease/lease_manager_base.h
    dancenn/src/lease/lease_manager_v2.cc
    dancenn/src/lease/lease_manager_v2.h
    dancenn/src/lease/lease_metrics.cc
    dancenn/src/lease/lease_metrics.h
    dancenn/src/lease/lease_monitor.cc
    dancenn/src/lease/lease_monitor.h
    dancenn/src/lease/lease_monitor_base.h
    dancenn/src/lease/lease_monitor_v2.cc
    dancenn/src/lease/lease_monitor_v2.h
    dancenn/src/mockdn/dispacher.h
    dancenn/src/mockdn/mock_dn.h
    dancenn/src/namespace/access_counter.cc
    dancenn/src/namespace/access_counter.h
    dancenn/src/namespace/access_counter_manager.cc
    dancenn/src/namespace/access_counter_manager.h
    dancenn/src/namespace/acl.h
    dancenn/src/namespace/az_monitor.cc
    dancenn/src/namespace/az_monitor.h
    dancenn/src/namespace/block_loader.cc
    dancenn/src/namespace/block_loader.h
    dancenn/src/namespace/checkpointer.cc
    dancenn/src/namespace/checkpointer.h
    dancenn/src/namespace/content_counter.h
    dancenn/src/namespace/create_flag.h
    dancenn/src/namespace/editlog_apply.cc
    dancenn/src/namespace/file_finalizer.cc
    dancenn/src/namespace/file_finalizer.h
    dancenn/src/namespace/file_usage.h
    dancenn/src/namespace/hyperfile_scanner_listener.cc
    dancenn/src/namespace/hyperfile_scanner_listener.h
    dancenn/src/namespace/inode.cc
    dancenn/src/namespace/inode.h
    dancenn/src/namespace/inode_attr_ttl_manager.cc
    dancenn/src/namespace/inode_attr_ttl_manager.h
    dancenn/src/namespace/inode_attr_ttl_manager_metrics.cc
    dancenn/src/namespace/inode_attr_ttl_manager_metrics.h
    dancenn/src/namespace/inode_with_blocks.h
    dancenn/src/namespace/lease_scanner_listener.cc
    dancenn/src/namespace/lease_scanner_listener.h
    dancenn/src/namespace/lifecycle_policy_util.cc
    dancenn/src/namespace/lifecycle_policy_util.h
    dancenn/src/namespace/lifecycle_scanner.cc
    dancenn/src/namespace/lifecycle_scanner.h
    dancenn/src/namespace/locked_path.cc
    dancenn/src/namespace/locked_path.h
    dancenn/src/namespace/merge_block_listener.cc
    dancenn/src/namespace/merge_block_listener.h
    dancenn/src/namespace/meta_scanner.cc
    dancenn/src/namespace/meta_scanner.h
    dancenn/src/namespace/meta_scanner_v2.cc
    dancenn/src/namespace/meta_scanner_v2.h
    dancenn/src/namespace/meta_storage.cc
    dancenn/src/namespace/meta_storage.h
    dancenn/src/namespace/meta_storage_constants.cc
    dancenn/src/namespace/meta_storage_constants.h
    dancenn/src/namespace/meta_storage_metrics.cc
    dancenn/src/namespace/meta_storage_metrics.h
    dancenn/src/namespace/meta_storage_util.cc
    dancenn/src/namespace/meta_storage_util.h
    dancenn/src/namespace/meta_storage_write_task.h
    dancenn/src/namespace/meta_storage_writer.cc
    dancenn/src/namespace/meta_storage_writer.h
    dancenn/src/namespace/namespace.cc
    dancenn/src/namespace/namespace.h
    dancenn/src/namespace/namespace_acc.cc
    dancenn/src/namespace/namespace_http.cc
    dancenn/src/namespace/namespace_http.h
    dancenn/src/namespace/namespace_metrics.cc
    dancenn/src/namespace/namespace_metrics.h
    dancenn/src/namespace/namespace_scrub.cc
    dancenn/src/namespace/namespace_scrub.h
    dancenn/src/namespace/namespace_scrub_inodestat.cc
    dancenn/src/namespace/namespace_scrub_inodestat.h
    dancenn/src/namespace/namespace_scrub_lifecycle_scan.cc
    dancenn/src/namespace/namespace_scrub_lifecycle_scan.h
    dancenn/src/namespace/namespace_scrub_pin.cc
    dancenn/src/namespace/namespace_scrub_pin.h
    dancenn/src/namespace/namespace_stat.cc
    dancenn/src/namespace/namespace_stat.h
    dancenn/src/namespace/namespace_stat_checker.cc
    dancenn/src/namespace/namespace_stat_checker.h
    dancenn/src/namespace/namespace_stat_delta_cache.cc
    dancenn/src/namespace/namespace_stat_delta_cache.h
    dancenn/src/namespace/namespace_usage_reporter.cc
    dancenn/src/namespace/namespace_usage_reporter.h
    dancenn/src/namespace/permission.h
    dancenn/src/namespace/permission_checker.cc
    dancenn/src/namespace/permission_checker.h
    dancenn/src/namespace/policy_manager.cc
    dancenn/src/namespace/policy_manager.h
    dancenn/src/namespace/quota_collector.cc
    dancenn/src/namespace/quota_collector.h
    dancenn/src/namespace/quota_manager.cc
    dancenn/src/namespace/quota_manager.h
    dancenn/src/namespace/recycle_scanner.cc
    dancenn/src/namespace/recycle_scanner.h
    dancenn/src/namespace/replica_policy_cache.cc
    dancenn/src/namespace/replica_policy_cache.h
    dancenn/src/namespace/rocksdb_listener.cc
    dancenn/src/namespace/rocksdb_listener.h
    dancenn/src/namespace/slows.cc
    dancenn/src/namespace/slows.h
    dancenn/src/namespace/snapshot.cc
    dancenn/src/namespace/ttl_atime_collector.cc
    dancenn/src/namespace/ttl_atime_collector.h
    dancenn/src/namespace/unique_txid_ring_window.cc
    dancenn/src/namespace/unique_txid_ring_window.h
    dancenn/src/namespace/user_group_info.cc
    dancenn/src/namespace/user_group_info.h
    dancenn/src/namespace/write_batch_merge_handler.cc
    dancenn/src/namespace/write_batch_merge_handler.h
    dancenn/src/namespace/xattr.h
    dancenn/src/op_task/dn_replacement_op_task.cc
    dancenn/src/op_task/dn_replacement_op_task.h
    dancenn/src/op_task/op_task.cc
    dancenn/src/op_task/op_task.h
    dancenn/src/op_task/op_task_manager.cc
    dancenn/src/op_task/op_task_manager.h
    dancenn/src/proxy/abstract_condition_checker.cc
    dancenn/src/proxy/abstract_condition_checker.h
    dancenn/src/proxy/block_pool_registry.cc
    dancenn/src/proxy/block_pool_registry.h
    dancenn/src/proxy/fanout_closure.cc
    dancenn/src/proxy/fanout_closure.h
    dancenn/src/proxy/frozen_directory_manager.cc
    dancenn/src/proxy/frozen_directory_manager.h
    dancenn/src/proxy/mount_entry.cc
    dancenn/src/proxy/mount_entry.h
    dancenn/src/proxy/mounts_manager.cc
    dancenn/src/proxy/mounts_manager.h
    dancenn/src/proxy/path_team_space_quota_manager.cc
    dancenn/src/proxy/path_team_space_quota_manager.h
    dancenn/src/proxy/proxy_throttler.cc
    dancenn/src/proxy/proxy_throttler.h
    dancenn/src/proxy/quota_or_usage.h
    dancenn/src/proxy/storage_policy_ttl_manager.cc
    dancenn/src/proxy/storage_policy_ttl_manager.h
    dancenn/src/proxy/upstream_manager.cc
    dancenn/src/proxy/upstream_manager.h
    dancenn/src/rpc/call_task.cc
    dancenn/src/rpc/call_task.h
    dancenn/src/rpc/client_call.cc
    dancenn/src/rpc/client_call.h
    dancenn/src/rpc/done_closure.cc
    dancenn/src/rpc/done_closure.h
    dancenn/src/rpc/failovered_rpc_channel.cc
    dancenn/src/rpc/failovered_rpc_channel.h
    dancenn/src/rpc/ha_rpc_channel.cc
    dancenn/src/rpc/ha_rpc_channel.h
    dancenn/src/rpc/pooled_rpc_channel.cc
    dancenn/src/rpc/pooled_rpc_channel.h
    dancenn/src/rpc/rpc_channel_impl.cc
    dancenn/src/rpc/rpc_channel_impl.h
    dancenn/src/rpc/rpc_client_connection.cc
    dancenn/src/rpc/rpc_client_connection.h
    dancenn/src/rpc/rpc_client_options.h
    dancenn/src/rpc/rpc_connection_base.cc
    dancenn/src/rpc/rpc_connection_base.h
    dancenn/src/rpc/rpc_constants.h
    dancenn/src/rpc/rpc_controller.cc
    dancenn/src/rpc/rpc_controller.h
    dancenn/src/rpc/rpc_server.cc
    dancenn/src/rpc/rpc_server.h
    dancenn/src/rpc/rpc_server_connection.cc
    dancenn/src/rpc/rpc_server_connection.h
    dancenn/src/rpc/rpc_server_options.h
    dancenn/src/rpc/stateless_rpc_channel.cc
    dancenn/src/rpc/stateless_rpc_channel.h
    dancenn/src/safemode/safemode.cc
    dancenn/src/safemode/safemode.h
    dancenn/src/safemode/safemode_base.h
    dancenn/src/security/block_token.cc
    dancenn/src/security/block_token.h
    dancenn/src/security/block_token_identifier.cc
    dancenn/src/security/block_token_identifier.h
    dancenn/src/security/block_token_secret_manager.cc
    dancenn/src/security/block_token_secret_manager.h
    dancenn/src/security/key_manager.cc
    dancenn/src/security/key_manager.h
    dancenn/src/security/key_manager_local.h
    dancenn/src/security/writable_utils.cc
    dancenn/src/security/writable_utils.h
    dancenn/src/server/dancenn.cc
    dancenn/src/server/dancenn.h
    dancenn/src/server/danceproxy.cc
    dancenn/src/server/placement_driver.cc
    dancenn/src/server/server_common.hpp
    dancenn/src/service/acc_client_namenode_service.cc
    dancenn/src/service/acc_client_namenode_service.h
    dancenn/src/service/client_namenode_service.cc
    dancenn/src/service/client_namenode_service.h
    dancenn/src/service/danceproxy_service.cc
    dancenn/src/service/danceproxy_service.h
    dancenn/src/service/datanode_service.cc
    dancenn/src/service/datanode_service.h
    dancenn/src/service/ha_service.cc
    dancenn/src/service/ha_service.h
    dancenn/src/service/method_metrics.h
    dancenn/src/service/method_recorder.h
    dancenn/src/service/method_tracer.h
    dancenn/src/service/method_tracer_closure.h
    dancenn/src/service/namenode_service.cc
    dancenn/src/service/namenode_service.h
    dancenn/src/service/placement_driver_service.cc
    dancenn/src/service/placement_driver_service.h
    dancenn/src/service/service_meta.h
    dancenn/src/service/service_util.cc
    dancenn/src/service/service_util.h
    dancenn/src/snapshot/snapshot_manager.h
    dancenn/src/snapshot/snapshot_manager_impl.cc
    dancenn/src/snapshot/snapshot_manager_impl.h
    dancenn/src/test/acc/task/sync_ctrl_test.cc
    dancenn/src/test/acc/task/tos_event_sync_task_test.cc
    dancenn/src/test/base/file_utils_test.cc
    dancenn/src/test/base/gmock_rwlock_manager.h
    dancenn/src/test/base/gmock_time_util.h
    dancenn/src/test/base/java_test.cc
    dancenn/src/test/base/json_test.cc
    dancenn/src/test/base/kafka_util_test.cc
    dancenn/src/test/base/lru_cache_test.cc
    dancenn/src/test/base/md5_calculator_test.cc
    dancenn/src/test/base/metric_test.cc
    dancenn/src/test/base/mime_type_test.cc
    dancenn/src/test/base/mock_databus_server_test.cc
    dancenn/src/test/base/mock_databus_server_test.h
    dancenn/src/test/base/net_utils_test.cc
    dancenn/src/test/base/object_pool_test.cc
    dancenn/src/test/base/path_util_test.cpp
    dancenn/src/test/base/pb_converter_test.cc
    dancenn/src/test/base/platform_test.cc
    dancenn/src/test/base/policy_cache_test.cc
    dancenn/src/test/base/properties_test.cc
    dancenn/src/test/base/rack_aware_test.cc
    dancenn/src/test/base/rand_number_test.cc
    dancenn/src/test/base/retry_cache_test.cc
    dancenn/src/test/base/ring_buffer_test.cc
    dancenn/src/test/base/runtime_monitor_test.cc
    dancenn/src/test/base/rw_spinlock_test.cc
    dancenn/src/test/base/rwlock_manager_test.cc
    dancenn/src/test/base/rwlock_table_test.cc
    dancenn/src/test/base/state_transition_lock_test.cc
    dancenn/src/test/base/status_test.cc
    dancenn/src/test/base/stop_watch_test.cc
    dancenn/src/test/base/string_utils_test.cc
    dancenn/src/test/base/throttler_test.cc
    dancenn/src/test/base/time_util.h
    dancenn/src/test/base/time_util_test.cc
    dancenn/src/test/base/timer_task_manager_test.cc
    dancenn/src/test/base/token_bucket_test.cc
    dancenn/src/test/base/two_step_vlock_test.cc
    dancenn/src/test/base/uuid_test.cc
    dancenn/src/test/base/vlock_test.cc
    dancenn/src/test/base/writable_util_test.cc
    dancenn/src/test/block_manager/block_info_test.cc
    dancenn/src/test/block_manager/block_manager_test.cc
    dancenn/src/test/block_manager/block_manager_test_v2.cc
    dancenn/src/test/block_manager/block_map_slice_test.cc
    dancenn/src/test/block_manager/block_pufs_info_test.cc
    dancenn/src/test/block_manager/block_pufs_name_gen_test.cc
    dancenn/src/test/block_manager/block_recycler_test.cc
    dancenn/src/test/block_manager/block_report_handler_test.cc
    dancenn/src/test/block_manager/dirty_block_info_proto_manager_test.cc
    dancenn/src/test/block_manager/gmock_bip_write_manager.h
    dancenn/src/test/block_manager/gmock_block_manager.h
    dancenn/src/test/block_manager/pending_replication_blocks_test.cc
    dancenn/src/test/block_manager/tos_read_block_crc_test.cc
    dancenn/src/test/block_manager/uc_state_test.cc
    dancenn/src/test/block_manager/under_replicated_blocks_test.cc
    dancenn/src/test/block_manager/upload_process_test.cc
    dancenn/src/test/config/ut_config.cc
    dancenn/src/test/config/ut_config.h
    dancenn/src/test/datanode_manager/block_index_test.cc
    dancenn/src/test/datanode_manager/block_placement_cfs_default_test.cc
    dancenn/src/test/datanode_manager/block_placement_default_test.cc
    dancenn/src/test/datanode_manager/block_placement_multidc_test.cc
    dancenn/src/test/datanode_manager/block_placement_nodezone_test.cc
    dancenn/src/test/datanode_manager/data_centers_test.cc
    dancenn/src/test/datanode_manager/data_centers_topology_test.cc
    dancenn/src/test/datanode_manager/datanode_info_test.cc
    dancenn/src/test/datanode_manager/datanode_manager_perf_test.cc
    dancenn/src/test/datanode_manager/datanode_manager_test.cc
    dancenn/src/test/datanode_manager/gmock_datanode_manager.h
    dancenn/src/test/edit/apply_cfs_op_test.cc
    dancenn/src/test/edit/edit_log_cfs_op_test.cc
    dancenn/src/test/edit/edit_log_recovery_test.cc
    dancenn/src/test/edit/gmock_edit_log_context.h
    dancenn/src/test/edit/gmock_ha_edit_log_context.cc
    dancenn/src/test/edit/gmock_ha_edit_log_context.h
    dancenn/src/test/edit/ha_edit_log_context_test.cc
    dancenn/src/test/edit/ha_flexible_edit_log_context_test.cc
    dancenn/src/test/edit/java_const.cc
    dancenn/src/test/edit/java_const.h
    dancenn/src/test/edit/new_edit_log_test.cc
    dancenn/src/test/edit/non_ha_edit_log_context_test.cc
    dancenn/src/test/edit/sender_test.cc
    dancenn/src/test/edit/serializer_test.cc
    dancenn/src/test/edit/tailer_test.cc
    dancenn/src/test/fsimage/fsimage_test.cc
    dancenn/src/test/ha/ha_mode_transition_manager_test.cc
    dancenn/src/test/ha/ha_state_test.cc
    dancenn/src/test/http/configuration_handler_test.cc
    dancenn/src/test/http/decommission_handler_test.cc
    dancenn/src/test/http/metrics_handler_test.cc
    dancenn/src/test/http/stale_handler_test.cc
    dancenn/src/test/iam/model/policy_document_test.cc
    dancenn/src/test/job_manager/job_manager_test.cc
    dancenn/src/test/job_manager/job_test.cc
    dancenn/src/test/job_manager/job_test.h
    dancenn/src/test/job_manager/job_tracker_test.cc
    dancenn/src/test/job_manager/managed_job_test.cc
    dancenn/src/test/job_manager/managed_task_test.cc
    dancenn/src/test/lease/gmock_lease_manager.h
    dancenn/src/test/lease/lease_manager_test.cc
    dancenn/src/test/lease/lease_manager_v2_test.cc
    dancenn/src/test/lease/lease_monitor_v2_test.cc
    dancenn/src/test/namespace/abandon_block_test.cc
    dancenn/src/test/namespace/access_counter_manager_test.cc
    dancenn/src/test/namespace/active_standby_sync_test.cc
    dancenn/src/test/namespace/file_finalizer_test.cc
    dancenn/src/test/namespace/gmock_file_finalizer.h
    dancenn/src/test/namespace/gmock_meta_storage.cc
    dancenn/src/test/namespace/gmock_meta_storage.h
    dancenn/src/test/namespace/gmock_writer.h
    dancenn/src/test/namespace/inode.cc
    dancenn/src/test/namespace/inode.h
    dancenn/src/test/namespace/lifecycle_test.cc
    dancenn/src/test/namespace/meta_scanner_test.cc
    dancenn/src/test/namespace/meta_scanner_v2_test.cc
    dancenn/src/test/namespace/meta_storage_test.cc
    dancenn/src/test/namespace/mock_meta_scanner_v2.h
    dancenn/src/test/namespace/mock_namespace.cc
    dancenn/src/test/namespace/mock_namespace.h
    dancenn/src/test/namespace/namespace_block_placement_test.cc
    dancenn/src/test/namespace/namespace_stat_checker_test.cc
    dancenn/src/test/namespace/namespace_stat_test.cc
    dancenn/src/test/namespace/namespace_test.cc
    dancenn/src/test/namespace/namespace_test_base.cc
    dancenn/src/test/namespace/namespace_test_base.h
    dancenn/src/test/namespace/namespace_test_v2.cc
    dancenn/src/test/namespace/nn_cluster_test_base.cc
    dancenn/src/test/namespace/nn_cluster_test_base.h
    dancenn/src/test/namespace/quota_collector_test.cc
    dancenn/src/test/namespace/quota_manager_test.cc
    dancenn/src/test/namespace/read_write_with_snapshots_test.cc
    dancenn/src/test/namespace/reconcile_inode_attr_test.cc
    dancenn/src/test/namespace/replica_policy_cache_test.cc
    dancenn/src/test/namespace/snapshot_test.cc
    dancenn/src/test/namespace/ttl_atime_collector_test.cc
    dancenn/src/test/namespace/xattr_test.cc
    dancenn/src/test/proto/generated/cloudfs/datanode_protocol.cc
    dancenn/src/test/proto/generated/cloudfs/datanode_protocol.h
    dancenn/src/test/proto/generated/cloudfs/hdfs.cc
    dancenn/src/test/proto/generated/cloudfs/hdfs.h
    dancenn/src/test/proto/generated/dancenn/block_info_proto.cc
    dancenn/src/test/proto/generated/dancenn/block_info_proto.h
    dancenn/src/test/proto/generated/dancenn/edit_log.cc
    dancenn/src/test/proto/generated/dancenn/edit_log.h
    dancenn/src/test/proto/generated/repeated_field.h
    dancenn/src/test/proxy/fanout_closure_test.cc
    dancenn/src/test/proxy/frozen_directory_manager_test.cc
    dancenn/src/test/proxy/mount_entry_test.cc
    dancenn/src/test/proxy/mounts_manager_test.cc
    dancenn/src/test/proxy/path_team_space_quota_manager_test.cc
    dancenn/src/test/proxy/quota_or_usage_test.cc
    dancenn/src/test/proxy/storage_policy_ttl_manager_test.cc
    dancenn/src/test/proxy/throttler_test.cc
    dancenn/src/test/rpc/cs_test.cc
    dancenn/src/test/rpc/failovered_rpc_channel_2_test.cc
    dancenn/src/test/rpc/failovered_rpc_channel_test.cc
    dancenn/src/test/rpc/hdfs_client_test.cc
    dancenn/src/test/rpc/pooled_rpc_channel_test.cc
    dancenn/src/test/rpc/stateless_rpc_channel_test.cc
    dancenn/src/test/rpc/test_service.cc
    dancenn/src/test/rpc/test_service.h
    dancenn/src/test/safemode/gmock_safemode.h
    dancenn/src/test/safemode/safemode_test.cc
    dancenn/src/test/safemode/safemode_with_bm_test.cc
    dancenn/src/test/security/block_token_test.cc
    dancenn/src/test/service/client_namenode_service_test.cc
    dancenn/src/test/tos/gmock_assume_role_client.h
    dancenn/src/test/tos/mock_tos_cred.h
    dancenn/src/test/tos/tos_cred_keeper_test.cc
    dancenn/src/test/tos/ufs_auth_conf_test.cc
    dancenn/src/test/ufs/hdfs_ufs/hdfs_ufs_test.cc
    dancenn/src/test/ufs/tos_ufs/mock_tos_client.h
    dancenn/src/test/ufs/tos_ufs/mock_tos_event.h
    dancenn/src/test/ufs/tos_ufs/mock_tos_ufs.h
    dancenn/src/test/ufs/tos_ufs/tos_event_test.cc
    dancenn/src/test/ufs/mock_ufs.h
    dancenn/src/test/ufs/mock_write_back_manager_v2.h
    dancenn/src/test/ufs/tos_ufs_perf_test.cc
    dancenn/src/test/ufs/tos_ufs_test.cc
    dancenn/src/test/ufs/tos_ufs_test_v2.cc
    dancenn/src/test/ufs/ufs_event_manager_status_test.cc
    dancenn/src/test/ufs/ufs_event_manager_test.cc
    dancenn/src/test/ufs/ufs_event_rmq_consumer_test.cc
    dancenn/src/test/ufs/ufs_util_test.cc
    dancenn/src/test/ufs/write_back_manager_v2_test.cc
    dancenn/src/test/dancenn_test_base.h
    dancenn/src/test/dancenn_test_enviroment.cc
    dancenn/src/test/dancenn_test_enviroment.h
    dancenn/src/test/gmock_edit_log_input_context.h
    dancenn/src/test/gmock_edit_log_sender.h
    dancenn/src/test/gmock_edit_log_sync_listener.h
    dancenn/src/test/gmock_ha_state.h
    dancenn/src/test/gmock_job_manager.h
    dancenn/src/test/matcher.h
    dancenn/src/test/mock_edit_log_context.h
    dancenn/src/test/mock_edit_log_input_context.h
    dancenn/src/test/mock_edit_log_sender.h
    dancenn/src/test/mock_edit_log_sync_listener.h
    dancenn/src/test/mock_ha_state.h
    dancenn/src/test/mock_safe_mode.h
    dancenn/src/test/test_main.cc
    dancenn/src/third/murmur_hash_3.cc
    dancenn/src/third/murmur_hash_3.h
    dancenn/src/tool/block_recycler_benchmark.cc
    dancenn/src/tool/ckpt_catcher.cc
    dancenn/src/tool/ckpt_comparator.cc
    dancenn/src/tool/crop_inode_tree.cc
    dancenn/src/tool/dancenn_haadmin.cc
    dancenn/src/tool/delete_tos_objects.cc
    dancenn/src/tool/dump_rocksdb.cc
    dancenn/src/tool/fsimage_transfer.cc
    dancenn/src/tool/gs_checker.cc
    dancenn/src/tool/image_diff.cc
    dancenn/src/tool/namesystem_info.cc
    dancenn/src/tool/path_viewer.cc
    dancenn/src/tool/print_dirstat.cc
    dancenn/src/tool/print_editlog.cc
    dancenn/src/tool/print_rocksdb.cc
    dancenn/src/tool/recover_lease.cc
    dancenn/src/tool/tos_assumerole_verify.cc
    dancenn/src/tool/tos_util.cc
    dancenn/src/tool/tos_util_raw.cc
    dancenn/src/tool/verify_correctness.cc
    dancenn/src/ufs/hdfs/hdfs_client.cc
    dancenn/src/ufs/hdfs/hdfs_client.h
    dancenn/src/ufs/hdfs/hdfs_client_metrics.cc
    dancenn/src/ufs/hdfs/hdfs_client_metrics.h
    dancenn/src/ufs/hdfs/hdfs_constant.cc
    dancenn/src/ufs/hdfs/hdfs_constant.h
    dancenn/src/ufs/hdfs_ufs/hdfs_ufs.cc
    dancenn/src/ufs/hdfs_ufs/hdfs_ufs.h
    dancenn/src/ufs/hdfs_ufs/hdfs_ufs_env.cc
    dancenn/src/ufs/hdfs_ufs/hdfs_ufs_env.h
    dancenn/src/ufs/hdfs_ufs/hdfs_ufs_uploader.cc
    dancenn/src/ufs/hdfs_ufs/hdfs_ufs_uploader.h
    dancenn/src/ufs/tos/tos_checksum_utils.cc
    dancenn/src/ufs/tos/tos_checksum_utils.h
    dancenn/src/ufs/tos/tos_client.cc
    dancenn/src/ufs/tos/tos_client.h
    dancenn/src/ufs/tos/tos_client_metrics.cc
    dancenn/src/ufs/tos/tos_client_metrics.h
    dancenn/src/ufs/tos/tos_constant.cc
    dancenn/src/ufs/tos/tos_constant.h
    dancenn/src/ufs/tos/tos_cred.cc
    dancenn/src/ufs/tos/tos_cred.h
    dancenn/src/ufs/tos/tos_cred_keeper.cc
    dancenn/src/ufs/tos/tos_cred_keeper.h
    dancenn/src/ufs/tos/tos_retry_strategy.cc
    dancenn/src/ufs/tos/tos_retry_strategy.h
    dancenn/src/ufs/tos/tos_s3_def.h
    dancenn/src/ufs/tos/tos_s3_forward.h
    dancenn/src/ufs/tos_ufs/tos_event.cc
    dancenn/src/ufs/tos_ufs/tos_event.h
    dancenn/src/ufs/tos_ufs/tos_info.cc
    dancenn/src/ufs/tos_ufs/tos_info.h
    dancenn/src/ufs/tos_ufs/tos_ufs.cc
    dancenn/src/ufs/tos_ufs/tos_ufs.h
    dancenn/src/ufs/tos_ufs/tos_ufs_env.cc
    dancenn/src/ufs/tos_ufs/tos_ufs_env.h
    dancenn/src/ufs/tos_ufs/tos_ufs_task.cc
    dancenn/src/ufs/tos_ufs/tos_ufs_task.h
    dancenn/src/ufs/tos_ufs/tos_ufs_uploader.cc
    dancenn/src/ufs/tos_ufs/tos_ufs_uploader.h
    dancenn/src/ufs/upload/ufs_noupload_manager.cc
    dancenn/src/ufs/upload/ufs_noupload_manager.h
    dancenn/src/ufs/upload/ufs_upload_monitor.cc
    dancenn/src/ufs/upload/ufs_upload_monitor.h
    dancenn/src/ufs/upload/ufs_uploader.cc
    dancenn/src/ufs/upload/ufs_uploader.h
    dancenn/src/ufs/upload/ufs_uploading_manager.cc
    dancenn/src/ufs/upload/ufs_uploading_manager.h
    dancenn/src/ufs/upload/write_back_manager.cc
    dancenn/src/ufs/upload/write_back_manager.h
    dancenn/src/ufs/upload/write_back_manager_metrics.cc
    dancenn/src/ufs/upload/write_back_manager_metrics.h
    dancenn/src/ufs/upload/write_back_manager_v2.cc
    dancenn/src/ufs/upload/write_back_manager_v2.h
    dancenn/src/ufs/persistent_ufs_info.cc
    dancenn/src/ufs/persistent_ufs_info.h
    dancenn/src/ufs/ufs.cc
    dancenn/src/ufs/ufs.h
    dancenn/src/ufs/ufs_auth_conf.cc
    dancenn/src/ufs/ufs_auth_conf.h
    dancenn/src/ufs/ufs_config.h
    dancenn/src/ufs/ufs_dir_status.h
    dancenn/src/ufs/ufs_dir_sync_actions.h
    dancenn/src/ufs/ufs_env.cc
    dancenn/src/ufs/ufs_env.h
    dancenn/src/ufs/ufs_event_kafka_consumer.cc
    dancenn/src/ufs/ufs_event_kafka_consumer.h
    dancenn/src/ufs/ufs_event_manager.cc
    dancenn/src/ufs/ufs_event_manager.h
    dancenn/src/ufs/ufs_event_manager_metrics.cc
    dancenn/src/ufs/ufs_event_manager_metrics.h
    dancenn/src/ufs/ufs_event_manager_status.h
    dancenn/src/ufs/ufs_event_rmq_consumer.cc
    dancenn/src/ufs/ufs_event_rmq_consumer.h
    dancenn/src/ufs/ufs_event_sync_task_factory.cc
    dancenn/src/ufs/ufs_event_sync_task_factory.h
    dancenn/src/ufs/ufs_file_status.cc
    dancenn/src/ufs/ufs_file_status.h
    dancenn/src/ufs/ufs_util.cc
    dancenn/src/ufs/ufs_util.h
    dancenn/src/volc/volc_cred.h
    dancenn/src/flags.cc)
