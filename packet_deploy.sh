#!/bin/bash

build_type=${1:-"Debug"};shift
branch=${1:-"hdfs_test"};shift
tag=${1:-"inf.hdfs.test.nn.beijing"};shift

set -e -o pipefail
base_dir=$(cd $(dirname ${BASH_SOURCE:-$0});pwd);
parent_dir=$(cd ${base_dir}/../; pwd);
deploy_dir=${parent_dir}/dancenn_deploy;

if [ ! -d ${deploy_dir}  ];then
    pushd $base_dir

    if [ "x${USER}x" == "xtigerx" ]; then
	git clone ssh://<EMAIL>:29418/inf/dancenn_deploy dancenn_deploy
    else 
    	user=$(git config -l|perl -lne 'print $1 if/user.email=(\w+)\@bytedance.com/')
    	[ -z "${user}" ] && user=$(cat ~/.ssh/*.pub|perl -lne 'print $1 if/\b(\w+)\b\@bytedance.com/')
	git clone ssh://${user}@git.byted.org:29418/dancenn_deploy dancenn_deploy
    fi
    popd
fi

find ${build_type}/conf/ -maxdepth 1 -type f -exec cp '{}' ${deploy_dir}/conf/ \;
find ${build_type}/bin/ -maxdepth 1 -type f -exec cp '{}' ${deploy_dir}/bin/ \;
find ${build_type}/test/ -maxdepth 1 -type f -exec cp '{}' ${deploy_dir}/test/ \;

pushd ${deploy_dir}

# git checkout ${branch}
set +e +o pipefail
branch_exist=$(git branch -v | grep ${branch})
set -e -o pipefail
if [ -z "${branch_exist}" ];then
	git checkout -b ${branch}
elif [ "x${branch_exist%% ${branch}}x" != "x*x" ];then
	git checkout ${branch}
fi

# git {add, commmit, push}
git add conf bin test && \
	git commit -m "$(date +'auto generated by ci %Y-%m-%d %T')" && \
	git push origin -f ${branch}:${branch}
popd
