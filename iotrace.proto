syntax = "proto2";
option java_package = "com.volcengine.cloudfs.proto";
package cloudfs;

message IOTraceProto {
  // primary_key used by client,nn,dn. Generated by client. May NOT be global unique
  required int64 trace_id = 1;
  optional string task_id = 2;
  // CLI_V1 = 0;
  // CLI_V2 = 1;
  // NN_PROXY = 2;
  // NAMENODE = 3;
  // DATANODE = 4;
  optional int32 component = 3;
  optional string src_ip = 4;
  optional string dst_ip = 5;
  optional uint64 filesystem_id = 6;
  optional uint64 namespace_id = 7;
  optional string path = 8;
  optional uint64 inode_id = 9;
  optional uint64 block_id = 10;
  optional string method = 11;
  optional string step = 12;
  optional bool success = 13;
  optional int64 start_ms = 14; // ms, NOT us, because CH need ms as partition
  optional int64 cost_us = 15; // us NOT ms
  optional uint64 data_size = 16; // only for DN read_block/write_block
  optional uint64 data_offset = 17; // only for DN read_block/write_block
  optional string msg = 18; // exception or msg when fail
  optional string task_id2 = 19;
  optional string task_id3 = 20;
  // conn_ip stands for connect_ip, which is the NIC ip for multi-nic machine
  optional string src_conn_ip = 21;
  optional string dst_conn_ip = 22;
  optional string client_name = 23;
  optional int32 call_id = 24;
  optional int64 pf_write_req_us = 25;
  optional int64 pf_trans_us = 26;
  optional int64 pf_wait_deserial_us = 27;
  optional int64 pf_wait_conn_us = 28;
}
