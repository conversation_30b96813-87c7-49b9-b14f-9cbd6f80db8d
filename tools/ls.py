#!/usr/bin/env python
# coding: utf-8

import os
import sys
from threading import Thread
from snakebite.client import Client


def _get_snakebite(host='localhost', port=8020):
    return Client(host=host, port=port)


def _test_ls(sb, path='/'):
    paths = [path]
    for i in range(10000):
        # t = sb.ls(paths, include_children=False)
        t = sb.ls(paths, include_children=False)
        print "ls", path, "result:", list(t)




def _run_all_tests():
    # sb = _get_snakebite('************', 8888)
    # sb = _get_snakebite('************', 8888)
    sb = _get_snakebite('************', 8898)
    _test_ls(sb, '/')


sb = _get_snakebite('************', 8898)


def ls_for_test(path):
    t = sb.ls([path])
    print "ls", path, "result:"
    for x in list(t):
        print x


def mkdir_for_test(path):
    if not path:
        return
    res = sb.mkdir([path], create_parent=True)
    result = next(res, None)
    print "mkdir", path, "result:", result


def touchz_for_test(path):
    if not path:
        return
    res = sb.touchz([path])
    result = next(res, None)
    print "touchz", path, "result:", result

def delete_for_test(path):
    if not path:
        return
    res = sb.delete([path], recurse=True)
    result = next(res, None)
    print "delete", path, "result:", result


def namespace_test():
    print "==============="
    ls_for_test("/")
    print "==============="
    mkdir_for_test("/test_dir")
    ls_for_test("/")
    print "==============="
    delete_for_test("/test_dir")
    ls_for_test("/")
    print "==============="
    touchz_for_test("/test_file")
    print ls_for_test("/")
    print "==============="
    delete_for_test("/test_file")
    ls_for_test("/")


def parallel_run():
    threads = []
    for i in range(10):
        t = Thread(target=_run_all_tests, args=())
        t.start()
        threads.append(t)
    for t in threads:
        t.join()


if __name__ == '__main__':
    namespace_test()
    # parallel_run()
