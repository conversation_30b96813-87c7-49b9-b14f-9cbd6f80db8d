package com.bytedance;

import java.io.*;
import java.net.URI;
import java.util.*;
import java.util.concurrent.*;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import org.apache.hadoop.conf.*;
import org.apache.hadoop.hdfs.NameNodeProxies;
import org.apache.hadoop.hdfs.protocol.ClientProtocol;
import org.apache.hadoop.security.SaslRpcServer;
import org.apache.hadoop.security.UserGroupInformation;
import org.apache.hadoop.util.*;
import com.bytedance.btrace.proto.ByteTraceLog;
import com.bytedance.btrace.proto.OperationType;
import org.apache.kafka.clients.consumer.*;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.serialization.ByteArrayDeserializer;
import org.apache.log4j.Logger;

import javax.annotation.Nullable;

public class DanceReplay implements Tool {
    private static Logger logger = Logger.getLogger(DanceReplay.class);

    public static class UpstreamTicket {
        public final String user;
        public final String fs;

        public UpstreamTicket(String user, String fs) {
            this.user = user;
            this.fs = fs;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (!(o instanceof UpstreamTicket)) return false;

            UpstreamTicket that = (UpstreamTicket) o;

            if (user != null ? !user.equals(that.user) : that.user != null) return false;
            return !(fs != null ? !fs.equals(that.fs) : that.fs != null);
        }

        @Override
        public int hashCode() {
            int result = user != null ? user.hashCode() : 0;
            result = 31 * result + (fs != null ? fs.hashCode() : 0);
            return result;
        }
    }

    static class ReplayRequest {
        String user;
        String method;
        String path;
    }

    public static class RequestReplayer implements Runnable {
        private DanceReplay replay;
        private ReplayRequest request;
        RequestReplayer(DanceReplay replay, ReplayRequest request) {
            this.request = request;
            this.replay = replay;
        }
        @Override
        public void run() {
            try {
                String fs = replay.conf.get("fs.defaultFS");
                if (replay.fs != null && !replay.fs.isEmpty()) {
                    fs = replay.fs;
                }
                ClientProtocol protocol = replay.upstreamCache.get(new UpstreamTicket(request.user, fs));
                if (request.method == "getBlockLocations") {
                    try {
                        protocol.getBlockLocations(request.path, 0, Long.MAX_VALUE);
                    } catch (IOException e) {
                        logger.error(e);
                    }
                } else if (request.method == "getListing") {
                    try {
                        protocol.getListing(request.path, "".getBytes(), true);
                    } catch (IOException e) {
                        logger.error(e);
                    }
                } else if (request.method == "getFileInfo") {
                    try {
                        protocol.getFileInfo(request.path);
                    } catch (IOException e) {
                        logger.error(e);
                    }
                } else {
                    logger.error("Unsupported method: " + request.method);
                }
            } catch (ExecutionException ee) {
                throw new RuntimeException(ee);
            }
        }
    }

    private Configuration conf;
    private ThreadPoolExecutor executor;
    private String fs;
    LoadingCache<UpstreamTicket, ClientProtocol> upstreamCache;

    public DanceReplay() {
        upstreamCache = CacheBuilder.<UpstreamTicket, ClientProtocol>newBuilder()
                .expireAfterAccess(3600000, TimeUnit.MILLISECONDS)
                .build(new CacheLoader<UpstreamTicket, ClientProtocol>() {
                    @Override
                    public ClientProtocol load(UpstreamTicket ticket) throws Exception {
                        if (ticket.user != null) {
                            UserGroupInformation.setLoginUser(UserGroupInformation.createRemoteUser(ticket.user,
                                    SaslRpcServer.AuthMethod.SIMPLE));
                        } else {
                            UserGroupInformation.setLoginUser(null);
                        }
                        URI fsUri = URI.create(ticket.fs);
                        NameNodeProxies.ProxyAndInfo proxyAndInfo = NameNodeProxies.createProxy(conf, fsUri, ClientProtocol.class);
                        logger.info("New upstream: " + ticket.user + "@" + ticket.fs);
                        return (ClientProtocol) proxyAndInfo.getProxy();
                    }
                });
        executor = new ThreadPoolExecutor(100, 100,
                0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(100 * 2), r -> {
            Thread th = new Thread(r);
            th.setName("dancereplay-executor");
            th.setDaemon(true);
            return th;
        });
        executor.prestartAllCoreThreads();
    }

    @Override
    public void setConf(Configuration conf) {
        this.conf = conf;
    }

    @Override
    public Configuration getConf() {
        return this.conf;
    }

    @Override
    public int run(String [] args) throws Exception {
        String brokers = conf.get("bytetrace.log.kafka.brokers", "10.10.192.34:9097,10.11.148.143:9097,10.11.148.145:9097,10.11.148.147:9097,10.11.148.149:9097,10.11.148.151:9097,10.11.148.153:9097,10.11.148.155:9097,10.11.148.157:9097,10.11.148.159:9097,10.11.148.161:9097,10.11.148.163:9097,10.11.148.165:9097,10.11.148.167:9097,10.11.148.169:9097,10.11.148.171:9097,10.11.148.196:9097,10.11.148.198:9097,10.11.148.200:9097,10.11.148.203:9097,10.11.148.205:9097,10.11.148.207:9097,10.11.148.209:9097,10.11.149.134:9097,10.3.129.152:9097,10.3.129.153:9097,10.3.129.155:9097,10.3.129.157:9097,10.3.130.38:9097,10.3.130.39:9097,10.3.130.40:9097,10.3.130.41:9097,10.3.130.42:9097,10.3.130.43:9097,10.3.130.66:9097,10.3.130.68:9097,10.3.130.70:9097,10.3.133.140:9097,10.8.132.35:9097,10.8.132.36:9097,10.8.132.37:9097,10.8.132.38:9097,10.8.132.39:9097,10.8.132.40:9097,10.8.132.41:9097,10.8.132.42:9097,10.8.132.43:9097,10.8.132.44:9097,10.8.132.45:9097,10.8.132.46:9097,10.8.132.47:9097,10.8.132.48:9097,10.8.132.66:9097,10.8.151.202:9097,10.8.152.130:9097,10.8.152.131:9097,10.8.152.132:9097,10.8.152.133:9097,10.8.152.134:9097,10.8.152.136:9097,10.8.152.194:9097,10.8.152.195:9097,10.8.152.196:9097,10.8.152.197:9097,10.8.152.198:9097,10.8.152.199:9097,10.8.152.200:9097,10.8.158.16:9097,10.8.158.99:9097,10.8.163.161:9097");
        String topic = conf.get("bytetrace.log.kafka.topic", "bytetrace_log");
        String consumerGroup = conf.get("bytetrace.log.kafka.consumer", "dancereplayer");
        this.fs = conf.get("replay.fs", "");
        logger.info("configured fs: " + this.fs);
        int pollTimeout = 30000;
        Properties props = new Properties();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, brokers);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, consumerGroup);
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, "true");
        props.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, "5000");
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, ByteArrayDeserializer.class.getName());
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, ByteArrayDeserializer.class.getName());
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 1000);
        // see https://stackoverflow.com/questions/39730126/difference-between-session-timeout-ms-and-max-poll-interval-ms-for-kafka-0-10-0
        props.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, 3600000);
        KafkaConsumer<byte[], byte[]> consumer = new KafkaConsumer<>(props);
        consumer.subscribe(Arrays.asList(topic), new ConsumerRebalanceListener() {
            public void onPartitionsRevoked(Collection<TopicPartition> partitions) {
            }

            public void onPartitionsAssigned(Collection<TopicPartition> partitions) {
                consumer.seekToEnd(partitions);
            }
        });

        while (true) {
            ConsumerRecords<byte[], byte[]> records = consumer.poll(pollTimeout);
            for (ConsumerRecord<byte[], byte[]> record : records) {
                RequestReplayer replayer = null;
                try {
                    ByteTraceLog log = ByteTraceLog.parseFrom(record.value());
                    if (log.getOperationType() != OperationType.READ) {
                        continue;
                    }
                    ReplayRequest request = new ReplayRequest();
                    request.path = log.getResourcePath();
                    request.user = "tiger";
                    if (log.getBtid().hasUser()) {
                        request.user = log.getBtid().getUser();
                    }
                    JsonNode extra = new ObjectMapper().readTree(log.getExtra());
                    if (extra.get("method").asText().equals("getBlockLocations")) {
                        request.method = "getBlockLocations";
                    } else if (extra.get("method").asText().equals("getBlockLocations")) {
                        request.method = "getFileInfo";
                    } else if (extra.get("method").asText().equals("getListing")) {
                        request.method = "getListing";
                    } else {
                        continue;
                    }
                    replayer = new RequestReplayer(this, request);
                    executor.execute(replayer);
                } catch (IOException e) {
                    logger.error("Parse btrace log failed");
                } catch (RejectedExecutionException e) {
                    while (true) {
                        try {
                            if (replayer != null) {
                                executor.execute(replayer);
                                logger.info("Retried on replayer " + replayer);
                            }
                            break;
                        } catch (RejectedExecutionException re) {
                            Thread.sleep(10);
                        } catch (Throwable tt) {
                            break;
                        }
                    }
                } catch (Throwable t) {
                    logger.info("Failed " + t.toString());
                    t.printStackTrace();
                }
            }
        }
    }

    public static void main(String[] args) throws Exception {
        System.exit(ToolRunner.run(new DanceReplay(), args));
    }
}

