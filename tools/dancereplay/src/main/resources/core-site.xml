<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="configuration.xsl"?>
<!--
  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. See accompanying LICENSE file.
-->

<!-- Put site-specific property overrides in this file. -->



<configuration>
  <property>
    <name>fs.defaultFS</name>
    <value>hdfs://haruna</value>
  </property>
  <property>
    <name>ipc.server.tcpnodelay</name>
    <value>true</value>
  </property>
  <property>
    <name>ipc.client.tcpnodelay</name>
    <value>true</value>
  </property>
  <property>
    <name>ipc.server.listen.queue.size</name>
    <value>4096</value>
  </property>
  <property>
    <name>io.file.buffer.size</name>
    <value>131072</value>
  </property>
  <property>
    <name>hadoop.tmp.dir</name>
    <value>/data12/yarn_${user.name}/tmp</value>
  </property>
  <property>
    <name>fs.trash.interval</name>
    <value>10080</value>
  </property>
  <property>
    <name>ha.zookeeper.quorum</name>
    <value>************,***********,************,***********,************</value>
  </property>
  <property>
    <name>ha.zookeeper.session-timeout.ms</name>
    <value>60000</value>
  </property>



  <property>
    <name>hadoop.native.lib</name>
    <value>true</value> 
    <description> Should native hadoop libraries, if present, be used.</description>
  </property>
  <property>
    <name>io.compression.codecs</name>
    <value>org.apache.hadoop.io.compress.GzipCodec,org.apache.hadoop.io.compress.DefaultCodec,org.apache.hadoop.io.compress.BZip2Codec,org.apache.hadoop.io.compress.SnappyCodec,io.sensesecure.hadoop.xz.XZUnsplittableCodec,com.bytedance.hadoop.io.compress.BroCodec,com.bytedance.ZstdCodec,com.bytedance.bec.hdfs.codec.EC32RS192Codec</value>
  </property>
  <property>
    <name>io.compression.codec.lzo.class</name>
    <value>com.hadoop.compression.lzo.LzoCodec</value>
  </property>
  <property>
    <name>topology.script.file.name</name>
    <value>/opt/tiger/hdfs_deploy/rack_aware</value>
  </property>
  <property>
    <name>hadoop.http.staticuser.user</name>
    <value>tiger</value>
  </property>
  <property>
    <name>dfs.permissions</name>
    <value>false</value>
  </property>

  <property>
    <name>hadoop.proxyuser.hue.hosts</name>
    <value>*</value>
  </property>
  <property>
    <name>hadoop.proxyuser.hue.groups</name>
    <value>*</value>
  </property>

  <property>
    <name>hadoop.proxyuser.tiger.hosts</name>
    <value>*</value>
  </property>
  <property>
    <name>hadoop.proxyuser.tiger.groups</name>
    <value>*</value>
  </property>
  <property>
    <name>net.topology.datacenters</name>
    <value>LF,HL</value>
  </property>
  <property>
    <name>net.topology.impl</name>
    <value>org.apache.hadoop.net.NetworkTopologyWithMultiDC</value>
  </property>
  <property>
    <name>dfs.block.replicator.classname</name>
    <value>org.apache.hadoop.hdfs.server.blockmanagement.BlockPlacementPolicyWithMultiDC</value>
  </property>
  <property>
    <name>ha.health-monitor.rpc-timeout.ms</name>
    <value>180000</value>
  </property>
  <property>
    <name>ha.failover-controller.graceful-fence.rpc-timeout.ms</name>
    <value>300000</value>
  </property>
  <property>
    <name>ha.failover-controller.new-active.rpc-timeout.ms</name>
    <value>300000</value>
  </property>
  <property>
    <name>io.erasurecode.codec.rs.rawcoder</name>
    <value>org.apache.hadoop.io.erasurecode.rawcoder.NativeRSRawErasureCoderFactory</value>
  </property>
  <property>
    <name>bec.recovery.topic.brokers</name>
    <value>10.10.150.218:9094,10.10.150.219:9094,10.10.150.220:9094,10.10.150.222:9094,10.10.150.223:9094,10.10.150.224:9094,10.10.150.226:9094,10.10.150.228:9094,10.10.150.230:9094,10.10.150.232:9094,10.10.150.234:9094,10.10.151.100:9094,10.10.151.102:9094,10.10.151.104:9094,10.10.151.106:9094,10.10.151.12:9094,10.10.151.130:9094,10.10.151.133:9094,10.10.151.134:9094,10.10.151.136:9094,10.10.151.138:9094,10.10.151.14:9094,10.10.151.140:9094,10.10.151.142:9094,10.10.151.144:9094,10.10.151.146:9094,10.10.151.148:9094,10.10.151.150:9094,10.10.151.152:9094,10.10.151.154:9094,10.10.151.156:9094,10.10.151.158:9094,10.10.151.16:9094,10.10.151.160:9094,10.10.151.162:9094,10.10.151.164:9094,10.10.151.166:9094,10.10.151.168:9094,10.10.151.170:9094,10.10.151.18:9094,10.10.151.194:9094,10.10.151.198:9094,10.10.151.20:9094,10.10.151.200:9094,10.10.151.202:9094,10.10.151.204:9094,10.10.151.206:9094,10.10.151.208:9094,10.10.151.210:9094,10.10.151.212:9094,10.10.151.214:9094,10.10.151.216:9094,10.10.151.218:9094,10.10.151.22:9094,10.10.151.220:9094,10.10.151.222:9094,10.10.151.226:9094,10.10.151.228:9094,10.10.151.230:9094,10.10.151.232:9094,10.10.151.234:9094,10.10.151.24:9094,10.10.151.26:9094,10.10.151.28:9094,10.10.151.30:9094,10.10.151.32:9094,10.10.151.34:9094,10.10.151.36:9094,10.10.151.38:9094,10.10.151.40:9094,10.10.151.42:9094,10.10.151.43:9094,10.10.151.46:9094,10.10.151.48:9094,10.10.151.50:9094,10.10.151.52:9094,10.10.151.66:9094,10.10.151.68:9094,10.10.151.70:9094,10.10.151.72:9094,10.10.151.74:9094,10.10.151.76:9094,10.10.151.78:9094,10.10.151.80:9094,10.10.151.82:9094,10.10.151.84:9094,10.10.151.86:9094,10.10.151.88:9094,10.10.151.90:9094,10.10.151.92:9094,10.10.151.94:9094,10.10.151.96:9094,10.10.151.98:9094,10.10.152.12:9094,10.10.152.14:9094,10.10.152.16:9094,10.10.152.18:9094,10.10.152.20:9094,10.10.152.22:9094,10.10.152.24:9094</value>
  </property>
  <property>
    <name>bec.recovery.topic.name</name>
    <value>dfs_bec_recovery</value>
  </property>
  <property>
    <name>bec.recovery.fresh.topic.name</name>
    <value>dfs_bec_recovery_fresh</value>
  </property>
  <property>
    <name>bec.recovery.spark.app.name</name>
    <value>BEC_Recovery_hetianyi</value>
  </property>
  <property>
    <name>bec.recovery.spark.checkpoint.dir</name>
    <value>/inf/bec/bec_recovery_ckpt</value>
  </property>
  <property>
    <name>bec.recovery.topic.zk</name>
    <value>10.3.31.48:2181,10.3.31.49:2181,10.3.31.50:2181,10.3.31.51:2181,10.3.31.66:2181</value>
  </property>
</configuration>