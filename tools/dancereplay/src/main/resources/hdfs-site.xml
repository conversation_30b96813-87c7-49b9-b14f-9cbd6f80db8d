<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="configuration.xsl"?>
<!--
  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License. See accompanying LICENSE file.
-->

<!-- Put site-specific property overrides in this file. -->

<configuration>
  <property>
    <name>dfs.nameservices</name>
    <value>haruna,clojure,kingkong,athena,harunasg,harunava,harunavaali,harunabackend,clojurebackend,tsdhybackend,lispbackend,rustbackend,teabackend,hbasebackend,scalabackend,athenabackend,hermesbackend,apollobackend,nereusbackend</value>
  </property>


  <property>
    <name>dfs.internal.nameservices</name>
    <value>harunabackend,clojurebackend,lispbackend,rustbackend,teabackend,hbasebackend,scalabackend,athenabackend,hermesbackend,apollobackend,nereusbackend</value>
  </property>


  <!-- kingkong -->
  <property>
    <name>dfs.ha.namenodes.kingkong</name>
    <value>p0,p1,p2,p3</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.kingkong.p0</name>
    <value>***********:65212</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.kingkong.p1</name>
    <value>***********:65212</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.kingkong.p2</name>
    <value>***********:65212</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.kingkong.p3</name>
    <value>***********:65212</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.kingkong</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.ha.automatic-failover.enabled.kingkong</name>
    <value>true</value>
  </property>
  <!-- haruna -->
  <property>
    <name>dfs.ha.namenodes.haruna</name>
    <value>p0,p1,p2,p3,p4,p5,p6</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.haruna.p0</name>
    <value>***********:65212</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.haruna.p3</name>
    <value>***********:65212</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.haruna.p4</name>
    <value>***********:65212</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.haruna.p5</name>
    <value>***********:65212</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.haruna.p6</name>
    <value>***********:65212</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.haruna</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.ha.automatic-failover.enabled.haruna</name>
    <value>true</value>
  </property>
  <!-- clojure -->
  <property>
    <name>dfs.ha.namenodes.clojure</name>
    <value>p0,p1,p2,p3,p4,p5,p6</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.clojure.p0</name>
    <value>***********:65212</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.clojure.p1</name>
    <value>***********:65212</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.clojure.p2</name>
    <value>***********:65212</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.clojure.p3</name>
    <value>***********:65212</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.clojure.p4</name>
    <value>***********:65212</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.clojure.p5</name>
    <value>***********:65212</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.clojure.p6</name>
    <value>***********:65212</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.clojure</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.ha.automatic-failover.enabled.clojure</name>
    <value>true</value>
  </property>
  <!-- athena -->
  <property>
    <name>dfs.ha.namenodes.athena</name>
    <value>p0,p1</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.athena.p0</name>
    <value>127.0.0.1:65212</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.athena.p1</name>
    <value>127.0.0.1:65212</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.athena</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.ha.automatic-failover.enabled.athena</name>
    <value>true</value>
  </property>
  <!-- harunasg -->
  <property>
    <name>dfs.ha.namenodes.harunasg</name>
    <value>p0,p1,p2</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.harunasg.p0</name>
    <value>************:65212</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.harunasg.p1</name>
    <value>************:65212</value>
  </property>
    <property>
    <name>dfs.namenode.rpc-address.harunasg.p2</name>
    <value>************:65212</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.harunasg</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.ha.automatic-failover.enabled.harunasg</name>
    <value>true</value>
  </property>
  <!-- harunava -->
  <property>
    <name>dfs.ha.namenodes.harunava</name>
    <value>p0,p1,p2</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.harunava.p0</name>
    <value>************:65212</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.harunava.p1</name>
    <value>************:65212</value>
  </property>
    <property>
    <name>dfs.namenode.rpc-address.harunava.p2</name>
    <value>************:65212</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.harunava</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.ha.automatic-failover.enabled.harunava</name>
    <value>true</value>
  </property>
  <!-- harunavaali -->
  <property>
    <name>dfs.ha.namenodes.harunavaali</name>
    <value>p0,p1,p2</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.harunavaali.p0</name>
    <value>************:65212</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.harunavaali.p1</name>
    <value>************:65212</value>
  </property>
    <property>
    <name>dfs.namenode.rpc-address.harunavaali.p2</name>
    <value>************:65212</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.harunavaali</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  <property>
    <name>dfs.ha.automatic-failover.enabled.harunavaali</name>
    <value>true</value>
  </property>
  <!-- haruna -->
  
  
  
  <property>
    <name>dfs.ha.namenodes.harunabackend</name>
    <value>har2,una2</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.harunabackend.har2</name>
    <value>***********:5061</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.harunabackend.una2</name>
    <value>***********:5061</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.harunabackend.har2</name>
    <value>***********:5070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.harunabackend.una2</name>
    <value>***********:5070</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.harunabackend.har2</name>
    <value>***********:5060</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.harunabackend.una2</name>
    <value>***********:5060</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.harunabackend</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  
  <property>
    <name>dfs.ha.automatic-failover.enabled.harunabackend</name>
    <value>false</value>
  </property>
  
  <property>
    <name>dfs.permissions.enabled</name>
    <value>false</value>
  </property>
  
  
  
  <!-- clojurebackend -->
  
  
  
  <property>
    <name>dfs.ha.namenodes.clojurebackend</name>
    <value>clo2,jure2</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.clojurebackend.clo2</name>
    <value>***********:5060</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.clojurebackend.jure2</name>
    <value>***********:5060</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.clojurebackend.clo2</name>
    <value>***********:5061</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.clojurebackend.jure2</name>
    <value>***********:5061</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.clojurebackend.clo2</name>
    <value>***********:5070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.clojurebackend.jure2</name>
    <value>***********:5070</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.clojurebackend</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  
  <property>
    <name>dfs.ha.automatic-failover.enabled.clojurebackend</name>
    <value>false</value>
  </property>
  
  <property>
    <name>dfs.permissions.enabled</name>
    <value>false</value>
  </property>
  
  
  
  <!-- lispbackend -->
  
  
  
  <property>
    <name>dfs.ha.namenodes.lispbackend</name>
    <value>li2,sp2</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.lispbackend.li2</name>
    <value>***********:5060</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.lispbackend.sp2</name>
    <value>***********:5060</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.lispbackend.li2</name>
    <value>***********:5061</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.lispbackend.sp2</name>
    <value>***********:5061</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.lispbackend.li2</name>
    <value>***********:5070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.lispbackend.sp2</name>
    <value>***********:5070</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.lispbackend</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  
  <property>
    <name>dfs.ha.automatic-failover.enabled.lispbackend</name>
    <value>false</value>
  </property>
  
  <property>
    <name>dfs.permissions.enabled</name>
    <value>false</value>
  </property>
  
  
  
  <!-- rustbackend -->
  
  
  
  <property>
    <name>dfs.ha.namenodes.rustbackend</name>
    <value>ru2,st2</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.rustbackend.ru2</name>
    <value>***********:5060</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.rustbackend.st2</name>
    <value>***********:5060</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.rustbackend.ru2</name>
    <value>***********:5061</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.rustbackend.st2</name>
    <value>***********:5061</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.rustbackend.ru2</name>
    <value>***********:5070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.rustbackend.st2</name>
    <value>***********:5070</value>
  </property>
    <property>
    <name>dfs.client.failover.proxy.provider.rustbackend</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  
  <property>
    <name>dfs.ha.automatic-failover.enabled.rustbackend</name>
    <value>false</value>
  </property>
  
  <property>
    <name>dfs.permissions.enabled</name>
    <value>false</value>
  </property>
  
  
  
  <!-- teabackend -->
  
  
  
  <property>
    <name>dfs.ha.namenodes.teabackend</name>
    <value>te2,a2</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.teabackend.te2</name>
    <value>***********:5060</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.teabackend.a2</name>
    <value>***********:5060</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.teabackend.te2</name>
    <value>***********:5061</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.teabackend.a2</name>
    <value>***********:5061</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.teabackend.te2</name>
    <value>***********:5070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.teabackend.a2</name>
    <value>***********:5070</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.teabackend</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  
  <property>
    <name>dfs.ha.automatic-failover.enabled.teabackend</name>
    <value>false</value>
  </property>
  
  <property>
    <name>dfs.permissions.enabled</name>
    <value>false</value>
  </property>
  
  
  
  <!-- hbasebackend -->
  
  
  
  <property>
    <name>dfs.ha.namenodes.hbasebackend</name>
    <value>hba2,se2</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.hbasebackend.hba2</name>
    <value>***********:5060</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.hbasebackend.se2</name>
    <value>***********:5060</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.hbasebackend.hba2</name>
    <value>***********:5061</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.hbasebackend.se2</name>
    <value>***********:5061</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.hbasebackend.hba2</name>
    <value>***********:5070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.hbasebackend.se2</name>
    <value>***********:5070</value>
  </property>
    <property>
    <name>dfs.client.failover.proxy.provider.hbasebackend</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  
  <property>
    <name>dfs.ha.automatic-failover.enabled.hbasebackend</name>
    <value>false</value>
  </property>
  
  <property>
    <name>dfs.permissions.enabled</name>
    <value>false</value>
  </property>
  
  
  
  <!-- scalabackend -->
  
  
  
  <property>
    <name>dfs.ha.namenodes.scalabackend</name>
    <value>sca2,la2</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.scalabackend.sca2</name>
    <value>***********:5060</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.scalabackend.la2</name>
    <value>************:5060</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.scalabackend.sca2</name>
    <value>***********:5061</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.scalabackend.la2</name>
    <value>************:5061</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.scalabackend.sca2</name>
    <value>***********:5070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.scalabackend.la2</name>
    <value>************:5070</value>
  </property>
    <property>
    <name>dfs.client.failover.proxy.provider.scalabackend</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  
  <property>
    <name>dfs.ha.automatic-failover.enabled.scalabackend</name>
    <value>false</value>
  </property>
  
  <property>
    <name>dfs.permissions.enabled</name>
    <value>false</value>
  </property>
  
  
  
  <!-- athenabackend -->
  
  
  
  <property>
    <name>dfs.ha.namenodes.athenabackend</name>
    <value>ath2,ena2</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.athenabackend.ath2</name>
    <value>***********:5060</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.athenabackend.ena2</name>
    <value>***********:5060</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.athenabackend.ath2</name>
    <value>***********:5061</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.athenabackend.ena2</name>
    <value>***********:5061</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.athenabackend.ath2</name>
    <value>***********:5070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.athenabackend.ena2</name>
    <value>***********:5070</value>
  </property>
    <property>
    <name>dfs.client.failover.proxy.provider.athenabackend</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  
  <property>
    <name>dfs.ha.automatic-failover.enabled.athenabackend</name>
    <value>false</value>
  </property>
  
  <property>
    <name>dfs.permissions.enabled</name>
    <value>false</value>
  </property>
  
  
  
  <!-- hermesbackend -->
  
  
  
  <property>
    <name>dfs.ha.namenodes.hermesbackend</name>
    <value>her2,mes2</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.hermesbackend.her2</name>
    <value>***********:5060</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.hermesbackend.mes2</name>
    <value>***********:5060</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.hermesbackend.her2</name>
    <value>***********:5061</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.hermesbackend.mes2</name>
    <value>***********:5061</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.hermesbackend.her2</name>
    <value>***********:5070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.hermesbackend.mes2</name>
    <value>***********:5070</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.hermesbackend</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  
  <property>
    <name>dfs.ha.automatic-failover.enabled.hermesbackend</name>
    <value>false</value>
  </property>
  
  <property>
    <name>dfs.permissions.enabled</name>
    <value>false</value>
  </property>
  
  
  
  <!-- apollobackend -->
  
  
  
  <property>
    <name>dfs.ha.namenodes.apollobackend</name>
    <value>apo2,llo2</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.apollobackend.apo2</name>
    <value>************:5060</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.apollobackend.llo2</name>
    <value>************:5060</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.apollobackend.apo2</name>
    <value>************:5061</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.apollobackend.llo2</name>
    <value>************:5061</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.apollobackend.apo2</name>
    <value>************:5070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.apollobackend.llo2</name>
    <value>************:5070</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.apollobackend</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  
  <property>
    <name>dfs.ha.automatic-failover.enabled.apollobackend</name>
    <value>false</value>
  </property>
  
  <property>
    <name>dfs.permissions.enabled</name>
    <value>false</value>
  </property>
  
  
  
  <!-- nereusbackend -->
  
  
  
  <property>
    <name>dfs.ha.namenodes.nereusbackend</name>
    <value>ner2,eus2</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.nereusbackend.ner2</name>
    <value>************:5060</value>
  </property>
  <property>
    <name>dfs.namenode.rpc-address.nereusbackend.eus2</name>
    <value>*************:5060</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.nereusbackend.ner2</name>
    <value>************:5061</value>
  </property>
  <property>
    <name>dfs.namenode.servicerpc-address.nereusbackend.eus2</name>
    <value>*************:5061</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.nereusbackend.ner2</name>
    <value>************:5070</value>
  </property>
  <property>
    <name>dfs.namenode.http-address.nereusbackend.eus2</name>
    <value>*************:5070</value>
  </property>
  <property>
    <name>dfs.client.failover.proxy.provider.nereusbackend</name>
    <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
  </property>
  
  <property>
    <name>dfs.ha.automatic-failover.enabled.nereusbackend</name>
    <value>false</value>
  </property>
  
  <property>
    <name>dfs.permissions.enabled</name>
    <value>false</value>
  </property>
  
  
  

  <!-- alicloud oss sg -->
  <!-- bucket: oss://bytedance-recommend -->
  <property>
    <name>fs.oss.endpoint.bytedance-recommend</name>

    <value>oss-ap-southeast-1.aliyuncs.com</value>

  </property>
  <property>
    <name>fs.oss.impl</name>
    <value>org.apache.hadoop.fs.aliyun.oss.AliyunOSSFileSystem</value>
  </property>
  <property>
    <name>fs.oss.accessKeyId.bytedance-recommend</name>
    <value>LTAIeQWWSVcNBsza</value>
  </property>
  <property>
    <name>fs.oss.accessKeySecret.bytedance-recommend</name>
    <value>WQC2uOBWOr6TRwBzFUVWA3CvD6HUla</value>
  </property>
  <property>
    <name>job.output.oss.redirect</name>
    <value>false</value>
  </property>
  <property>
    <name>fs.oss.buffer.dirs</name>
    <value>/tmp/oss</value>
  </property>
  <property>
    <name>fs.oss.impl.disable.cache</name>
    <value>true</value>
  </property>
  <property>
    <name>fs.oss.attempts.maximum.bytedance-recommend</name>
    <value>5</value>
  </property>

  <property>
    <name>dfs.datanode.address</name>
    <value>0.0.0.0:5080</value>
  </property>
  <property>
    <name>dfs.datanode.http.address</name>
    <value>0.0.0.0:5085</value>
  </property>
  <property>
    <name>dfs.datanode.ipc.address</name>
    <value>0.0.0.0:5090</value>
  </property>
  <property>
    <name>dfs.namenode.edits.journal-plugin.bookkeeper</name>
    <value>org.apache.hadoop.contrib.bkjournal.BookKeeperJournalManager</value>
  </property>
  <property>
    <name>dfs.namenode.bookkeeperjournal.zk.ledgers</name>
    <value>/bk/ledgers</value>
  </property>
  <property>
    <name>dfs.namenode.bookkeeperjournal.zk.availablebookies</name>
    <value>/bk/ledgers/available</value>
  </property>





  <property>
    <name>dfs.namenode.edits.dir</name>
    <value>/data12/yarn/nndata</value>
  </property>

  <property>
    <name>dfs.journalnode.edits.dir</name>
    <value>/data12/yarn/qjm/journal</value>
  </property>
  <property>
    <name>dfs.journalnode.rpc-address</name>
    <value>0.0.0.0:5075</value>
  </property>
  <property>
    <name>dfs.qjournal.write-txns.timeout.ms</name>
    <value>128000</value>
  </property>
  <property>
    <name>dfs.ha.fencing.methods</name>
    <value>sshfence</value>
  </property>
  <property>
    <name>dfs.ha.fencing.ssh.private-key-files</name>
    <value>/home/<USER>/.ssh/id_rsa</value>
  </property>

  

  <property>
    <name>dfs.client.read.shortcircuit</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.domain.socket.path</name>
    <value>/data12/yarn/dnsock/dn_socket</value>
  </property>
  <property>
    <name>dfs.client.read.shortcircuit.buffer.size</name>
    <value>4096</value>
  </property>
  <property>
    <name>dfs.client.read.shortcircuit.streams.cache.size.expiry.ms</name>
    <value>4096</value>
  </property>

  <property>
    <name>dfs.name.dir</name>
    <value>file:///data12/yarn/nndata</value>
    <final>true</final>
  </property>




  <property>
    <name>dfs.data.dir</name>
    <value>file:///data00/yarn/dndata</value>
    <final>true</final>
  </property>

  <property>
    <name>dfs.replication</name>
    <value>3</value>
  </property>








  <property>
    <name>dfs.datanode.du.reserved</name>
    <value>322122547200</value>
  </property>

  <property>
    <name>dfs.datanode.du.reserved.data00</name>
    <value>408021893120</value>
  </property>


  <property>
    <name>dfs.datanode.du.reserved.data12</name>
    <value>268435456000</value>
  </property>

  <property>
    <name>dfs.balance.bandwidthPerSec</name>
    <value>73400320</value>
  </property>

  <property>
    <name>dfs.block.size</name>
    <value>536870912</value>
    <description>512M</description>
  </property>

  <property>
    <name>dfs.hosts.exclude</name>
    <value>/opt/tiger/hdfs_deploy/hadoop-2.6.0-cdh5.4.4/conf/dfs.excluded</value>
  </property>

  <property>
    <name>dfs.datanode.max.xcievers</name>
    <value>8192</value>
  </property>
  <property>
    <name>dfs.datanode.socket.write.timeout</name>
    <value>256000</value>
  </property>
  <property>
    <name>dfs.socket.timeout</name>
    <value>256000</value>
  </property>

  <property>
    <name>dfs.namenode.handler.count</name>
    <value>320</value>
  </property>
  <property>
    <name>dfs.namenode.service.handler.count</name>
    <value>128</value>
  </property>
  <property>
    <name>dfs.datanode.handler.count</name>
    <value>128</value>
  </property>

  <property>
    <name>dfs.support.append</name>
    <value>true</value>
  </property>

  <property>
    <name>dfs.datanode.hdfs-blocks-metadata.enabled</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.client.file-block-storage-locations.timeout.millis</name>
    <value>10000</value>
  </property>

  <property>
    <name>dfs.webhdfs.enabled</name>
    <value>true</value>
  </property>

  <property>
    <name>dfs.datanode.fsdataset.volume.choosing.policy</name>
    <value>org.apache.hadoop.hdfs.server.datanode.fsdataset.AvailableSpaceVolumeChoosingPolicy</value>
  </property>

  <property>
    <name>dfs.namenode.datanode.registration.ip-hostname-check</name>
    <value>false</value>
  </property>

  <property>
      <name>dfs.datanode.failed.volumes.tolerated</name>
      <value>3</value>
  </property>

  <property>
      <name>dfs.client.block.write.replace-datanode-on-failure.policy</name>
      <value>ALWAYS</value>
  </property>
  <property>
      <name>dfs.datanode.peer.backlog</name>
      <value>32</value>
  </property>
  <property>
      <name>dfs.namenode.replication.considerLoad</name>
      <value>false</value>
  </property>
  <property>
    <name>dfs.namenode.hideremotedn</name>
    <value>false</value>
  </property>
  <property>
    <name>dfs.namenode.hideremotedn.ignore-centralized</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.replica.pipeline.enabled</name>
    <value>true</value>
  </property>
  <property>
    <name>dfs.namenode.replication.interval</name>
    <value>10</value>
  </property>
  <property>
    <name>dfs.namenode.safemode.extension</name>
    <value>600000</value>
  </property>
  <property>
      <name>ipc.ping.interval</name>
      <value>600000</value>
  </property>
  <property>
      <name>dfs.namenode.heartbeat.recheck-interval</name>
      <value>1800000</value>
  </property>
  <property>
      <name>dfs.namenode.avoid.read.stale.datanode</name>
      <value>true</value>
  </property>
  <property>
      <name>dfs.namenode.avoid.write.stale.datanode</name>
      <value>true</value>
  </property>
  <property>
      <name>dfs.namenode.stale.datanode.interval</name>
      <value>120000</value>
  </property>
  <property>
      <name>dfs.client.max.block.acquire.failures</name>
      <value>120</value>
  </property>
  <property>
      <name>dfs.datanode.sync.behind.writes</name>
      <value>true</value>
  </property>
  <property>
      <name>dfs.client.write.max-packets-in-flight</name>
      <value>128</value>
  </property>
  <property>
      <name>dfs.datanode.balance.max.concurrent.moves</name>
      <value>256</value>
  </property>
  <property>
      <name>dfs.namenode.checkpoint.period</name>
      <value>36000</value>
  </property>
  <property>
      <name>dfs.namenode.checkpoint.txns</name>
      <value>10000000</value>
  </property>
  <property>
      <name>dfs.mover.moverThreads</name>
      <value>200</value>
  </property>
  <property>
      <name>dfs.bytes-per-checksum</name>
      <value>4096</value>
  </property>
  
  <property>
      <name>dfs.datanode.max.locked.memory</name>
      <value>32212254720</value>
  </property>
  
  <property>
      <name>dfs.namenode.accesstime.precision</name>
      <value>86400000</value>
  </property>

  <property>
      <name>hdfs.mutlidc.enforce-dc.file</name>
      <value>/opt/tiger/hdfs_deploy/hadoop/conf/enforce-dc.id</value>
  </property>
  <property>
      <name>dfs.client.block.write.locateFollowingBlock.retries</name>
      <value>16</value>
  </property>
  <property>
      <name>dfs.client.failover.max.attempts</name>
      <value>256</value>
  </property>
  <property>
      <name>dfs.namenode.path.based.cache.refresh.interval.ms</name>
      <value>600000</value>
  </property>
  <property>
      <name>fs.du.interval</name>
      <value>14400000</value>
  </property>
  <property>
      <name>hadoop.fuse.timer.period</name>
      <value>5</value>
  </property>
  <property>
      <name>hadoop.fuse.connection.timeout</name>
      <value>300</value>
  </property>
  <property>
      <name>dfs.datanode.available-space-volume-choosing-policy.balanced-space-threshold</name>
      <value>322122547200</value>
      <description>300GB</description>
  </property>
  <property>
      <name>dfs.datanode.available-space-volume-choosing-policy.balanced-space-preference-fraction</name>


      <value>-0.0</value>
      <description>220% (nf) chance to choose volumes with high available space</description>
  </property>

  <property>
      <name>dfs.namenode.datanode.max-avg-ioutil-ms</name>
      <value>300</value>
      <description>MaxIOUtilMs = 300 * numDevices (avg. 13). Means 3900ms total IOUtil.</description>
  </property>

  <property>
      <name>dfs.namenode.overload.multiplier</name>
      <value>3</value>
  </property>
  <property>
      <name>dfs.datanode.max.transfer.threads-per-volume</name>
      <value>30</value>
  </property>
  <property>
      <name>dfs.namenode.bookkeeperjournal.zk.session.timeout</name>
      <value>600000</value>
  </property>

  <property>
      <name>dfs.namenode.replication.work.multiplier.per.iteration</name>
      <value>0.25</value>
  </property>
  <property>
      <name>dfs.namenode.invalidate.work.pct.per.iteration</name>
      <value>0.5</value>
  </property>
  <property>
      <name>dfs.datanode.volume-map.dump.path</name>
      <value>/data00/yarn/dndata/volume_map.bin</value>
  </property>
  <property>
      <name>dfs.datanode.volume-map.dump.ttl.ms</name>
      <value>1800000</value>
  </property>
  <property>
      <name>dfs.namenode.metrics.percentiles.intervals</name>
      <value>30,60</value>
  </property>
  <property>
      <name>dfs.ls.limit</name>
      <!-- maximum (num_block*replication) returned for each getListing call -->
      <value>4096</value>
  </property>


  <property>
    <name>dfs.hosts.forcestale</name>
    <value>/opt/tiger/hdfs_deploy/hadoop-2.6.0-cdh5.4.4/conf/dfs.forcestale</value>
  </property>
  
  <property>
    <name>dfs.block.invalidate.limit</name>

    <value>3000</value>

  </property>
</configuration>