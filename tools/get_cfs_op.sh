#!/bin/bash

if [ "$#" -ne 2 ]; then
  echo "Usgae: $0 <ledger_id> <start_entry_id>"
  echo "Use zk cmd [get /xxx/CurrentInprogress] to get ledger id"
  echo "Use zk cmd [ls /xxx/ledgers] to get ledger id"
  exit 0
fi

/opt/tiger/bk_deploy/bookkeeper-server/bin/bookkeeper shell readledger "$1" "$2" "$2" |\
  sed 's/Entry Id:.*Data: //'                                                         |\
  awk '{print substr($0,10,48)}'                                                      |\
  sed 's/ //g' | sed ':a;N;$!ba;s/\n//g'                                              |\
  sed 's/^.\{50\}//'                                                                  |\
  sed 's/.\{16\}$//'                                                                  |\
  xxd -r -p
