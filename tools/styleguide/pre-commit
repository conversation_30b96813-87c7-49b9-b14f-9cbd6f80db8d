#!/bin/sh
#
# Copy pre-commit script to $GIT_ROOT/.git/hooks/pre-commit

# Enforce C++ Style Checking (Google C++ Style).
# Note: you must have cpplint.py installed in your $PATH
CPPFILES=`git diff --cached --name-only | grep -E '^.+\.(h|hpp|cpp|cc)$'`
if [ ! -z "$CPPFILES" ]; then
	echo "$CPPFILES" | xargs -n 128 cpplint.py --root=src --filter=-build/c++11
	[ $? -gt 0 ] && exit 1
fi

# Enforce Python Style Checking (PEP8)
# http://www.python.org/dev/peps/pep-0008/
# Note: must have pep8 installed in $PATH.
PYFILES=`git diff --cached --name-only | grep -E '^.+\.py$'`
if [ ! -z "$PYFILES" ]; then
	# TODO: add pylint check someday?
	echo "$PYFILES" | xargs -n 128 pep8
	[ $? -gt 0 ] && exit 1
fi

exit 0
