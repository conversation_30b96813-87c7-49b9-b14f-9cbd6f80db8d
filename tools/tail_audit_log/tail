#!/usr/bin/env python
# coding: utf-8

from pykafkaclient.kafka_proxy2.kafka_proxy import KafkaProxy
import argparse
import logging
import threading
import multiprocessing
import json
from datetime import datetime

def parse_cmd():
    parse = argparse.ArgumentParser(description='Tail audit log from specified timestamp. ')
    parse.add_argument('-r', "--region", type=str, required=True, help="Region, cn/va/sg")
    parse.add_argument('-s', "--times_string", type=str, required=True, help="String of time for seek with format YYYY-mm-dd HH:MM:SS")
    parse.add_argument('-n', "--nn", type=str, required=False, default='', help="the namenode backend")
    parse.add_argument('-p', "--psm", type=str, required=False, default='inf.hdfs.dancenn', help="psm")
    parse.add_argument('-o', "--owner", type=str, required=False, default='yangjinfeng.02', help="owner")
    parse.add_argument('-t', "--team", type=str, required=False, default='web-arch', help="team")
    parse.add_argument('-g', "--consumer_group", type=str, required=False, default='AuditLogTailer', help="the consumer group name")
    return parse


def reset_offset(times_string, cluster_name, topic, consumer_group, psm, owner, team, api_version, prefer_service, ignore_dc_check):
    proxy = KafkaProxy(cluster_name=cluster_name,
                       topic=topic,
                       consumer_group=consumer_group,
                       psm=psm,
                       owner=owner,
                       team=team,
                       api_version=api_version,
                       prefer_service=prefer_service,
                       ignore_dc_check=ignore_dc_check)

    tmp_consumer = proxy.get_kafka_consumer()
    result = proxy.offsets_for_times(times_string)

    for topic_partition, offset_and_timestamp in result.items():
        if offset_and_timestamp is not None:
            tmp_consumer.seek(topic_partition, offset_and_timestamp[0])

    tmp_consumer.update_positions()
    tmp_consumer.commit()
    tmp_consumer.close()


def process(proxy, nn):
    while True:
        msgs = proxy.fetch_msgs()
        if not msgs:
            continue

        for msg in msgs:
            try:
                pos = msg.find(", ", 0)
                if pos == -1:
                    logging.warn("No time field in message")
                    continue

                t = msg[:pos]
                tpos = t.find(": ", 0)
                if tpos == -1:
                    logging.warn("No time field in message")
                    continue
                time = long(t[tpos + 2:])

                msg = msg[pos + 2:]
                pos = msg.find(", ", 0)
                if pos == -1:
                    logging.warn("No nameservice field in message1")
                    logging.warn(msg)
                    continue

                n = msg[:pos]
                npos = n.find(": ", 0)
                if npos == -1:
                    logging.warn("No nameservice field in message2")
                    logging.warn(msg)
                    continue
                nameservice = n[npos + 2:]

                msg = msg[pos + 2:]
                m = json.loads(msg)
                if nn and nameservice != nn:
                    continue
                m["nameservice"] = nameservice
                m["op_time"] = time
                m['time_str'] = datetime.fromtimestamp(time/(int(1e9))).isoformat() + '.' + str(time%(int(1e9)))
                logging.warn(m)
            except Exception, e:
                logging.exception(e)
                logging.warn(msg)


def Tail(thread_id, cluster_name, topic, consumer_group, psm, owner, team, api_version, prefer_service, ignore_dc_check, nn):
    proxy = KafkaProxy(cluster_name=cluster_name,
                       topic=topic,
                       consumer_group=consumer_group,
                       psm=psm,
                       owner=owner,
                       team=team,
                       api_version=api_version,
                       prefer_service=prefer_service,
                       ignore_dc_check=ignore_dc_check,
                       partitions=[thread_id])
    process(proxy, nn)


def _main(region, nn, times_string, psm, owner, team, consumer_group):
    api_version = (0, 10, 1)
    ps = 'dcleader'
    if region == 'cn':
        cluster_name = 'bmq_data'
    else:
        cluster_name = 'kafka_main_aws'
    topic = 'dancenn_audit_log'
    reset_offset(times_string=times_string,
                 cluster_name=cluster_name,
                 topic=topic,
                 consumer_group=consumer_group,
                 psm=psm,
                 owner=owner,
                 team=team,
                 api_version=api_version,
                 prefer_service=ps,
                 ignore_dc_check=True)

    processes = []
    for i in range(200):
        p = multiprocessing.Process(target=Tail, args = (i, cluster_name, topic, consumer_group, psm, owner, team, api_version, ps, True, nn))
        p.start()
        processes.append(p)

    for p in processes:
        p.join()


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    parser = parse_cmd()
    args = parser.parse_args()
    _main(args.region, args.nn, args.times_string, args.psm, args.owner, args.team, args.consumer_group)
