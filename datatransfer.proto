/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * These .proto interfaces are private and stable.
 * Please see http://wiki.apache.org/hadoop/Compatibility
 * for what changes are allowed for a *stable* .proto interface.
 */

// This file contains protocol buffers that are used to transfer data
// to and from the datanode, as well as between datanodes.
syntax = "proto2";

option java_package = "com.volcengine.cloudfs.proto";
option java_outer_classname = "DataTransferProtos";
option java_generate_equals_and_hash = true;
option cc_generic_services = true;
package cloudfs;

import "Security.proto";
import "hdfs.proto";
import "RpcHeader.proto";

message DataTransferEncryptorMessageProto {
  enum DataTransferEncryptorStatus {
    SUCCESS = 0;
    ERROR_UNKNOWN_KEY = 1;
    ERROR = 2;
  }
  required DataTransferEncryptorStatus status = 1;
  optional bytes payload = 2;
  optional string message = 3;
  repeated CipherOptionProto cipherOption = 4;
}

message BaseHeaderProto {
  enum IOPriorityClass {
    BEST_EFFORT = 0;
    REALTIME = 1;
    IDLE = 2;
  }
  required ExtendedBlockProto block = 1;
  optional TokenProto token = 2;
  optional DataTransferTraceInfoProto traceInfo = 3;
  optional IOPriorityClass ioprioClass = 4 [default = BEST_EFFORT];
  optional int32 ioprioLevel = 5 [default = 4];
  repeated TraceBaggageProto baggages = 6;
  // source location_tag. Used to compute cross-az thpt by DN
  optional LocationTag srcLocation = 10;
}

message DataTransferTraceInfoProto {
  required uint64 traceId = 1;
  required uint64 parentId = 2;
}

message ClientOperationHeaderProto {
  required BaseHeaderProto baseHeader = 1;
  required string clientName = 2;
  optional IOPriority iopriority = 3;
  // 10506 means 1.5.6
  optional uint64 clientVersion = 10000 [default = 0];
}

message OpReadBlockProto {
  required ClientOperationHeaderProto header = 1;
  required uint64 offset = 2;
  required uint64 len = 3;
  optional bool sendChecksums = 4 [default = true];
  optional CachingStrategyProto cachingStrategy = 5;
  optional bool connectionKeepAlive = 6;
  optional bool needRspWhenKeepAlive = 7;
  optional uint32 keepAliveTimeMs = 8;
  optional bool forceRead = 1000 [default = false];
}

//for connect reuse
enum StreamTypeV2 {
  CONTROL_HEARTBEAT = 0;
  CONTROL_CLOSE = 1;
  CONTROL_WRITE_REQ = 10;
  CONTROL_WRITE_RESP = 11;
  CONTROL_WRITE_ACK = 12;
  CONTROL_READ_REQ = 20;
  CONTROL_READ_RESP = 21;
  DATA_WRITE = 200;
  DATA_READ = 201;
}

message ControlHeader{
  required ExtendedBlockProto block = 1;
  optional TokenProto token = 2;
  repeated TraceBaggageProto baggages = 3;
  required string streamId = 4; // uuid
  required string clientName = 5;
}

message StatusV2{
  required uint32 httpCode = 1;
  optional string message = 2;
}


message OpWriteBlockProtoV2 {
  required ControlHeader header = 1;
  repeated DatanodeInfoProto targets = 2;
  optional DatanodeInfoProto source = 3;
  enum BlockConstructionStage {
    PIPELINE_SETUP_APPEND = 0;
    PIPELINE_SETUP_APPEND_RECOVERY = 1;
    DATA_STREAMING = 2;
    PIPELINE_SETUP_STREAMING_RECOVERY = 3;
    PIPELINE_CLOSE = 4;
    PIPELINE_CLOSE_RECOVERY = 5;
    PIPELINE_SETUP_CREATE = 6;
    TRANSFER_RBW = 7;
    TRANSFER_FINALIZED = 8;
  }
  required BlockConstructionStage stage = 4;
  required uint32 pipelineSize = 5;
  required uint64 minBytesRcvd = 6;
  required uint64 maxBytesRcvd = 7;
  required uint64 latestGenerationStamp = 8;
  required ChecksumProto requestedChecksum = 9;
  optional StorageTypeProto storageType = 11 [default = DISK];
  repeated StorageTypeProto targetStorageTypes = 12;
}


message OpReadBlockProtoV2 {
  required ControlHeader header = 1;
  required uint64 offset = 2;
  required uint64 len = 3;
  optional bool sendChecksums = 4 [default = true];
  optional bool forceRead = 1000 [default = false];
}


message OpReadBlockViaUpstreamProto {
  required ClientOperationHeaderProto header = 1;
  required uint64 offset = 2;
  required uint64 len = 3;
  optional bool sendChecksums = 4 [default = true];
  optional CachingStrategyProto cachingStrategy = 5;
  required DatanodeInfoProto upstream = 6;
  required StorageTypeProto upstreamStorageType = 7;

  required bool save = 8;
  optional StorageTypeProto saveStorageType = 9 [default = DISK];
}

message OpRandomReadProto {
  required ClientOperationHeaderProto header = 1;
  optional bool sendChecksums = 2 [default = true];
  optional CachingStrategyProto cachingStrategy = 3;
  repeated RangeProto range = 4;
}

message RangeProto {
  required uint64 offset = 1;
  required uint64 length = 2;
}

message OpWriteBlockProto {
  required ClientOperationHeaderProto header = 1;
  repeated DatanodeInfoProto targets = 2;
  optional DatanodeInfoProto source = 3;
  enum BlockConstructionStage {
    PIPELINE_SETUP_APPEND = 0;
    // pipeline set up for failed PIPELINE_SETUP_APPEND recovery
    PIPELINE_SETUP_APPEND_RECOVERY = 1;
    // data streaming
    DATA_STREAMING = 2;
    // pipeline setup for failed data streaming recovery
    PIPELINE_SETUP_STREAMING_RECOVERY = 3;
    // close the block and pipeline
    PIPELINE_CLOSE = 4;
    // Recover a failed PIPELINE_CLOSE
    PIPELINE_CLOSE_RECOVERY = 5;
    // pipeline set up for block creation
    PIPELINE_SETUP_CREATE = 6;
    // transfer RBW for adding datanodes
    TRANSFER_RBW = 7;
    // transfer Finalized for adding datanodes
    TRANSFER_FINALIZED = 8;
  }
  required BlockConstructionStage stage = 4;
  required uint32 pipelineSize = 5;
  required uint64 minBytesRcvd = 6;
  required uint64 maxBytesRcvd = 7;
  required uint64 latestGenerationStamp = 8;

  /**
   * The requested checksum mechanism for this block write.
   */
  required ChecksumProto requestedChecksum = 9;
  optional CachingStrategyProto cachingStrategy = 10;
  optional StorageTypeProto storageType = 11 [default = DISK];
  repeated StorageTypeProto targetStorageTypes = 12;

  /**
   * Hint to the DataNode that the block can be allocated on transient
   * storage i.e. memory and written to disk lazily. The DataNode is free
   * to ignore this hint.
   */
  optional bool allowLazyPersist = 13 [default = false];

  optional string delHint = 14;

  optional uint32 socketWriteTimeoutHint = 15 [default = 0];
  optional uint32 socketReadTimeoutHint = 17 [default = 0];

  optional uint32 residentTime = 16;
  // Enable check for packet or not:
  //   1. Do crc for packetLen/headerLen/header
  //   2. Append magic number after packet
  optional bool enablePacketCheck = 1000 [default = false];
}

message OpTransferBlockProto {
  required ClientOperationHeaderProto header = 1;
  repeated DatanodeInfoProto targets = 2;
  repeated StorageTypeProto targetStorageTypes = 3;
  optional uint32 residentTime = 4;
}

message OpReplaceBlockProto {
  required BaseHeaderProto header = 1;
  required string delHint = 2;
  required DatanodeInfoProto source = 3;
  optional StorageTypeProto storageType = 4 [default = DISK];
}

message OpCopyBlockProto {
  required BaseHeaderProto header = 1;
  optional bool ignoreQuota = 2 [default = false];
}

message OpBlockChecksumProto {
  required BaseHeaderProto header = 1;
}

/**
 * An ID uniquely identifying a shared memory segment.
 */
message ShortCircuitShmIdProto {
  required int64 hi = 1;
  required int64 lo = 2;
}

/**
 * An ID uniquely identifying a slot within a shared memory segment.
 */
message ShortCircuitShmSlotProto {
  required ShortCircuitShmIdProto shmId = 1;
  required int32 slotIdx = 2;
}

message OpRequestShortCircuitAccessProto {
  required BaseHeaderProto header = 1;

  /** In order to get short-circuit access to block data, clients must set this
   * to the highest version of the block data that they can understand.
   * Currently 1 is the only version, but more versions may exist in the future
   * if the on-disk format changes.
   */
  required uint32 maxVersion = 2;

  /**
   * The shared memory slot to use, if we are using one.
   */
  optional ShortCircuitShmSlotProto slotId = 3;

  /**
   * True if the client supports verifying that the file descriptor has been
   * sent successfully.
   */
  optional bool supportsReceiptVerification = 4 [default = false];
}

message ReleaseShortCircuitAccessRequestProto {
  required ShortCircuitShmSlotProto slotId = 1;
  optional DataTransferTraceInfoProto traceInfo = 2;
}

message ReleaseShortCircuitAccessResponseProto {
  required Status status = 1;
  optional string error = 2;
}

message ShortCircuitShmRequestProto {
  // The name of the client requesting the shared memory segment.  This is
  // purely for logging / debugging purposes.
  required string clientName = 1;
  optional DataTransferTraceInfoProto traceInfo = 2;
}

message ShortCircuitShmResponseProto {
  required Status status = 1;
  optional string error = 2;
  optional ShortCircuitShmIdProto id = 3;
}

message PacketHeaderProto {
  // All fields must be fixed-length!
  required sfixed64 offsetInBlock = 1;
  required sfixed64 seqno = 2;
  required bool lastPacketInBlock = 3;
  required sfixed32 dataLen = 4;
  optional bool syncBlock = 5 [default = false];

  // Enable check for packet or not:
  //   1. Do crc for packetLen/headerLen/header
  //   2. Append magic number after packet
  optional bool enablePacketCheck = 1000 [default = false];
  // crc value
  optional sfixed64 crc = 1001 [default = 0];
  // Only used for TOS mode to indicate that the data
  // in the buffer has been cleared.
  optional bool bufferCleared = 1002 [default = false];
}

// used to connect reuse
message PacketHeaderProtoV2 {
  // All fields must be fixed-length!
  required string streamId = 1; // uuid
  required sfixed64 offsetInBlock = 2;
  required sfixed64 seqno = 3;
  required bool lastPacketInBlock = 4;
  required sfixed32 dataLen = 5;
  required sfixed32 crcLen = 6;
  optional bool syncBlock = 7 [default = false];
}

// Used to calculate crc for packet header
message PacketHeaderCRCHelperProto {
  required PacketHeaderProto header = 1;
  required int32 packetLen = 2;
  required int32 headerLen = 3;
}

enum ShortCircuitFdResponse {
  DO_NOT_USE_RECEIPT_VERIFICATION = 0;
  USE_RECEIPT_VERIFICATION = 1;
}

message PipelineAckProto {
  required sint64 seqno = 1;
  repeated Status status = 2;
  optional uint64 downstreamAckTimeNanos = 3 [default = 0];
  repeated uint64 perPeerAckTimeNanos = 4;  // n copies
  repeated uint64 perPeerRecvPktNanos = 5;  // n copies
  repeated uint64 perPeerWritePktNanos = 6;  // n copies
  repeated uint64 mirrorPktNanos = 7;  // n copies, last one is 0
}

message PipelineAckProtoV2 {
  required string streamId = 1;
  required sint64 seqno = 2;
  repeated StatusV2 status = 3;
  optional uint64 downstreamAckTimeNanos = 4 [default = 0];
  repeated uint64 perPeerAckTimeNanos = 5;
}

/**
 * Sent as part of the BlockOpResponseProto
 * for READ_BLOCK and COPY_BLOCK operations.
 */
message ReadOpChecksumInfoProto {
  required ChecksumProto checksum = 1;

  /**
   * The offset into the block at which the first packet
   * will start. This is necessary since reads will align
   * backwards to a checksum chunk boundary.
   */
  required uint64 chunkOffset = 2;
}

message BlockOpResponseProto {
  required Status status = 1;

  optional string firstBadLink = 2;
  optional OpBlockChecksumResponseProto checksumResponse = 3;
  optional ReadOpChecksumInfoProto readOpChecksumInfo = 4;

  /** explanatory text which may be useful to log on the client side */
  optional string message = 5;

  /** If the server chooses to agree to the request of a client for
   * short-circuit access, it will send a response message with the relevant
   * file descriptors attached.
   *
   * In the body of the message, this version number will be set to the
   * specific version number of the block data that the client is about to
   * read.
   */
  optional uint32 shortCircuitAccessVersion = 6;
}


message BlockOpResponseProtoV2 {
  // reponse from datanode in connection reuse
  required string streamId = 1;
  required StatusV2 status = 2;

  optional string firstBadLink = 3;
  optional OpBlockChecksumResponseProto checksumResponse = 4;
  optional ReadOpChecksumInfoProto readOpChecksumInfo = 5;
}

/**
 * Message sent from the client to the DN after reading the entire
 * read request.
 */
message ClientReadStatusProto {
  required Status status = 1;
}

message DNTransferAckProto {
  required Status status = 1;
}

message OpBlockChecksumResponseProto {
  required uint32 bytesPerCrc = 1;
  required uint64 crcPerBlock = 2;
  required bytes md5 = 3;
  optional ChecksumTypeProto crcType = 4;
}

message OpFastCopyBlockProto {
  required ClientOperationHeaderProto header = 1;
  repeated DatanodeInfoProto targets = 2;
  repeated StorageTypeProto targetStorageTypes = 3;
  optional string targetBPId = 4;
}

message OpCopyBlockAcrossFederationProto {
  required ClientOperationHeaderProto header = 1;
  required ExtendedBlockProto dstBlk = 2;
  required DatanodeInfoProto target = 3;
  required StorageTypeProto targetStorageType = 4;
}
