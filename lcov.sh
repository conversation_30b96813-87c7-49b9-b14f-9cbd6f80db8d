#!/bin/bash

set -e
set +x

SCRIPTROOT=$(cd $(dirname ${BASH_SOURCE:-$0});pwd)

if [ ! -d "$SCRIPTROOT/builds" ] || [ ! -d "$SCRIPTROOT/Test" ]; then
  echo "Please run Test build and ut before generating lcov."
  exit -1
fi

if ! command -v lcov &> /dev/null
then
    echo "lcov could not be found. Please use the correct docker image or install it manually: `apt-get install lcov`"
    exit
fi

LCOV_DIR=$SCRIPTROOT/builds/lcov
rm -rf $LCOV_DIR && mkdir -p $LCOV_DIR

echo "Running lcov......"
lcov -c --directory $SCRIPTROOT --output-file $LCOV_DIR/main_coverage.info

echo "Generating lcov report html......"
genhtml $LCOV_DIR/main_coverage.info --output-directory $LCOV_DIR/report

echo "Generating report tarball......"
pushd $LCOV_DIR
tar czf report.tar.gz report
popd

echo "Generate lcov report successfully. Please download it and open in browser."
echo "Report: $LCOV_DIR/report/index.html"
echo "Tarball: $LCOV_DIR/report.tar.gz"
echo "You can start a simple http server: python3 -m http.server --directory $LCOV_DIR/report"