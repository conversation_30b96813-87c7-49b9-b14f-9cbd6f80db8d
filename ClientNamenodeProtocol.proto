/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * These .proto interfaces are private and stable.
 * Please see http://wiki.apache.org/hadoop/Compatibility
 * for what changes are allowed for a *stable* .proto interface.
 */
syntax = "proto2";

option java_package = "com.volcengine.cloudfs.proto";
option java_outer_classname = "ClientNamenodeProtocolProtos";
option java_generic_services = true;
option java_generate_equals_and_hash = true;
option cc_generic_services = true;
package cloudfs;

import "Security.proto";
import "hdfs.proto";
import "acl.proto";
import "xattr.proto";
import "encryption.proto";
import "inotify.proto";
import "HAServiceProtocol.proto";
import "lifecycle.proto";

/**
 * The ClientNamenodeProtocol Service defines the interface between a client
 * (as runnign inside a MR Task) and the Namenode.
 * See org.apache.hadoop.hdfs.protocol.ClientProtocol for the javadoc
 * for each of the methods.
 * The exceptions declared in the above class also apply to this protocol.
 * Exceptions are unwrapped and thrown by the  PB libraries.
 */

enum ExpectedIoMode {
  DEFAULT_MODE = 0;
  TOS_BLOCK_EXPECTED = 1;
  DATANODE_BLOCK_EXPECTED = 2;
}

message GetBlockLocationsRequestProto {
  required string src = 1;     // file name
  required uint64 offset = 2;  // range start offset
  required uint64 length = 3;  // range length
  optional uint64 fileId = 4 [default = 0];  // default as a bogus id
  optional ExpectedIoMode expectedIoMode = 1000 [default = DATANODE_BLOCK_EXPECTED];

  optional string rdmaTag = 1003; // format: [vpc|rdma]-<cluster-id>
  repeated DatanodeInfoProto excludeNodes = 1020;
}

message GetBlockLocationsResponseProto {
  optional LocatedBlocksProto locations = 1;
}

message GetHyperBlockLocationsRequestProto {
  required string src = 1; // hyper file name
  required uint64 offset = 2;  // range start offset
  required uint64 length = 3;  // range length
}

message GetHyperBlockLocationsResponseProto {
  repeated LocatedHyperBlockProto locations = 1;
  optional uint64 stripeWidth = 2;
}

message LocatedHyperBlockProto {
  required uint32 index = 1;
  required string path = 2;
  required LocatedBlocksProto locations = 3;
}

message GetServerDefaultsRequestProto { // No parameters
}

message GetServerDefaultsResponseProto {
  required FsServerDefaultsProto serverDefaults = 1;
}

enum CreateFlagProto {
  CREATE = 0x01;    // Create a file
  OVERWRITE = 0x02; // Truncate/overwrite a file. Same as POSIX O_TRUNC
  APPEND = 0x04;    // Append to a file
  LAZY_PERSIST = 0x10; // File with reduced durability guarantees.
  ACC_ASYNC = 0x20; // Async write in ACC mode
  ACC_APPENDABLE = 0x40; // Appendable file type for ACC mode
}

message CreateRequestProto {
  required string src = 1;
  required FsPermissionProto masked = 2;
  required string clientName = 3;
  required uint32 createFlag = 4;  // bits set using CreateFlag
  required bool createParent = 5;
  required uint32 replication = 6; // Short: Only 16 bits used
  required uint64 blockSize = 7;
  repeated CryptoProtocolVersionProto cryptoProtocolVersion = 8;
  repeated XAttrProto attr = 9;

  optional bool withAddBlock = 10;
  // when set to true, NN can not decease replica-num if available DNs are
  // not enough. Instead, NN will return error in this case.
  optional bool strict_replica_num = 11 [default=false];

  repeated DatanodeInfoProto excludeNodes = 12; // only used when withAddBlock is true

  optional RpcProtocolType rpc_type = 100 [default=RPC_HDFS_MODE];

  optional ExpectedIoMode expectedIoMode = 1000 [default = DATANODE_BLOCK_EXPECTED];

  optional string rdmaTag = 1003;  // format: [vpc|rdma]-<cluster-id>
}

message CreateResponseProto {
  optional HdfsFileStatusProto fs = 1;
  optional string namenodebackend = 2;

  optional LocatedBlockProto block = 3;
}

message AppendRequestProto {
  required string src = 1;
  required string clientName = 2;
  optional bool force = 3 [ default = false ];
}

message AppendResponseProto {
  optional LocatedBlockProto block = 1;
  optional string namenodebackend = 2;
  optional LocatedBlockProto unfinalizedLastBlock = 3;
  optional HdfsFileStatusProto fs = 4;
}

message SetReplicationRequestProto {
  required string src = 1;
  required uint32 replication = 2; // Short: Only 16 bits used
  optional bool allowZeroReplica = 3 [default = false];
}

message SetReplicationResponseProto {
  required bool result = 1;
}

message SetStoragePolicyRequestProto {
  required string src = 1;
  required string policyName = 2;
}

message SetStoragePolicyResponseProto { // void response
}

message GetStoragePoliciesRequestProto { // void request
}

message GetStoragePoliciesResponseProto {
  repeated BlockStoragePolicyProto policies = 1;
}

message SetPermissionRequestProto {
  required string src = 1;
  required FsPermissionProto permission = 2;
}

message SetPermissionResponseProto { // void response
}

message SetOwnerRequestProto {
  required string src = 1;
  optional string username = 2;
  optional string groupname = 3;
}

message SetOwnerResponseProto { // void response
}

message AbandonBlockRequestProto {
  required ExtendedBlockProto b = 1;
  required string src = 2;
  required string holder = 3;
  optional uint64 fileId = 4 [default = 0];  // default to GRANDFATHER_INODE_ID
}

message AbandonBlockResponseProto { // void response
}

// Next Id: 8
message AddBlockRequestProto {
  required string src = 1;
  required string clientName = 2;
  optional ExtendedBlockProto previous = 3 [ deprecated = true ];  // use in block protocol v2 to finalize block.
  optional LocatedBlockProto located_previous = 7;
  repeated DatanodeInfoProto excludeNodes = 4;
  optional uint64 fileId = 5 [default = 0];  // default as a bogus id
  repeated string favoredNodes = 6; //the set of datanodes to use for the block

  optional RpcProtocolType rpc_type = 100 [default=RPC_HDFS_MODE];
  // when set to true, NN can not decease replica-num if available DNs are
  // not enough. Instead, NN will return error in this case.
  optional bool strict_replica_num = 101 [default=false];

  optional ExpectedIoMode expectedIoMode = 1000 [default = DATANODE_BLOCK_EXPECTED];

  optional string rdmaTag = 1003;  // format: [vpc|rdma]-<cluster-id>
}

message AddBlockResponseProto {
  required LocatedBlockProto block = 1;
  optional string namenodebackend = 2;
}

// Next Id: 7
message CommitLastBlockRequestProto {
  required string src = 1;
  required string clientName = 2;
  required BlockProto lastBlock = 3 [deprecated = true];
  optional LocatedBlockProto located_last_block = 6;
  optional uint64 fileId = 4 [default = 0];  // default to GRANDFATHER_INODE_ID
  optional uint64 fileLength = 5;
}

message CommitLastBlockResponseProto {
}

message GetAdditionalDatanodeRequestProto {
  required string src = 1;
  required ExtendedBlockProto blk = 2;
  repeated DatanodeInfoProto existings = 3;
  repeated DatanodeInfoProto excludes = 4;
  required uint32 numAdditionalNodes = 5;
  required string clientName = 6;
  repeated string existingStorageUuids = 7;
  optional uint64 fileId = 8 [default = 0];  // default to GRANDFATHER_INODE_ID
}

message GetAdditionalDatanodeResponseProto {
  required LocatedBlockProto block = 1;
}

// Next Id: 6
message CompleteRequestProto {
  required string src = 1;
  required string clientName = 2;
  optional ExtendedBlockProto last = 3;
  optional uint64 fileId = 4 [default = 0];  // default to GRANDFATHER_INODE_ID
  optional LocatedBlockProto located_last = 5;
}

message CompleteResponseProto {
  required bool result = 1;
}

message ReportBadBlocksRequestProto {
  repeated LocatedBlockProto blocks = 1;
}

message ReportBadBlocksResponseProto { // void response
}

message ConcatRequestProto {
  required string trg = 1;
  repeated string srcs = 2;
}

message ConcatResponseProto { // void response
}

message TruncateRequestProto {
  required string src = 1;
  required uint64 newLength = 2;
  required string clientName = 3;
}

message TruncateResponseProto {
  required bool result = 1;
}

message RenameRequestProto {
  required string src = 1;
  required string dst = 2;
  optional uint32 maxCount = 1000 [default = 0]; // ACC FS
  optional uint32 maxSize = 1001 [default = 0]; // ACC FS, GiB
}

message RenameResponseProto {
  required bool result = 1;
}


message Rename2RequestProto {
  required string src = 1;
  required string dst = 2;
  required bool overwriteDest = 3;
  optional uint32 maxCount = 1000 [default = 0]; // ACC FS
  optional uint32 maxSize = 1001 [default = 0]; // ACC FS, GiB
  optional uint32 maxOverwriteCount = 1002 [default = 0]; // ACC FS
}

message Rename2ResponseProto { // void response
}

message DeleteRequestProto {
  required string src = 1;
  required bool recursive = 2;
  optional uint64 fileid = 3 [default = 0];
  optional uint32 maxCount = 1000 [default = 0]; // Acc FS, Max number of files to delete
}

message DeleteResponseProto {
    required bool result = 1;
}

message MkdirsRequestProto {
  required string src = 1;
  required FsPermissionProto masked = 2;
  required bool createParent = 3;
}
message MkdirsResponseProto {
    required bool result = 1;
}

message MkdirExtendedRequestProto {
  required string src = 1;
  required FsPermissionProto masked = 2;
  required bool createParent = 3;
  optional ExpirationRuleProto expRule = 4;
  optional UfsUploadPolicyProto upload_policy = 5;
  optional SyncPolicyProto sync_policy = 6;
}
message MkdirExtendedResponseProto {
    required bool result = 1;
}

message GetListingRequestProto {
  // Hash calculation method
  enum HashType {
    NONE = 0;
    PATH_SEGMENT = 1;
    FULL_PATH = 2;
  }
  required string src = 1;
  required bytes startAfter = 2;
  required bool needLocation = 3;
  optional uint32 maxCount = 1000 [default = 0]; // ACC FS
  optional uint32 maxTimeSec = 1001 [default = 0]; // ACC FS
  optional uint32 pageSize = 1002 [default = 0];
  // rdmaTag is only used when needLocation is true.
  // format: [vpc|rdma]-<cluster-id>
  optional string rdmaTag = 1003;

  optional HashType hashType = 1004 [default = NONE];
  // For PATH_SEGMENT hash calculation method
  // record which segment of the path is used for the calculation
  optional int32 hashSegIndex = 1005 [default = -1];
  optional uint32 nnGroupCount = 1006 [default = 1];
  optional uint32 currNnIndex = 1007 [default = 0];
}

message GetListingResponseProto {
  optional DirectoryListingProto dirList = 1;
}

message GetSnapshottableDirListingRequestProto { // no input parameters
}
message GetSnapshottableDirListingResponseProto {
  optional SnapshottableDirectoryListingProto snapshottableDirList = 1;
}

message ListDirPolicyV2RequestProto {
  optional bool need_replica_policy = 1 [default = true];
  optional bool need_read_policy = 2 [default = true];
  optional bool need_upload_policy = 3 [default = true];

  optional uint64 start_after_fileid = 4;    // Start listing after this inode
  optional uint32 page_size = 5 [default = 1000];
}

message ListDirPolicyV2ResponseProto {
  message PolicyEntry {
    required string path = 1;
    required uint64 file_id = 2;

    optional ReplicaPolicyProto replica_policy = 3;
    optional ReadPolicyProto read_policy = 4;
    optional UfsUploadPolicyProto upload_policy = 5;
  }

  repeated PolicyEntry policy_list = 1;
  optional bool has_more_entries = 2 [default = false];
  optional uint64 next_after_fileid = 3;
}

message GetSnapshotDiffReportRequestProto {
  required string snapshotRoot = 1;
  required string fromSnapshot = 2;
  required string toSnapshot = 3;
}
message GetSnapshotDiffReportResponseProto {
  required SnapshotDiffReportProto diffReport = 1;
}

message RenewLeaseRequestProto {
  required string clientName = 1;
  repeated string namenodebackends = 2;
}

message RenewLeaseResponseProto { //void response
}

message RecoverLeaseRequestProto {
  required string src = 1;
  required string clientName = 2;
  optional uint64 fileId = 3;
  optional bool closeFile = 4 [default = true];
}
message RecoverLeaseResponseProto {
  required bool result = 1;
}

message CheckLeaseRequestProto {
  required string src = 1;
  required string clientName = 2;
  required uint64 fileid = 3;
}

message CheckLeaseResponseProto {
  required bool hasLease = 1;
}

message GetFsStatusRequestProto { // no input paramters
}

message GetFsStatsResponseProto {
  required uint64 capacity = 1;
  required uint64 used = 2;
  required uint64 remaining = 3;
  required uint64 under_replicated = 4;
  required uint64 corrupt_blocks = 5;
  required uint64 missing_blocks = 6;
  optional uint64 missing_repl_one_blocks = 7;

  optional uint64 inode_capacity = 8;
  optional uint64 inode_used = 9;
  optional uint64 inode_remaining = 10;

  optional uint64 inode_wait_upload = 11;
  optional uint64 inode_no_upload = 12;
  optional uint64 inode_uploading = 13;
}

enum DatanodeReportTypeProto {  // type of the datanode report
  ALL = 1;
  LIVE = 2;
  DEAD = 3;
  DECOMMISSIONING = 4;
  CUR_DC_LIVE = 5;
  DYING = 6;
}

message GetDatanodeReportRequestProto {
  required DatanodeReportTypeProto type = 1;
}

message GetDatanodeReportResponseProto {
  repeated DatanodeInfoProto di = 1;
}

message GetDatanodeStorageReportRequestProto {
  required DatanodeReportTypeProto type = 1;
}

message DatanodeStorageReportProto {
  required DatanodeInfoProto datanodeInfo = 1;
  repeated StorageReportProto storageReports = 2;
}

message GetDatanodeStorageReportResponseProto {
  repeated DatanodeStorageReportProto datanodeStorageReports = 1;
}

message GetPreferredBlockSizeRequestProto {
  required string filename = 1;
}

message GetPreferredBlockSizeResponseProto {
  required uint64 bsize = 1;
}

enum SafeModeActionProto {
  SAFEMODE_LEAVE = 1;
  SAFEMODE_ENTER = 2;
  SAFEMODE_GET = 3;
}

message SetSafeModeRequestProto {
  required SafeModeActionProto action = 1;
  optional bool checked = 2 [default = false];
}

message SetSafeModeResponseProto {
  required bool result = 1;
}

message SaveNamespaceRequestProto { // no parameters
}

message SaveNamespaceResponseProto { // void response
}

message RollEditsRequestProto { // no parameters
}

message RollEditsResponseProto { // response
  required uint64 newSegmentTxId = 1;
}

message RestoreFailedStorageRequestProto {
  required string arg = 1;
}

message RestoreFailedStorageResponseProto {
    required bool result = 1;
}

message RefreshNodesRequestProto { // no parameters
}

message RefreshNodesResponseProto { // void response
}

message FinalizeUpgradeRequestProto { // no parameters
}

message FinalizeUpgradeResponseProto { // void response
}

enum RollingUpgradeActionProto {
  QUERY = 1;
  START = 2;
  FINALIZE = 3;
}

message RollingUpgradeRequestProto {
  required RollingUpgradeActionProto action = 1;
}

message RollingUpgradeInfoProto {
  required RollingUpgradeStatusProto status = 1;
  required uint64 startTime = 2;
  required uint64 finalizeTime = 3;
  required bool createdRollbackImages = 4;
}

message RollingUpgradeResponseProto {
  optional RollingUpgradeInfoProto rollingUpgradeInfo= 1;
}

message ListCorruptFileBlocksRequestProto {
  required string path = 1;
  optional string cookie = 2;
}

message ListCorruptFileBlocksResponseProto {
  required CorruptFileBlocksProto corrupt = 1;
}

message MetaSaveRequestProto {
  required string filename = 1;
}

message MetaSaveResponseProto { // void response
}

message GetFileInfoRequestProto {
  required string src = 1;
  optional bool needLocation = 2 [default = false];
  optional bool updateTtlAtime = 3 [default = false];
  // rdmaTag is only used when needLocation is true.
  // format: [vpc|rdma]-<cluster-id>
  optional string rdmaTag = 100;
}

message GetFileInfoResponseProto {
  optional HdfsFileStatusProto fs = 1;
}

message IsFileClosedRequestProto {
  required string src = 1;
  optional uint64 fileId = 2 [default = 0];
}

message IsFileClosedResponseProto {
  required bool result = 1;
}

message CacheDirectiveInfoProto {
  optional int64 id = 1;
  optional string path = 2;
  optional uint32 replication = 3;
  optional string pool = 4;
  optional CacheDirectiveInfoExpirationProto expiration = 5;
}

message CacheDirectiveInfoExpirationProto {
  required int64 millis = 1;
  required bool isRelative = 2;
}

message CacheDirectiveStatsProto {
  required int64 bytesNeeded = 1;
  required int64 bytesCached = 2;
  required int64 filesNeeded = 3;
  required int64 filesCached = 4;
  required bool hasExpired = 5;
}

enum CacheFlagProto {
  FORCE = 0x01;    // Ignore pool resource limits
}

message AddCacheDirectiveRequestProto {
  required CacheDirectiveInfoProto info = 1;
  optional uint32 cacheFlags = 2;  // bits set using CacheFlag
}

message AddCacheDirectiveResponseProto {
  required int64 id = 1;
}

message ModifyCacheDirectiveRequestProto {
  required CacheDirectiveInfoProto info = 1;
  optional uint32 cacheFlags = 2;  // bits set using CacheFlag
}

message ModifyCacheDirectiveResponseProto {
}

message RemoveCacheDirectiveRequestProto {
  required int64 id = 1;
}

message RemoveCacheDirectiveResponseProto {
}

message ListCacheDirectivesRequestProto {
  required int64 prevId = 1;
  required CacheDirectiveInfoProto filter = 2;
}

message CacheDirectiveEntryProto {
  required CacheDirectiveInfoProto info = 1;
  required CacheDirectiveStatsProto stats = 2;
}

message ListCacheDirectivesResponseProto {
  repeated CacheDirectiveEntryProto elements = 1;
  required bool hasMore = 2;
}

message CachePoolInfoProto {
  optional string poolName = 1;
  optional string ownerName = 2;
  optional string groupName = 3;
  optional int32 mode = 4;
  optional int64 limit = 5;
  optional uint32 replication = 6;
  optional int64 maxRelativeExpiry = 7;
}

message CachePoolStatsProto {
  required int64 bytesNeeded = 1;
  required int64 bytesCached = 2;
  required int64 bytesOverlimit = 3;
  required int64 filesNeeded = 4;
  required int64 filesCached = 5;
}

message AddCachePoolRequestProto {
  required CachePoolInfoProto info = 1;
}

message AddCachePoolResponseProto { // void response
}

message ModifyCachePoolRequestProto {
  required CachePoolInfoProto info = 1;
}

message ModifyCachePoolResponseProto { // void response
}

message RemoveCachePoolRequestProto {
  required string poolName = 1;
}

message RemoveCachePoolResponseProto { // void response
}

message ListCachePoolsRequestProto {
  required string prevPoolName = 1;
}

message ListCachePoolsResponseProto {
  repeated CachePoolEntryProto entries = 1;
  required bool hasMore = 2;
}

message CachePoolEntryProto {
  required CachePoolInfoProto info = 1;
  required CachePoolStatsProto stats = 2;
}

message GetFileLinkInfoRequestProto {
  required string src = 1;
}

message GetFileLinkInfoResponseProto {
  optional HdfsFileStatusProto fs = 1;
}

message GetContentSummaryRequestProto {
  required string path = 1;
}

message GetContentSummaryResponseProto {
  required ContentSummaryProto summary = 1;
}

message SetQuotaRequestProto {
  required string path = 1;
  required uint64 namespaceQuota = 2;
  required uint64 diskspaceQuota = 3;
}

message SetQuotaResponseProto { // void response
}

message FsyncRequestProto {
  required string src = 1;
  required string client = 2;
  optional sint64 lastBlockLength = 3 [default = -1];
  optional uint64 fileId = 4 [default = 0];  // default to GRANDFATHER_INODE_ID
  optional uint64 lastBlockId = 5 [default = 0];
}

message FsyncResponseProto { // void response
}

message SetTimesRequestProto {
  required string src = 1;
  required uint64 mtime = 2;
  required uint64 atime = 3;
}

message SetTimesResponseProto { // void response
}

message CreateSymlinkRequestProto {
  required string target = 1;
  required string link = 2;
  required FsPermissionProto dirPerm = 3;
  required bool createParent = 4;
}

message CreateSymlinkResponseProto { // void response
}

message GetLinkTargetRequestProto {
  required string path = 1;
}
message GetLinkTargetResponseProto {
  optional string targetPath = 1;
}

message UpdateBlockForPipelineRequestProto {
  required ExtendedBlockProto block = 1;
  required string clientName = 2;
}

message UpdateBlockForPipelineResponseProto {
  required LocatedBlockProto block = 1;
}

message UpdatePipelineRequestProto {
  required string clientName = 1;
  required ExtendedBlockProto oldBlock = 2;
  required ExtendedBlockProto newBlock = 3;
  repeated DatanodeIDProto newNodes = 4;
  repeated string storageIDs = 5;
}

message UpdatePipelineResponseProto { // void response
}

message SetBalancerBandwidthRequestProto {
  required int64 bandwidth = 1;
}

message SetBalancerBandwidthResponseProto { // void response
}

message GetDataEncryptionKeyRequestProto { // no parameters
}

message GetDataEncryptionKeyResponseProto {
  optional DataEncryptionKeyProto dataEncryptionKey = 1;
}

message CreateSnapshotRequestProto {
  required string snapshotRoot = 1;
  optional string snapshotName = 2;
}

message CreateSnapshotResponseProto {
  required string snapshotPath = 1;
}

message RenameSnapshotRequestProto {
  required string snapshotRoot = 1;
  required string snapshotOldName = 2;
  required string snapshotNewName = 3;
}

message RenameSnapshotResponseProto { // void response
}

message AllowSnapshotRequestProto {
  required string snapshotRoot = 1;
}

message AllowSnapshotResponseProto {
}

message DisallowSnapshotRequestProto {
  required string snapshotRoot = 1;
}

message DisallowSnapshotResponseProto {
}

message DeleteSnapshotRequestProto {
  required string snapshotRoot = 1;
  required string snapshotName = 2;
}

message DeleteSnapshotResponseProto { // void response
}

message CheckACPAccessWithInfSecIdentifierRequestProto {
  required string path = 1;
  required AclEntryProto.FsActionProto mode = 2;
  required string infSecIdentityUser = 3;
  required string infSecIdentityPSM = 4;
}

message CheckACPAccessWithInfSecIdentifierResponseProto { // void response
}

message CheckACPAccessRequestProto {
  required string path = 1;
  required AclEntryProto.FsActionProto mode = 2;
}

message CheckACPAccessResponseProto { // void response
}

message CheckAccessRequestProto {
  required string path = 1;
  required AclEntryProto.FsActionProto mode = 2;
}

message CheckAccessResponseProto { // void response
}

message GetCurrentEditLogTxidRequestProto {
}

message GetCurrentEditLogTxidResponseProto {
  required int64 txid = 1;
}

message GetEditsFromTxidRequestProto {
  required int64 txid = 1;
}

message GetEditsFromTxidResponseProto {
  required EventsListProto eventsList = 1;
}

message GetAccessCounterValuesRequestProto {
  required string path = 1;
}

message DataCenterProto {
  required string name = 1;
  required int32 id = 2;
}

message GetAccessCounterValuesResponseProto {
  repeated DataCenterProto dc = 1;
  repeated double value = 2;
}

message IncreaseAccessCounterRequestProto {
  required string path = 1;
  required DataCenterProto dc = 2;
  required int64 step = 3;
}

message IncreaseAccessCounterResponseProto {
}

// Deprecated: remove API
message SetReplicaPolicyRequestProto {
  required string path = 1;
  required int32 id = 2;
  optional string dc = 3;

  optional ReplicaPolicyProto policy = 4;
}

// Deprecated: remove API
message SetReplicaPolicyResponseProto {
}

// Deprecated: remove API
message GetReplicaPolicyRequestProto {
  required string path = 1;
}

// Deprecated: remove API
message GetReplicaPolicyResponseProto {
  required int32 id = 1;
  optional string dc = 2;
}

// Deprecated: remove API
message GetDistributedRequestProto {
}

// Deprecated: remove API
message GetDistributedResponseProto {
  repeated string paths = 1;
  repeated int32 ids = 2;
  repeated string dcs = 3;
}

// todo(xiongmu): replace old ReplicaPolicy
message ReplicaPolicyProto {
  optional bool distributed = 1 [default = false];
  repeated string dc = 2;
  optional bool randomMajority = 3 [default = false];

  optional int32 local_switch_target = 4 [default = 0];
  optional int32 other_switch_target = 5 [default = 0];
}

message ReadPolicyProto {
  enum ReadSwitchPolicy{
    PREFER_CACHED = 1;
    PREFER_LOCAL = 2;
  }
  optional bool localDcOnly = 1 [default = false];
  repeated string blacklistDc = 2;

  optional ReadSwitchPolicy read_switch_policy = 3;
}

message UfsUploadPolicyProto {
  // start upload after file close [this] ms, -1 means never upload, 0 means immediately.
  optional int32 upload_interval_ms = 1 [default = 0];
  optional bool add_to_local_sync_policy = 2 [default = false];
}

// Deprecated: remove API
message SetReadPolicyRequestProto {
  required string path = 1;
  optional ReadPolicyProto policy = 2;
}

// Deprecated: remove API
message SetReadPolicyResponseProto {
}

// Deprecated: remove API
message GetReadPolicyRequestProto {
  required string path = 1;
}

// Deprecated: remove API
message GetReadPolicyResponseProto {
  required ReadPolicyProto policy = 1;
}

// Deprecated: remove API
message ListReadPoliciesRequestProto {
}

// Deprecated: remove API
message ListReadPoliciesResponseProto {
  repeated string paths = 1;
  repeated ReadPolicyProto policies = 2;
}

message SetDirPolicyRequestProto {
  required string path = 1;

  optional ReplicaPolicyProto replica_policy = 2;
  optional ReadPolicyProto read_policy = 3;
  optional UfsUploadPolicyProto upload_policy = 4;
}

message SetDirPolicyResponseProto {
}

enum SyncPolicyProto {
  kSyncPolicySync = 1;
  kSyncPolicyLocal = 2;
  kSyncPolicySemiSync = 3;
}

message SetSyncPolicyRequestProto {
  required string path = 1;
  required SyncPolicyProto sync_policy = 2;
}

message SetSyncPolicyResponseProto {
}

message RemoveDirPolicyRequestProto {
  required string path = 1;

  optional bool remove_replica_policy = 2 [default = false];
  optional bool remove_read_policy = 3 [default = false];
  optional bool remove_upload_policy = 4 [default = false];
}

message RemoveDirPolicyResponseProto {
  optional ReplicaPolicyProto removed_replica_policy = 1;
  optional ReadPolicyProto removed_read_policy = 2;
  optional UfsUploadPolicyProto removed_upload_policy = 3;
}

message GetDirPolicyRequestProto {
  required string path = 1;

  optional bool need_replica_policy = 2 [default = true];
  optional bool need_read_policy = 3 [default = true];
  optional bool need_upload_policy = 4 [default = true];
}

message GetDirPolicyResponseProto {
  optional ReplicaPolicyProto replica_policy = 1;
  optional ReadPolicyProto read_policy = 2;
  optional UfsUploadPolicyProto upload_policy = 4;
}

message ListDirPolicyRequestProto {
  optional bool need_replica_policy = 1 [default = true];
  optional bool need_read_policy = 2 [default = true];
  optional bool need_upload_policy = 3 [default = true];
}

message ListDirPolicyResponseProto {
  message PolicyEntry {
    required string path = 1;

    optional ReplicaPolicyProto replica_policy = 2;
    optional ReadPolicyProto read_policy = 3;
    optional UfsUploadPolicyProto upload_policy = 4;
  }

  repeated PolicyEntry policy_list = 1;
}

message ListSyncPolicyRequestProto {
  optional bool need_local_sync_policy = 1 [default = true];
}

message ListSyncPolicyResponseProto {
  message PolicyEntry {
    required string path = 1;
    optional SyncPolicyProto sync_policy = 2;
  }

  repeated PolicyEntry policy_list = 1;
}

message ListSyncPolicyV2RequestProto {
  optional bool need_local_sync_policy = 1 [default = true];

  optional uint64 start_after_fileid = 2;    // Start listing after this inode
  optional uint32 page_size = 3 [default = 1000];
}

message ListSyncPolicyV2ResponseProto {
  message PolicyEntry {
    required string path = 1;
    required uint64 file_id = 2;
    optional SyncPolicyProto sync_policy = 3;
  }

  repeated PolicyEntry policy_list = 1;
  optional bool has_more_entries = 2 [default = false];
  optional uint64 next_after_fileid = 3;
}

message RecyclePolicyProto {
  enum Reason {
    DELETE = 1;
    CREATE_OVERWRITE = 2;
    RENAME2_OVERWRITE = 3;
  }
  optional uint64 recycleTime = 1 [default = 0];
  optional uint64 movetoTimestamp = 2;
  optional uint64 cleanupTimestamp = 3;
  optional Reason reason = 4;
  optional string old_path = 5;
}

enum HyperFileMode {
  PIPELINE_MANUAL = 0;
  PIPELINE_AUTOMATIC = 1;
}

/**
 * StripeWidth means the strip width of per HyperBlock which unit is byte.
 * If StripeWidth is 1MB, then client will write 1MB data to target HyperBlock then switch to next HyperBlock.
 * If a HyperFile has 3 HyperBlocks, then client will write data to HyperBlocks in a round-robin manner.
 * The StripeWidth in HyperFile level is 3 * 1MB = 3MB
 */
message HyperCacheMetaProto {
  required HyperFileMode mode = 1;
  repeated string hyperblock = 2;
  required uint64 stripeWidth = 3;
}

message AddCompleteBlocksAndCloseFileRequestProto {
  required string src = 1;
  required string clientName = 2;
  optional uint64 fileId = 3;
  repeated uint64 blockSizes = 4;
}

message AddCompleteBlocksAndCloseFileResponseProto {
  repeated LocatedBlockProto locatedBlocks = 1;
}

/**
 * void request
 */
message GetBlockKeysRequestProto {
}

/**
 * keys - Information about block keys at the active namenode
 */
message GetBlockKeysResponseProto {
  optional ExportedBlockKeysProto keys = 1;
}

message GetINodeStatRequestProto {
  repeated uint64 inodeIds = 1;
}

message GetINodeStatResponseProto {
  message INodeStat {
    required uint64 inodeId = 1;
    required uint64 inodeNum = 2;
    required uint64 fileNum = 3;
    required uint64 dirNum = 4;
    required uint64 blockNum = 5;
    required uint64 dataSize = 6;
  }
  repeated INodeStat inodeStats = 1;
  repeated uint64 missingINodeIds = 2;
  required int64 updateTsInSec = 4;
  required int64 snapshotTxId = 3;
}

message SetLifecyclePolicyRequestProto {
  required string path = 1;
  required LifecyclePolicyProto lifecyclePolicy = 2;
}

message SetLifecyclePolicyResponseProto {
}

message UnsetLifecyclePolicyRequestProto {
  required string path = 1;
}

message UnsetLifecyclePolicyResponseProto {
}

message GetLifecyclePolicyRequestProto {
  required string path = 1;
}

message GetLifecyclePolicyResponseProto {
  required LifecyclePolicyProto lifecyclePolicy = 1;
}

message MsyncRequestProto {
}

message MsyncResponseProto {
}


message HAServiceStateRequestProto {
}

message HAServiceStateResponseProto {
  required HAServiceStateProto state = 1;
}

message AcquireQuotaRequest {
  enum QuotaType {
    Get = 0;
    Put = 1;
    GetRate = 3;
    PutRate = 4;
  }
  required string    fs = 1;
  required string    action = 2;
  required uint64    acquire = 3;
  required QuotaType quotaType = 4;
}

message BatchAcquireQuotaRequest {
  repeated AcquireQuotaRequest requests = 1;
}

message AcquireQuotaResponse {
  enum AcquireStatus {
    Ok = 0;
    Insufficient = 1;
    OutOfQuota = 2;
  }
  required uint64        acquired = 1;
  required AcquireStatus status   = 2;
  required uint64        acquiredAt = 3;
}

message BatchAcquireQuotaResponse {
  repeated AcquireQuotaResponse responses = 1;
}

enum RouteDest {
  DATAMGMT = 0;
  NAMENODE = 1;
}

message LoadRequestProto {
  required string src = 1;
  required bool recursive = 2;
  required bool metadata = 3;
  required bool data = 4;
  optional uint32 replicaNum = 5 [default = 1];;
  optional DataCenterProto dc = 6;
  optional RouteDest routeDest = 7 [default = DATAMGMT];
}

message LoadResponseProto {
  required string jobId = 1;
  required bool done = 2;
}

message FreeRequestProto {
  required string src = 1;
  required bool recursive = 2;
  required bool metadata = 3;
  optional RouteDest routeDest = 4 [default = DATAMGMT];
}

message FreeResponseProto {
  required string jobId = 1;
  required bool done = 2;
}

message PinRequestProto {
  required string src = 1;
  optional int64 ttl = 2 [ default = -1 ]; // -1 stands for no ttl
  optional bool recursive = 3 [ default = false ];
  optional bool unpin = 4 [ default = false ]; // ttl must be -1 when unpin
}

message PinResponseProto {
  required bool result = 1;
}

message LookupJobRequestProto {
  required string jobId = 1;
  optional RouteDest routeDest = 2 [default = DATAMGMT];
}

message LookupJobResponseProto {
  enum JobStatus {
    Accepted = 0;
    Submitted = 1;
    Running = 2;
    Finished = 3;
    Cancelled = 4;
    Failed = 5;
  }
  repeated JobStateProto jobState = 1;
  optional JobStatus status = 2;
  optional string msg = 3;
}

message CancelJobRequestProto {
  required string jobId = 1;
  optional RouteDest routeDest = 2 [default = DATAMGMT];
}

message CancelJobResponseProto {
}

enum ClientMetricsType {
  COUNTER = 0;
  METER = 1;
  STORE = 2;
  TIMER = 3;
  TSSTORE = 4;
}

message ClientMetricsTag {
  required string key = 1;
  required string value = 2;
}

message ClientMetricsEntry {
  required ClientMetricsType emitType = 1;
  required string name = 2;
  required double value = 3;
  repeated ClientMetricsTag tags = 4;
}

message ClientMetricsRequestProto {
  required int64 metricTimeSec = 1;
  repeated ClientMetricsEntry metricEntries = 2;
  repeated ClientMetricsTag commonTags = 3;
}

message ClientMetricsResponseProto { // void response
}

message BatchCreateFileEntry {
  required string src = 1;
  required FsPermissionProto masked = 2;
  // required string clientName = 3;
  required uint32 createFlag = 4;  // bits set using CreateFlag
  // required bool createParent = 5;
  required uint32 replication = 6; // Short: Only 16 bits used
  required uint64 blockSize = 7;
  repeated CryptoProtocolVersionProto cryptoProtocolVersion = 8;
  repeated XAttrProto attr = 9;
  // when set to true, NN can not decease replica-num if available DNs are
  // not enough. Instead, NN will return error in this case.
  optional bool strict_replica_num = 10 [default=false];
}

message BatchCreateFileRequestProto {
  enum OverwriteFlag {
    NO_OVERWRITE = 0;
    NO_OVERWRITE_UC = 1;
    OVERWRITE_ALL = 2;
  }

  repeated BatchCreateFileEntry files = 1;
  required string clientName = 2;

  optional bool withAddBlock = 10;
  optional OverwriteFlag overwrite_flag = 11 [default=OVERWRITE_ALL];
  // if true, all success or nothing
  // if false, record failed path in response and continue
  optional bool atomic_flag = 12 [default = true];
  repeated DatanodeInfoProto excludeNodes = 13; // only used when withAddBlock is true

  optional RpcProtocolType rpc_type = 100 [default=RPC_HDFS_MODE];
  optional ExpectedIoMode expectedIoMode = 1000 [default = DATANODE_BLOCK_EXPECTED];
  optional string rdmaTag = 1003;  // format: [vpc|rdma]-<cluster-id>
}

message BatchCreateFileResponseEntry {
  optional HdfsFileStatusProto fs = 1;

  optional LocatedBlockProto block = 3;

  // each file's original index in request
  optional uint32 original_index = 4;
}

// exception if any inode is existed
message BatchCreateFileResponseProto {
  message FailedMsg {
    optional string path = 1;
    optional string reason = 2;

    // each file's original index in request
    optional uint32 original_index = 3;
  }

  repeated BatchCreateFileResponseEntry files = 1;

  optional string namenodebackend = 2;

  repeated FailedMsg failed_file = 3;

  optional bool result = 4;

  repeated string namenodebackends = 5;
}

// Next Id: 5
message BatchCompleteFileEntry {
  required string src = 1;
  optional ExtendedBlockProto lastBlock = 2 [ deprecated = true ]; // Deprecate in 5.12.2
  optional LocatedBlockProto locatedLastBlock = 4;
  optional uint64 fileId = 3 [default = 0];  // default to GRANDFATHER_INODE_ID

  repeated XAttrProto add_attr = 9;
  repeated XAttrProto del_attr = 10;
}

message BatchCompleteFileConcatEntry {
  repeated BatchCompleteFileEntry srcs = 1;
  required BatchCompleteFileEntry target = 2;
}

message BatchCompleteFileRequestProto {
  // complete file
  repeated BatchCompleteFileEntry singleFile = 1;

  // complete multi files and concat them to the target file
  repeated BatchCompleteFileConcatEntry concatFile = 2;

  required string clientName = 3;

  // if true, all success or nothing
  // if false, record failed path in response and continue
  optional bool atomic_flag = 12 [default = true];

  optional RpcProtocolType rpc_type = 100 [default=RPC_HDFS_MODE];
}

// exception if any inode not found
message BatchCompleteFileResponseProto {
  message FailedMsg {
    optional string path = 1;
    optional string reason = 2;

    // each file's original index in request
    optional uint32 original_index = 3;
  }

  required bool result = 1;

  repeated FailedMsg failed_single_file = 2;

  repeated FailedMsg failed_concat_file = 3;
}

message BatchDeleteFileRequestProto {
  repeated string srcs = 1;
  repeated uint64 fileid = 3;

  // even if some files do not exist, it still executes delete
  optional bool force = 2;
}

// exception if any inode not found
message BatchDeleteFileResponseProto {
  required bool result = 1;
}

message BatchGetFileRequestProto {
  repeated string srcs = 1;

  optional bool needLocation = 2 [default = false];
  optional bool updateTtlAtime = 3 [default = false];

  // rdmaTag is only used when needLocation is true.
  // format: [vpc|rdma]-<cluster-id>
  optional string rdmaTag = 100;
}

// exception if any inode not found
message BatchGetFileResponseProto {
  // if file not found, ignore it
  repeated HdfsFileStatusProto files = 1;

  // each file's original index in request
  repeated uint32 original_index = 2;
}

service ClientNamenodeProtocol {
  rpc getBlockLocations(GetBlockLocationsRequestProto)
      returns(GetBlockLocationsResponseProto);
  rpc getServerDefaults(GetServerDefaultsRequestProto)
      returns(GetServerDefaultsResponseProto);
  rpc create(CreateRequestProto)returns(CreateResponseProto);
  rpc append(AppendRequestProto) returns(AppendResponseProto);
  rpc setReplication(SetReplicationRequestProto)
      returns(SetReplicationResponseProto);
  rpc setReplicationAttrOnly(SetReplicationRequestProto)
      returns(SetReplicationResponseProto);
  rpc setStoragePolicy(SetStoragePolicyRequestProto)
      returns(SetStoragePolicyResponseProto);
  rpc getStoragePolicies(GetStoragePoliciesRequestProto)
      returns(GetStoragePoliciesResponseProto);
  rpc setPermission(SetPermissionRequestProto)
      returns(SetPermissionResponseProto);
  rpc setOwner(SetOwnerRequestProto) returns(SetOwnerResponseProto);
  rpc abandonBlock(AbandonBlockRequestProto) returns(AbandonBlockResponseProto);
  rpc addBlock(AddBlockRequestProto) returns(AddBlockResponseProto);
  rpc commitLastBlock(CommitLastBlockRequestProto)
      returns(CommitLastBlockResponseProto);
  rpc getAdditionalDatanode(GetAdditionalDatanodeRequestProto)
      returns(GetAdditionalDatanodeResponseProto);
  rpc complete(CompleteRequestProto) returns(CompleteResponseProto);
  rpc reportBadBlocks(ReportBadBlocksRequestProto)
      returns(ReportBadBlocksResponseProto);
  rpc concat(ConcatRequestProto) returns(ConcatResponseProto);
  rpc truncate(TruncateRequestProto) returns(TruncateResponseProto);
  rpc rename(RenameRequestProto) returns(RenameResponseProto);
  rpc rename2(Rename2RequestProto) returns(Rename2ResponseProto);
  rpc Delete(DeleteRequestProto) returns(DeleteResponseProto);
  rpc mkdirs(MkdirsRequestProto) returns(MkdirsResponseProto);
  rpc mkdirExtended(MkdirExtendedRequestProto) returns(MkdirExtendedResponseProto);
  rpc getListing(GetListingRequestProto) returns(GetListingResponseProto);
  rpc renewLease(RenewLeaseRequestProto) returns(RenewLeaseResponseProto);
  rpc recoverLease(RecoverLeaseRequestProto)
      returns(RecoverLeaseResponseProto);
  rpc checkLease(CheckLeaseRequestProto) returns(CheckLeaseResponseProto);
  rpc getFsStats(GetFsStatusRequestProto) returns(GetFsStatsResponseProto);
  rpc getDatanodeReport(GetDatanodeReportRequestProto)
      returns(GetDatanodeReportResponseProto);
  rpc getDatanodeStorageReport(GetDatanodeStorageReportRequestProto)
      returns(GetDatanodeStorageReportResponseProto);
  rpc getPreferredBlockSize(GetPreferredBlockSizeRequestProto)
      returns(GetPreferredBlockSizeResponseProto);
  rpc setSafeMode(SetSafeModeRequestProto)
      returns(SetSafeModeResponseProto);
  rpc saveNamespace(SaveNamespaceRequestProto)
      returns(SaveNamespaceResponseProto);
  rpc rollEdits(RollEditsRequestProto)
      returns(RollEditsResponseProto);
  rpc restoreFailedStorage(RestoreFailedStorageRequestProto)
      returns(RestoreFailedStorageResponseProto);
  rpc refreshNodes(RefreshNodesRequestProto) returns(RefreshNodesResponseProto);
  rpc finalizeUpgrade(FinalizeUpgradeRequestProto)
      returns(FinalizeUpgradeResponseProto);
  rpc rollingUpgrade(RollingUpgradeRequestProto)
      returns(RollingUpgradeResponseProto);
  rpc listCorruptFileBlocks(ListCorruptFileBlocksRequestProto)
      returns(ListCorruptFileBlocksResponseProto);
  rpc metaSave(MetaSaveRequestProto) returns(MetaSaveResponseProto);
  rpc getFileInfo(GetFileInfoRequestProto) returns(GetFileInfoResponseProto);
  rpc addCacheDirective(AddCacheDirectiveRequestProto)
      returns (AddCacheDirectiveResponseProto);
  rpc modifyCacheDirective(ModifyCacheDirectiveRequestProto)
      returns (ModifyCacheDirectiveResponseProto);
  rpc removeCacheDirective(RemoveCacheDirectiveRequestProto)
      returns (RemoveCacheDirectiveResponseProto);
  rpc listCacheDirectives(ListCacheDirectivesRequestProto)
      returns (ListCacheDirectivesResponseProto);
  rpc addCachePool(AddCachePoolRequestProto)
      returns(AddCachePoolResponseProto);
  rpc modifyCachePool(ModifyCachePoolRequestProto)
      returns(ModifyCachePoolResponseProto);
  rpc removeCachePool(RemoveCachePoolRequestProto)
      returns(RemoveCachePoolResponseProto);
  rpc listCachePools(ListCachePoolsRequestProto)
      returns(ListCachePoolsResponseProto);
  rpc getFileLinkInfo(GetFileLinkInfoRequestProto)
      returns(GetFileLinkInfoResponseProto);
  rpc getContentSummary(GetContentSummaryRequestProto)
      returns(GetContentSummaryResponseProto);
  rpc setQuota(SetQuotaRequestProto) returns(SetQuotaResponseProto);
  rpc fsync(FsyncRequestProto) returns(FsyncResponseProto);
  rpc setTimes(SetTimesRequestProto) returns(SetTimesResponseProto);
  rpc createSymlink(CreateSymlinkRequestProto)
      returns(CreateSymlinkResponseProto);
  rpc getLinkTarget(GetLinkTargetRequestProto)
      returns(GetLinkTargetResponseProto);
  rpc updateBlockForPipeline(UpdateBlockForPipelineRequestProto)
      returns(UpdateBlockForPipelineResponseProto);
  rpc updatePipeline(UpdatePipelineRequestProto)
      returns(UpdatePipelineResponseProto);
  rpc getDelegationToken(GetDelegationTokenRequestProto)
      returns(GetDelegationTokenResponseProto);
  rpc renewDelegationToken(RenewDelegationTokenRequestProto)
      returns(RenewDelegationTokenResponseProto);
  rpc cancelDelegationToken(CancelDelegationTokenRequestProto)
      returns(CancelDelegationTokenResponseProto);
  rpc setBalancerBandwidth(SetBalancerBandwidthRequestProto)
      returns(SetBalancerBandwidthResponseProto);
  rpc getDataEncryptionKey(GetDataEncryptionKeyRequestProto)
      returns(GetDataEncryptionKeyResponseProto);
  rpc createSnapshot(CreateSnapshotRequestProto)
      returns(CreateSnapshotResponseProto);
  rpc renameSnapshot(RenameSnapshotRequestProto)
      returns(RenameSnapshotResponseProto);
  rpc allowSnapshot(AllowSnapshotRequestProto)
      returns(AllowSnapshotResponseProto);
  rpc disallowSnapshot(DisallowSnapshotRequestProto)
      returns(DisallowSnapshotResponseProto);
  rpc getSnapshottableDirListing(GetSnapshottableDirListingRequestProto)
      returns(GetSnapshottableDirListingResponseProto);
  rpc deleteSnapshot(DeleteSnapshotRequestProto)
      returns(DeleteSnapshotResponseProto);
  rpc getSnapshotDiffReport(GetSnapshotDiffReportRequestProto)
      returns(GetSnapshotDiffReportResponseProto);
  rpc isFileClosed(IsFileClosedRequestProto)
      returns(IsFileClosedResponseProto);
  rpc modifyAclEntries(ModifyAclEntriesRequestProto)
      returns(ModifyAclEntriesResponseProto);
  rpc removeAclEntries(RemoveAclEntriesRequestProto)
      returns(RemoveAclEntriesResponseProto);
  rpc removeDefaultAcl(RemoveDefaultAclRequestProto)
      returns(RemoveDefaultAclResponseProto);
  rpc removeAcl(RemoveAclRequestProto)
      returns(RemoveAclResponseProto);
  rpc setAcl(SetAclRequestProto)
      returns(SetAclResponseProto);
  rpc getAclStatus(GetAclStatusRequestProto)
      returns(GetAclStatusResponseProto);
  rpc setXAttr(SetXAttrRequestProto)
      returns(SetXAttrResponseProto);
  rpc getXAttrs(GetXAttrsRequestProto)
      returns(GetXAttrsResponseProto);
  rpc listXAttrs(ListXAttrsRequestProto)
      returns(ListXAttrsResponseProto);
  rpc removeXAttr(RemoveXAttrRequestProto)
      returns(RemoveXAttrResponseProto);
  rpc checkACPAccessWithInfSecIdentifier(CheckACPAccessWithInfSecIdentifierRequestProto)
      returns(CheckACPAccessWithInfSecIdentifierResponseProto);
  rpc checkACPAccess(CheckACPAccessRequestProto)
      returns(CheckACPAccessResponseProto);
  rpc checkAccess(CheckAccessRequestProto)
      returns(CheckAccessResponseProto);
  rpc createEncryptionZone(CreateEncryptionZoneRequestProto)
      returns(CreateEncryptionZoneResponseProto);
  rpc listEncryptionZones(ListEncryptionZonesRequestProto)
      returns(ListEncryptionZonesResponseProto);
  rpc getEZForPath(GetEZForPathRequestProto)
      returns(GetEZForPathResponseProto);
  rpc getCurrentEditLogTxid(GetCurrentEditLogTxidRequestProto)
      returns(GetCurrentEditLogTxidResponseProto);
  rpc getEditsFromTxid(GetEditsFromTxidRequestProto)
      returns(GetEditsFromTxidResponseProto);
  rpc increaseAccessCounter(IncreaseAccessCounterRequestProto)
      returns(IncreaseAccessCounterResponseProto);
  rpc getAccessCounterValues(GetAccessCounterValuesRequestProto)
      returns(GetAccessCounterValuesResponseProto);

  // Deprecated: remove API
  rpc setReplicaPolicy(SetReplicaPolicyRequestProto)
      returns(SetReplicaPolicyResponseProto);
  // Deprecated: remove API
  rpc getReplicaPolicy(GetReplicaPolicyRequestProto)
      returns(GetReplicaPolicyResponseProto);
  // Deprecated: remove API
  rpc getDistributed(GetDistributedRequestProto)
      returns(GetDistributedResponseProto);
  // Deprecated: remove API
  rpc setReadPolicy(SetReadPolicyRequestProto)
      returns(SetReadPolicyResponseProto);
  // Deprecated: remove API
  rpc getReadPolicy(GetReadPolicyRequestProto)
      returns(GetReadPolicyResponseProto);
  // Deprecated: remove API
  rpc listReadPolicies(ListReadPoliciesRequestProto)
      returns(ListReadPoliciesResponseProto);

  rpc setDirPolicy(SetDirPolicyRequestProto)
      returns(SetDirPolicyResponseProto);
  rpc removeDirPolicy(RemoveDirPolicyRequestProto)
      returns(RemoveDirPolicyResponseProto);
  rpc getDirPolicy(GetDirPolicyRequestProto)
      returns(GetDirPolicyResponseProto);
  rpc listDirPolicy(ListDirPolicyRequestProto)
      returns(ListDirPolicyResponseProto);
  rpc listDirPolicyV2(ListDirPolicyV2RequestProto) returns(ListDirPolicyV2ResponseProto);

  rpc setSyncPolicy(SetSyncPolicyRequestProto)
      returns(SetSyncPolicyResponseProto);
  rpc listSyncPolicy(ListSyncPolicyRequestProto)
      returns(ListSyncPolicyResponseProto);
  rpc listSyncPolicyV2(ListSyncPolicyV2RequestProto) returns(ListSyncPolicyV2ResponseProto);

  rpc addCompleteBlocksAndCloseFile(AddCompleteBlocksAndCloseFileRequestProto)
      returns(AddCompleteBlocksAndCloseFileResponseProto);
  rpc msync(MsyncRequestProto)
      returns(MsyncResponseProto);
  rpc getHAServiceState(HAServiceStateRequestProto)
      returns(HAServiceStateResponseProto);
  rpc getHyperBlockLocations(GetHyperBlockLocationsRequestProto)
      returns(GetHyperBlockLocationsResponseProto);
  rpc getBlockKeys(GetBlockKeysRequestProto)
      returns(GetBlockKeysResponseProto);
  rpc getINodeStat(GetINodeStatRequestProto)
      returns(GetINodeStatResponseProto);
  rpc setLifecyclePolicy(SetLifecyclePolicyRequestProto)
      returns(SetLifecyclePolicyResponseProto);
  rpc unsetLifecyclePolicy(UnsetLifecyclePolicyRequestProto)
      returns(UnsetLifecyclePolicyResponseProto);
  rpc getLifecyclePolicy(GetLifecyclePolicyRequestProto)
      returns(GetLifecyclePolicyResponseProto);
  rpc batchAcquireQuota(BatchAcquireQuotaRequest)
      returns(BatchAcquireQuotaResponse);
  rpc load(LoadRequestProto)
      returns(LoadResponseProto);
  rpc free(FreeRequestProto)
      returns(FreeResponseProto);
  rpc pin(PinRequestProto)
      returns(PinResponseProto);
  rpc lookupJob(LookupJobRequestProto)
      returns(LookupJobResponseProto);
  rpc cancelJob(CancelJobRequestProto)
      returns(CancelJobResponseProto);

  rpc batchCreateFile(BatchCreateFileRequestProto)
      returns(BatchCreateFileResponseProto);
  rpc batchCompleteFile(BatchCompleteFileRequestProto)
      returns(BatchCompleteFileResponseProto);
  rpc batchDeleteFile(BatchDeleteFileRequestProto)
      returns(BatchDeleteFileResponseProto);
  rpc batchGetFile(BatchGetFileRequestProto)
    returns(BatchGetFileResponseProto);

  rpc clientMetrics(ClientMetricsRequestProto)
    returns(ClientMetricsResponseProto);
}
