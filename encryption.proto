/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/**
 * These .proto interfaces are private and stable.
 * Please see http://wiki.apache.org/hadoop/Compatibility
 * for what changes are allowed for a *stable* .proto interface.
 */

// This file contains protocol buffers that are used throughout HDFS -- i.e.
// by the client, server, and data transfer protocols.
syntax = "proto2";

option java_package = "com.volcengine.cloudfs.proto";
option java_outer_classname = "EncryptionZonesProtos";
option java_generate_equals_and_hash = true;
option cc_generic_services = true;
package cloudfs;

import "hdfs.proto";

message CreateEncryptionZoneRequestProto {
  required string src = 1;
  optional string keyName = 2;
}

message CreateEncryptionZoneResponseProto {
}

message ListEncryptionZonesRequestProto {
  required int64 id = 1;
}

message EncryptionZoneProto {
  required int64 id = 1;
  required string path = 2;
  required CipherSuiteProto suite = 3;
  required CryptoProtocolVersionProto cryptoProtocolVersion = 4;
  required string keyName = 5;
}

message ListEncryptionZonesResponseProto {
  repeated EncryptionZoneProto zones = 1;
  required bool hasMore = 2;
}

message GetEZForPathRequestProto {
    required string src = 1;
}

message GetEZForPathResponseProto {
    optional EncryptionZoneProto zone = 1;
}

